{"last_node_id": 17, "last_link_id": 23, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [845, 172], "size": [315, 474], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 18}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7, 10]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [108744418779578, "randomize", 12, 8, "dpmpp_sde", "normal", 1, ""]}, {"id": 5, "type": "EmptyLatentImage", "pos": [435, 600], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [768, 768, 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [374, 171], "size": [422.8500061035156, 164.30999755859375], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 19}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4, 12]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["masterpiece HDR victorian portrait painting of woman, blonde hair, mountain nature, blue sky\n"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [377, 381], "size": [425.2799987792969, 180.61000061035156], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 20}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6, 13]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["bad hands, text, watermark\n"]}, {"id": 8, "type": "VAEDecode", "pos": [1240, 600], "size": [210, 46], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 21}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1496, 600], "size": [232.94000244140625, 282.42999267578125], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI", ""]}, {"id": 10, "type": "LatentUpscale", "pos": [1238, 170], "size": [315, 130], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 10}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [14]}], "properties": {"Node name for S&R": "LatentUpscale"}, "widgets_values": ["nearest-exact", 1152, 1152, "disabled"]}, {"id": 11, "type": "K<PERSON><PERSON><PERSON>", "pos": [1585, 114], "size": [315, 474], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 23}, {"name": "positive", "type": "CONDITIONING", "link": 12}, {"name": "negative", "type": "CONDITIONING", "link": 13}, {"name": "latent_image", "type": "LATENT", "link": 14}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [15]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [885500652260966, "randomize", 14, 8, "dpmpp_2m", "simple", 0.5, ""]}, {"id": 12, "type": "SaveImage", "pos": [2096, 232], "size": [407.5400085449219, 468.1300048828125], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 17}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI", ""]}, {"id": 13, "type": "VAEDecode", "pos": [1976, 128], "size": [210, 46], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 15}, {"name": "vae", "type": "VAE", "link": 22}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [17]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 16, "type": "CheckpointLoaderSimple", "pos": [24, 315], "size": [315, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [18, 23]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [19, 20]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [21, 22]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "v2-1_768-ema-pruned.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-2-1/resolve/main/v2-1_768-ema-pruned.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["v2-1_768-ema-pruned.safetensors"]}, {"id": 17, "type": "<PERSON>downNote", "pos": [0, 780], "size": [328, 128], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [Hires Fix - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/2_pass_txt2img/) — Overview\n> \n> [ComfyUI Image Upscale - docs.comfy.org](https://docs.comfy.org/tutorials/basic/upscale) — Upscaling step-by-step tutorial"], "color": "#432", "bgcolor": "#653"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [9, 8, 0, 9, 0, "IMAGE"], [10, 3, 0, 10, 0, "LATENT"], [12, 6, 0, 11, 1, "CONDITIONING"], [13, 7, 0, 11, 2, "CONDITIONING"], [14, 10, 0, 11, 3, "LATENT"], [15, 11, 0, 13, 0, "LATENT"], [17, 13, 0, 12, 0, "IMAGE"], [18, 16, 0, 3, 0, "MODEL"], [19, 16, 1, 6, 0, "CLIP"], [20, 16, 1, 7, 0, "CLIP"], [21, 16, 2, 8, 1, "VAE"], [22, 16, 2, 13, 1, "VAE"], [23, 16, 0, 11, 0, "MODEL"]], "groups": [{"id": 1, "title": "Txt2Img", "bounding": [0, 32, 1211, 708], "color": "#a1309b", "font_size": 24, "flags": {}}, {"id": 2, "title": "Save Intermediate Image", "bounding": [1232, 520, 576, 392], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "<PERSON><PERSON>", "bounding": [1232, 32, 710, 464], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"id": 4, "title": "Save Final Image", "bounding": [1968, 32, 664, 744], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {}, "version": 0.4}