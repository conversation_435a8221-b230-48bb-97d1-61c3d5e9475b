{"version": 3, "file": "KeybindingPanel-BOTB3HPy.js", "sources": ["../../src/components/dialog/content/setting/keybinding/KeyComboDisplay.vue", "../../src/components/dialog/content/setting/KeybindingPanel.vue"], "sourcesContent": ["<template>\n  <span>\n    <template v-for=\"(sequence, index) in keySequences\" :key=\"index\">\n      <Tag :severity=\"isModified ? 'info' : 'secondary'\">\n        {{ sequence }}\n      </Tag>\n      <span v-if=\"index < keySequences.length - 1\" class=\"px-2\">+</span>\n    </template>\n  </span>\n</template>\n\n<script setup lang=\"ts\">\nimport Tag from 'primevue/tag'\nimport { computed } from 'vue'\n\nimport { KeyComboImpl } from '@/stores/keybindingStore'\n\nconst { keyCombo, isModified = false } = defineProps<{\n  keyCombo: KeyComboImpl\n  isModified?: boolean\n}>()\n\nconst keySequences = computed(() => keyCombo.getKeySequences())\n</script>\n", "<template>\n  <PanelTemplate value=\"Keybinding\" class=\"keybinding-panel\">\n    <template #header>\n      <SearchBox\n        v-model=\"filters['global'].value\"\n        :placeholder=\"$t('g.searchKeybindings') + '...'\"\n      />\n    </template>\n\n    <DataTable\n      v-model:selection=\"selectedCommandData\"\n      :value=\"commandsData\"\n      :global-filter-fields=\"['id', 'label']\"\n      :filters=\"filters\"\n      selection-mode=\"single\"\n      striped-rows\n      :pt=\"{\n        header: 'px-0'\n      }\"\n      @row-dblclick=\"editKeybinding($event.data)\"\n    >\n      <Column field=\"actions\" header=\"\">\n        <template #body=\"slotProps\">\n          <div class=\"actions invisible flex flex-row\">\n            <Button\n              icon=\"pi pi-pencil\"\n              class=\"p-button-text\"\n              @click=\"editKeybinding(slotProps.data)\"\n            />\n            <Button\n              icon=\"pi pi-replay\"\n              class=\"p-button-text p-button-warn\"\n              :disabled=\"\n                !keybindingStore.isCommandKeybindingModified(slotProps.data.id)\n              \"\n              @click=\"resetKeybinding(slotProps.data)\"\n            />\n            <Button\n              icon=\"pi pi-trash\"\n              class=\"p-button-text p-button-danger\"\n              :disabled=\"!slotProps.data.keybinding\"\n              @click=\"removeKeybinding(slotProps.data)\"\n            />\n          </div>\n        </template>\n      </Column>\n      <Column\n        field=\"id\"\n        :header=\"$t('g.command')\"\n        sortable\n        class=\"max-w-64 2xl:max-w-full\"\n      >\n        <template #body=\"slotProps\">\n          <div\n            class=\"overflow-hidden text-ellipsis whitespace-nowrap\"\n            :title=\"slotProps.data.id\"\n          >\n            {{ slotProps.data.label }}\n          </div>\n        </template>\n      </Column>\n      <Column field=\"keybinding\" :header=\"$t('g.keybinding')\">\n        <template #body=\"slotProps\">\n          <KeyComboDisplay\n            v-if=\"slotProps.data.keybinding\"\n            :key-combo=\"slotProps.data.keybinding.combo\"\n            :is-modified=\"\n              keybindingStore.isCommandKeybindingModified(slotProps.data.id)\n            \"\n          />\n          <span v-else>-</span>\n        </template>\n      </Column>\n      <Column field=\"source\" :header=\"$t('g.source')\">\n        <template #body=\"slotProps\">\n          <span class=\"overflow-hidden text-ellipsis\">{{\n            slotProps.data.source || '-'\n          }}</span>\n        </template>\n      </Column>\n    </DataTable>\n\n    <Dialog\n      v-model:visible=\"editDialogVisible\"\n      class=\"min-w-96\"\n      modal\n      :header=\"currentEditingCommand?.label\"\n      @hide=\"cancelEdit\"\n    >\n      <div>\n        <InputText\n          ref=\"keybindingInput\"\n          class=\"mb-2 text-center\"\n          :model-value=\"newBindingKeyCombo?.toString() ?? ''\"\n          placeholder=\"Press keys for new binding\"\n          autocomplete=\"off\"\n          fluid\n          @keydown.stop.prevent=\"captureKeybinding\"\n        />\n        <Message v-if=\"existingKeybindingOnCombo\" severity=\"warn\">\n          Keybinding already exists on\n          <Tag\n            severity=\"secondary\"\n            :value=\"existingKeybindingOnCombo.commandId\"\n          />\n        </Message>\n      </div>\n      <template #footer>\n        <Button\n          :label=\"existingKeybindingOnCombo ? 'Overwrite' : 'Save'\"\n          :icon=\"existingKeybindingOnCombo ? 'pi pi-pencil' : 'pi pi-check'\"\n          :severity=\"existingKeybindingOnCombo ? 'warn' : undefined\"\n          autofocus\n          @click=\"saveKeybinding\"\n        />\n      </template>\n    </Dialog>\n    <Button\n      v-tooltip=\"$t('g.resetAllKeybindingsTooltip')\"\n      class=\"mt-4\"\n      :label=\"$t('g.resetAll')\"\n      icon=\"pi pi-replay\"\n      severity=\"danger\"\n      fluid\n      text\n      @click=\"resetAllKeybindings\"\n    />\n  </PanelTemplate>\n</template>\n\n<script setup lang=\"ts\">\nimport { FilterMatchMode } from '@primevue/core/api'\nimport Button from 'primevue/button'\nimport Column from 'primevue/column'\nimport DataTable from 'primevue/datatable'\nimport Dialog from 'primevue/dialog'\nimport InputText from 'primevue/inputtext'\nimport Message from 'primevue/message'\nimport Tag from 'primevue/tag'\nimport { useToast } from 'primevue/usetoast'\nimport { computed, ref, watchEffect } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport SearchBox from '@/components/common/SearchBox.vue'\nimport { useKeybindingService } from '@/services/keybindingService'\nimport { useCommandStore } from '@/stores/commandStore'\nimport {\n  KeyComboImpl,\n  KeybindingImpl,\n  useKeybindingStore\n} from '@/stores/keybindingStore'\nimport { normalizeI18nKey } from '@/utils/formatUtil'\n\nimport PanelTemplate from './PanelTemplate.vue'\nimport KeyComboDisplay from './keybinding/KeyComboDisplay.vue'\n\nconst filters = ref({\n  global: { value: '', matchMode: FilterMatchMode.CONTAINS }\n})\n\nconst keybindingStore = useKeybindingStore()\nconst keybindingService = useKeybindingService()\nconst commandStore = useCommandStore()\nconst { t } = useI18n()\n\ninterface ICommandData {\n  id: string\n  keybinding: KeybindingImpl | null\n  label: string\n  source?: string\n}\n\nconst commandsData = computed<ICommandData[]>(() => {\n  return Object.values(commandStore.commands).map((command) => ({\n    id: command.id,\n    label: t(\n      `commands.${normalizeI18nKey(command.id)}.label`,\n      command.label ?? ''\n    ),\n    keybinding: keybindingStore.getKeybindingByCommandId(command.id),\n    source: command.source\n  }))\n})\n\nconst selectedCommandData = ref<ICommandData | null>(null)\nconst editDialogVisible = ref(false)\nconst newBindingKeyCombo = ref<KeyComboImpl | null>(null)\nconst currentEditingCommand = ref<ICommandData | null>(null)\nconst keybindingInput = ref<InstanceType<typeof InputText> | null>(null)\n\nconst existingKeybindingOnCombo = computed<KeybindingImpl | null>(() => {\n  if (!currentEditingCommand.value) {\n    return null\n  }\n\n  // If the new keybinding is the same as the current editing command, then don't show the error\n  if (\n    currentEditingCommand.value.keybinding?.combo?.equals(\n      newBindingKeyCombo.value\n    )\n  ) {\n    return null\n  }\n\n  if (!newBindingKeyCombo.value) {\n    return null\n  }\n\n  return keybindingStore.getKeybinding(newBindingKeyCombo.value)\n})\n\nfunction editKeybinding(commandData: ICommandData) {\n  currentEditingCommand.value = commandData\n  newBindingKeyCombo.value = commandData.keybinding\n    ? commandData.keybinding.combo\n    : null\n  editDialogVisible.value = true\n}\n\nwatchEffect(() => {\n  if (editDialogVisible.value) {\n    // nextTick doesn't work here, so we use a timeout instead\n    setTimeout(() => {\n      // @ts-expect-error - $el is an internal property of the InputText component\n      keybindingInput.value?.$el?.focus()\n    }, 300)\n  }\n})\n\nasync function removeKeybinding(commandData: ICommandData) {\n  if (commandData.keybinding) {\n    keybindingStore.unsetKeybinding(commandData.keybinding)\n    await keybindingService.persistUserKeybindings()\n  }\n}\n\nasync function captureKeybinding(event: KeyboardEvent) {\n  // Allow the use of keyboard shortcuts when adding keyboard shortcuts\n  if (!event.shiftKey && !event.altKey && !event.ctrlKey && !event.metaKey) {\n    switch (event.key) {\n      case 'Escape':\n        cancelEdit()\n        return\n      case 'Enter':\n        await saveKeybinding()\n        return\n    }\n  }\n  const keyCombo = KeyComboImpl.fromEvent(event)\n  newBindingKeyCombo.value = keyCombo\n}\n\nfunction cancelEdit() {\n  editDialogVisible.value = false\n  currentEditingCommand.value = null\n  newBindingKeyCombo.value = null\n}\n\nasync function saveKeybinding() {\n  if (currentEditingCommand.value && newBindingKeyCombo.value) {\n    const updated = keybindingStore.updateKeybindingOnCommand(\n      new KeybindingImpl({\n        commandId: currentEditingCommand.value.id,\n        combo: newBindingKeyCombo.value\n      })\n    )\n    if (updated) {\n      await keybindingService.persistUserKeybindings()\n    }\n  }\n  cancelEdit()\n}\n\nasync function resetKeybinding(commandData: ICommandData) {\n  if (keybindingStore.resetKeybindingForCommand(commandData.id)) {\n    await keybindingService.persistUserKeybindings()\n  } else {\n    console.warn(\n      `No changes made when resetting keybinding for command: ${commandData.id}`\n    )\n  }\n}\n\nconst toast = useToast()\nasync function resetAllKeybindings() {\n  keybindingStore.resetAllKeybindings()\n  await keybindingService.persistUserKeybindings()\n  toast.add({\n    severity: 'info',\n    summary: 'Info',\n    detail: 'All keybindings reset',\n    life: 3000\n  })\n}\n</script>\n\n<style scoped>\n:deep(.p-datatable-tbody) > tr > td {\n  @apply p-1;\n  min-height: 2rem;\n}\n\n:deep(.p-datatable-row-selected) .actions,\n:deep(.p-datatable-selectable-row:hover) .actions {\n  @apply visible;\n}\n</style>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAsBA,UAAM,eAAe,SAAS,MAAM,QAAA,SAAS,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsI9D,UAAM,UAAU,IAAI;AAAA,MAClB,QAAQ,EAAE,OAAO,IAAI,WAAW,gBAAgB,SAAS;AAAA,IAAA,CAC1D;AAED,UAAM,kBAAkB;AACxB,UAAM,oBAAoB;AAC1B,UAAM,eAAe;AACf,UAAA,EAAE,MAAM;AASR,UAAA,eAAe,SAAyB,MAAM;AAClD,aAAO,OAAO,OAAO,aAAa,QAAQ,EAAE,IAAI,CAAC,aAAa;AAAA,QAC5D,IAAI,QAAQ;AAAA,QACZ,OAAO;AAAA,UACL,YAAY,iBAAiB,QAAQ,EAAE,CAAC;AAAA,UACxC,QAAQ,SAAS;AAAA,QACnB;AAAA,QACA,YAAY,gBAAgB,yBAAyB,QAAQ,EAAE;AAAA,QAC/D,QAAQ,QAAQ;AAAA,MAChB,EAAA;AAAA,IAAA,CACH;AAEK,UAAA,sBAAsB,IAAyB,IAAI;AACnD,UAAA,oBAAoB,IAAI,KAAK;AAC7B,UAAA,qBAAqB,IAAyB,IAAI;AAClD,UAAA,wBAAwB,IAAyB,IAAI;AACrD,UAAA,kBAAkB,IAA2C,IAAI;AAEjE,UAAA,4BAA4B,SAAgC,MAAM;AAClE,UAAA,CAAC,sBAAsB,OAAO;AACzB,eAAA;AAAA,MACT;AAIE,UAAA,sBAAsB,MAAM,YAAY,OAAO;AAAA,QAC7C,mBAAmB;AAAA,MAAA,GAErB;AACO,eAAA;AAAA,MACT;AAEI,UAAA,CAAC,mBAAmB,OAAO;AACtB,eAAA;AAAA,MACT;AAEO,aAAA,gBAAgB,cAAc,mBAAmB,KAAK;AAAA,IAAA,CAC9D;AAED,aAAS,eAAe,aAA2B;AACjD,4BAAsB,QAAQ;AAC9B,yBAAmB,QAAQ,YAAY,aACnC,YAAY,WAAW,QACvB;AACJ,wBAAkB,QAAQ;AAAA,IAC5B;AANS;AAQT,gBAAY,MAAM;AAChB,UAAI,kBAAkB,OAAO;AAE3B,mBAAW,MAAM;AAEC,0BAAA,OAAO,KAAK;WAC3B,GAAG;AAAA,MACR;AAAA,IAAA,CACD;AAED,mBAAe,iBAAiB,aAA2B;AACzD,UAAI,YAAY,YAAY;AACV,wBAAA,gBAAgB,YAAY,UAAU;AACtD,cAAM,kBAAkB;MAC1B;AAAA,IACF;AALe;AAOf,mBAAe,kBAAkB,OAAsB;AAEjD,UAAA,CAAC,MAAM,YAAY,CAAC,MAAM,UAAU,CAAC,MAAM,WAAW,CAAC,MAAM,SAAS;AACxE,gBAAQ,MAAM,KAAK;AAAA,UACjB,KAAK;AACQ;AACX;AAAA,UACF,KAAK;AACH,kBAAM,eAAe;AACrB;AAAA,QACJ;AAAA,MACF;AACM,YAAA,WAAW,aAAa,UAAU,KAAK;AAC7C,yBAAmB,QAAQ;AAAA,IAC7B;AAde;AAgBf,aAAS,aAAa;AACpB,wBAAkB,QAAQ;AAC1B,4BAAsB,QAAQ;AAC9B,yBAAmB,QAAQ;AAAA,IAC7B;AAJS;AAMT,mBAAe,iBAAiB;AAC1B,UAAA,sBAAsB,SAAS,mBAAmB,OAAO;AAC3D,cAAM,UAAU,gBAAgB;AAAA,UAC9B,IAAI,eAAe;AAAA,YACjB,WAAW,sBAAsB,MAAM;AAAA,YACvC,OAAO,mBAAmB;AAAA,UAAA,CAC3B;AAAA,QAAA;AAEH,YAAI,SAAS;AACX,gBAAM,kBAAkB;QAC1B;AAAA,MACF;AACW;IACb;AAbe;AAef,mBAAe,gBAAgB,aAA2B;AACxD,UAAI,gBAAgB,0BAA0B,YAAY,EAAE,GAAG;AAC7D,cAAM,kBAAkB;MAAuB,OAC1C;AACG,gBAAA;AAAA,UACN,0DAA0D,YAAY,EAAE;AAAA,QAAA;AAAA,MAE5E;AAAA,IACF;AARe;AAUf,UAAM,QAAQ;AACd,mBAAe,sBAAsB;AACnC,sBAAgB,oBAAoB;AACpC,YAAM,kBAAkB;AACxB,YAAM,IAAI;AAAA,QACR,UAAU;AAAA,QACV,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,MAAM;AAAA,MAAA,CACP;AAAA,IACH;AATe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}