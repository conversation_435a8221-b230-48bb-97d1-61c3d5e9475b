{"id": "96995b8f-85c5-47af-b3cf-7b6a24675694", "revision": 0, "last_node_id": 184, "last_link_id": 264, "nodes": [{"id": 68, "type": "CreateVideo", "pos": [1370, 50], "size": [270, 78], "flags": {"collapsed": false}, "order": 33, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 139}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [129]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CreateVideo"}, "widgets_values": [16]}, {"id": 70, "type": "SaveAnimatedWEBP", "pos": [2110, 50], "size": [670, 900], "flags": {}, "order": 32, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 130}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SaveAnimatedWEBP"}, "widgets_values": ["ComfyUI", 6, true, 80, "default"]}, {"id": 48, "type": "ModelSamplingSD3", "pos": [630, 50], "size": [315, 58], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 257}], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [95]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "ModelSamplingSD3"}, "widgets_values": [8.000000000000002]}, {"id": 158, "type": "UNETLoader", "pos": [-670, 50], "size": [350, 82], "flags": {}, "order": 0, "mode": 4, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [235]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "UNETLoader", "models": [{"name": "wan2.1_vace_1.3B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_vace_1.3B_fp16.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["wan2.1_vace_1.3B_fp16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 159, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-670, 170], "size": [350, 126], "flags": {}, "order": 17, "mode": 4, "inputs": [{"name": "model", "type": "MODEL", "link": 235}, {"name": "clip", "type": "CLIP", "link": 236}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": []}, {"name": "CLIP", "type": "CLIP", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "models": [{"name": "Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors", "url": "https://huggingface.co/Kijai/WanVideo_comfy/resolve/main/Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors", "directory": "loras"}]}, "widgets_values": ["Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors", 0.7000000000000002, 1], "color": "#322", "bgcolor": "#533"}, {"id": 49, "type": "WanVaceToVideo", "pos": [634.6180419921875, 153.07472229003906], "size": [315, 254], "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 96}, {"name": "negative", "type": "CONDITIONING", "link": 97}, {"name": "vae", "type": "VAE", "link": 260}, {"name": "control_video", "shape": 7, "type": "IMAGE", "link": 207}, {"name": "control_masks", "shape": 7, "type": "MASK", "link": 204}, {"name": "reference_image", "shape": 7, "type": "IMAGE", "link": null}, {"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 215}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 217}, {"name": "length", "type": "INT", "widget": {"name": "length"}, "link": 209}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [98]}, {"name": "negative", "type": "CONDITIONING", "links": [99]}, {"name": "latent", "type": "LATENT", "links": [160]}, {"name": "trim_latent", "type": "INT", "links": [115]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "WanVaceToVideo"}, "widgets_values": [720, 720, 45, 1, 1]}, {"id": 153, "type": "<PERSON>downNote", "pos": [646.3126220703125, 455.0546569824219], "size": [310, 110], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About Video Size", "properties": {}, "widgets_values": ["| Model                                                         | 480P | 720P |\n| ------------------------------------------------------------ | ---- | ---- |\n| [VACE-1.3B](https://huggingface.co/Wan-AI/Wan2.1-VACE-1.3B) | ✅   | ❌   |\n| [VACE-14B](https://huggingface.co/Wan-AI/Wan2.1-VACE-14B)   | ✅   | ✅   |"], "color": "#432", "bgcolor": "#653"}, {"id": 58, "type": "TrimVideoLatent", "pos": [1000.3111572265625, 359.673095703125], "size": [315, 60], "flags": {"collapsed": true}, "order": 30, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 116}, {"name": "trim_amount", "type": "INT", "widget": {"name": "trim_amount"}, "link": 115}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [117]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "TrimVideoLatent"}, "widgets_values": [0]}, {"id": 8, "type": "VAEDecode", "pos": [1172.2872314453125, 361.1716003417969], "size": [315, 46], "flags": {"collapsed": true}, "order": 31, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 117}, {"name": "vae", "type": "VAE", "link": 261}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [130, 139]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 136, "type": "Note", "pos": [1060, 690], "size": [240, 310], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Currently (May 18, 2025), the Comfy Core nodes lack relevant nodes for obtaining image size or video frames. Here, we can use the Preview image to indirectly obtain the image size and the total number of video frames.\n\nYou can use custom nodes such as image size acquisition to optimize the step of size modification.\n\n---\n\n目前（2025年5月18日），Comfy Core 节点缺少获取图像尺寸或数量的相关节点。这里我们可以使用 Preview image 来辅助获取到图片尺寸和视频总帧数。\n\n你可以使用图像尺寸获取之类的自定义节点，来优化尺寸修改这个步骤\n"], "color": "#432", "bgcolor": "#653"}, {"id": 111, "type": "MaskToImage", "pos": [330, 914], "size": [240, 26], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 180}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [201]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 129, "type": "RepeatImageBatch", "pos": [330, 802], "size": [240, 60], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 201}, {"name": "amount", "type": "INT", "widget": {"name": "amount"}, "link": 212}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [202]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "RepeatImageBatch"}, "widgets_values": [17]}, {"id": 130, "type": "ImageToMask", "pos": [330, 690], "size": [240, 60], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 202}], "outputs": [{"name": "MASK", "type": "MASK", "links": [204]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 71, "type": "LoadVideo", "pos": [-280, 690], "size": [274.080078125, 317.6267395019531], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [131]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "LoadVideo"}, "widgets_values": ["girl.mp4", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 139, "type": "PrimitiveInt", "pos": [100, 1260], "size": [210, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [217]}], "title": "Height", "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "PrimitiveInt"}, "widgets_values": [960, "fixed"], "color": "#322", "bgcolor": "#533"}, {"id": 161, "type": "CLIPLoader", "pos": [-260, 380], "size": [350, 106], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [236, 242]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPLoader", "models": [{"name": "umt5_xxl_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp16.safetensors", "directory": "text_encoders"}]}, "widgets_values": ["umt5_xxl_fp16.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 152, "type": "VAELoader", "pos": [-260, 520], "size": [350, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "slot_index": 0, "links": [260, 261]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAELoader", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors", "directory": "vae"}]}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 110, "type": "ImagePadForOutpaint", "pos": [20, 830], "size": [250, 174], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 205}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [207, 214]}, {"name": "MASK", "type": "MASK", "links": [180]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "ImagePadForOutpaint"}, "widgets_values": [120, 120, 120, 200, 0], "color": "#322", "bgcolor": "#533"}, {"id": 72, "type": "GetVideoComponents", "pos": [20, 690], "size": [250, 66], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 131}], "outputs": [{"name": "images", "type": "IMAGE", "links": [205, 218]}, {"name": "audio", "type": "AUDIO", "links": null}, {"name": "fps", "type": "FLOAT", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "GetVideoComponents"}, "widgets_values": []}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [992.5, 50], "size": [315, 262], "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 95}, {"name": "positive", "type": "CONDITIONING", "link": 98}, {"name": "negative", "type": "CONDITIONING", "link": 99}, {"name": "latent_image", "type": "LATENT", "link": 160}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [116]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [776767434080331, "randomize", 3, 1, "uni_pc", "simple", 1], "color": "#322", "bgcolor": "#533"}, {"id": 151, "type": "UNETLoader", "pos": [-260, 80], "size": [350, 82], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [241]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "UNETLoader", "models": [{"name": "wan2.1_vace_14B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_vace_14B_fp16.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["wan2.1_vace_14B_fp16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 183, "type": "<PERSON>downNote", "pos": [-1170, 0], "size": [470, 780], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tutorial](https://docs.comfy.org/tutorials/video/wan/vace) | [教程](https://docs.comfy.org/zh-CN/tutorials/video/wan/vace)\n\n[Causvid Lora extracted  by <PERSON><PERSON><PERSON>](https://www.reddit.com/r/StableDiffusion/comments/1knuafk/causvid_lora_massive_speedup_for_wan21_made_by/) Thanks to CausVid MIT\n\n##  14B Support 480P 720P\n\n**Diffusion Model**\n- [wan2.1_vace_14B_fp16.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_vace_14B_fp16.safetensors)\n\n**LoRA**\n- [Wan21_CausVid_14B_T2V_lora_rank32.safetensors](https://huggingface.co/Kijai/WanVideo_comfy/blob/main/Wan21_CausVid_14B_T2V_lora_rank32.safetensors)\n\nFYI: It takes about 40 minutes to run to completion at 81 frames per second in 720P resolution with the RTX 4090 when testing the workflow.  \nAfter using Wan21_CausVid_14B_T2V_lora_rank32.safetensors, it probably only takes about 4 minutes. \n\n## 1.3B Support 480P only\n\n**Diffusion Model**\n- [wan2.1_vace_1.3B_fp16.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_vace_14B_fp16.safetensors)\n\n**LoRA**\n- [Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors](https://huggingface.co/Kijai/WanVideo_comfy/blob/main/Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors)\n\nFYI: After using Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors, it probably only takes about 2 minutes. \n## Other Models\n\n* You may already have these models if you use Wan workflow before.\n\n**VAE**\n- [wan_2.1_vae.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true)\n\n**Text encoders**   Chose one of following model\n- [umt5_xxl_fp16.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp16.safetensors?download=true)\n- [umt5_xxl_fp8_e4m3fn_scaled.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true)\n\n> You can choose between fp16 of fp8; I used fp16 to match what kijai's wrapper is compatible with.\n\nFile save location\n\n```\nComfyUI/\n├── models/\n│   ├── diffusion_models/\n│   │   ├-── wan2.1_vace_14B_fp16.safetensors\n│   │   └─── wan2.1_vace_1.3B_fp16.safetensors \n│   ├── text_encoders/\n│   │   └─── umt5_xxl_fp8_e4m3fn_scaled.safetensors # or fp16\n│   ├── loras/\n│   │   ├── Wan21_CausVid_14B_T2V_lora_rank32.safetensors\n│   │   └── Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors\n│   └── vae/\n│       └──  wan_2.1_vae.safetensors\n```\n"], "color": "#432", "bgcolor": "#653"}, {"id": 156, "type": "<PERSON>downNote", "pos": [-680, 560], "size": [370, 220], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "title": "Note", "properties": {}, "widgets_values": ["The generation quality of the 14B model is better, but it takes longer to generate. If you have already downloaded the model, you can choose to directly use the nodes above, or just modify the model loaded by the loader node.\n\nThe corresponding LoRA should match the Diffusion Model. For example, the LoRA corresponding to the 14B diffusion model is the 14B LoRA. \n\n---\n\n14B 的生成质量更好，但是需要更长的时间去生成，如果你下载好了模型，你可以选择直接使用上面的节点，或者只是修改 loader 节点加载的模型。\n\n对应的 LoRA 应该是和 Diffusion Model匹配的，比如 14B 的 diffusion model 对应的是 14B 的 LoRA"], "color": "#432", "bgcolor": "#653"}, {"id": 160, "type": "CLIPLoader", "pos": [-680, 380], "size": [370, 106], "flags": {}, "order": 10, "mode": 4, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPLoader", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true", "directory": "text_encoders"}]}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 137, "type": "PrimitiveInt", "pos": [100, 1120], "size": [210, 82], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [215]}], "title": "<PERSON><PERSON><PERSON>", "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "PrimitiveInt"}, "widgets_values": [960, "fixed"], "color": "#322", "bgcolor": "#533"}, {"id": 142, "type": "Note", "pos": [330, 1120], "size": [250, 260], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Here, you need to manually set the size of the video after outpainting. Since the native nodes currently do not support the relevant functions, you can use some custom nodes to improve this step. Additionally, because the step size of the size setting for the WanVaceToVideo node is 16, the number you enter must be divisible by 16.    \n\n在这里你需要手动设置视频 outpainting 后尺寸，由于目前原生节点没有相关功能支持，你可以使用一些自定义节点来改进这个步骤，另外由于 WanVaceToVideo 节点设置了尺寸的步长是 16 所以你输入的数字要求必须能被 16 整除\n"], "color": "#432", "bgcolor": "#653"}, {"id": 154, "type": "<PERSON>downNote", "pos": [997.2053833007812, 402.0997619628906], "size": [303.90106201171875, 158.5415802001953], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [], "title": "<PERSON><PERSON><PERSON><PERSON>", "properties": {}, "widgets_values": ["## Default\n\n- steps:20\n- cfg:6.0\n\n## [For CausVid LoRA](https://www.reddit.com/r/StableDiffusion/comments/1knuafk/causvid_lora_massive_speedup_for_wan21_made_by/)\n\n- steps: 2-4\n- cfg: 1.0\n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 131, "type": "PrimitiveInt", "pos": [-260, 1180], "size": [310, 90], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [209, 212]}], "title": "Length", "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "PrimitiveInt"}, "widgets_values": [81, "fixed"], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "CLIPTextEncode", "pos": [160, 50], "size": [420, 280], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 258}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [96]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A portrait of a woman with her eyes closed, her head surrounded by white transparent glass flowers, with only her face exposed. The camera slowly moves in, and the girl smiles. There are other transparent glass flowers in the style of <PERSON><PERSON> around her. The detailed painting features natural light and soft lighting, creating a beautiful, surreal, and emotionally expressive movie scene.\n"], "color": "#232", "bgcolor": "#353"}, {"id": 7, "type": "CLIPTextEncode", "pos": [160, 380], "size": [420, 190], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 259}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [97]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["bad quality, blurry, messy, chaotic"], "color": "#223", "bgcolor": "#335"}, {"id": 143, "type": "PreviewImage", "pos": [630, 1070], "size": [390, 270], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 218}], "outputs": [], "title": "Preview Image - original", "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 135, "type": "PreviewImage", "pos": [630, 690], "size": [390, 310], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 214}], "outputs": [], "title": "Preview Image - Outpaint", "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 69, "type": "SaveVideo", "pos": [1370, 180], "size": [676.23095703125, 774.23095703125], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 129}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SaveVideo"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 184, "type": "<PERSON>downNote", "pos": [-290, -190], "size": [410, 140], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [], "title": "About CausVid LoRA", "properties": {}, "widgets_values": ["We have added CausVid LoRA by default to achieve acceleration. However, in some cases, the video may shake and become blurry. You might need to test different LoRA intensities to get the best results, which should be between 0.3 and 0.7. If you don't need it, you can use the bypass mode to disable it, and then restore the settings of `KSampler` to the default ones.\n\n\n我们默认添加了  CausVid LoRA 来实现加速，但有些情况下会出现视频抖动和模糊的情况，你可能需要测试不同的 LoRA 强度来获取最好的结果，0.3～0.7 之间。如果你不需要的话，可以使用 bypass 模式禁用它，然后恢复 `KSampler`的设置到默认的设置即可。"], "color": "#432", "bgcolor": "#653"}, {"id": 165, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-260, 200], "size": [350, 126], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 241}, {"name": "clip", "type": "CLIP", "link": 242}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [257]}, {"name": "CLIP", "type": "CLIP", "links": [258, 259]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "models": [{"name": "Wan21_CausVid_14B_T2V_lora_rank32.safetensors", "url": "https://huggingface.co/Kijai/WanVideo_comfy/resolve/main/Wan21_CausVid_14B_T2V_lora_rank32.safetensors", "directory": "loras"}]}, "widgets_values": ["Wan21_CausVid_14B_T2V_lora_rank32.safetensors", 0.4000000000000001, 1], "color": "#322", "bgcolor": "#533"}], "links": [[95, 48, 0, 3, 0, "MODEL"], [96, 6, 0, 49, 0, "CONDITIONING"], [97, 7, 0, 49, 1, "CONDITIONING"], [98, 49, 0, 3, 1, "CONDITIONING"], [99, 49, 1, 3, 2, "CONDITIONING"], [115, 49, 3, 58, 1, "INT"], [116, 3, 0, 58, 0, "LATENT"], [117, 58, 0, 8, 0, "LATENT"], [129, 68, 0, 69, 0, "VIDEO"], [130, 8, 0, 70, 0, "IMAGE"], [131, 71, 0, 72, 0, "VIDEO"], [139, 8, 0, 68, 0, "IMAGE"], [160, 49, 2, 3, 3, "LATENT"], [180, 110, 1, 111, 0, "MASK"], [201, 111, 0, 129, 0, "IMAGE"], [202, 129, 0, 130, 0, "IMAGE"], [204, 130, 0, 49, 4, "MASK"], [205, 72, 0, 110, 0, "IMAGE"], [207, 110, 0, 49, 3, "IMAGE"], [209, 131, 0, 49, 8, "INT"], [212, 131, 0, 129, 1, "INT"], [214, 110, 0, 135, 0, "IMAGE"], [215, 137, 0, 49, 6, "INT"], [217, 139, 0, 49, 7, "INT"], [218, 72, 0, 143, 0, "IMAGE"], [235, 158, 0, 159, 0, "MODEL"], [236, 161, 0, 159, 1, "CLIP"], [241, 151, 0, 165, 0, "MODEL"], [242, 161, 0, 165, 1, "CLIP"], [257, 165, 0, 48, 0, "MODEL"], [258, 165, 1, 6, 0, "CLIP"], [259, 165, 1, 7, 0, "CLIP"], [260, 152, 0, 49, 2, "VAE"], [261, 152, 0, 8, 1, "VAE"]], "groups": [{"id": 2, "title": "Prompt", "bounding": [140, -30, 450, 620], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Sampling & Decoding", "bounding": [610, -30, 720, 620], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Save Video(Mp4)", "bounding": [1350, -30, 720, 1010], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Save Video(WebP)", "bounding": [2090, -30, 700, 1010], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Load Video for Outpainting", "bounding": [-290, 610, 580, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 9, "title": "Length - 1 should be divisible by 4", "bounding": [-290, 1040, 370, 330], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "Repeat <PERSON> Batch", "bounding": [310, 610, 280, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 12, "title": "Get video size & frame counts", "bounding": [610, 610, 720, 760], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 14, "title": "Video size - Must be divisble by 16", "bounding": [90, 1040, 500, 330], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 21, "title": "Load models here", "bounding": [-290, -30, 410, 620], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 22, "title": "14B", "bounding": [-270, 10, 370, 330], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 23, "title": "1.3B", "bounding": [-680, -30, 370, 330], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.7400249944259123, "offset": [1071.896715613218, 525.1800937874856]}, "frontendVersion": "1.20.7", "node_versions": {"comfy-core": "0.3.34"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}