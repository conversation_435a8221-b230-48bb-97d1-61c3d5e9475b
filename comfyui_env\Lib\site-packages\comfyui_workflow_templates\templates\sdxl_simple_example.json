{"id": "88bcf757-1298-432f-885a-db5ab88cf224", "revision": 0, "last_node_id": 52, "last_link_id": 48, "nodes": [{"id": 5, "type": "EmptyLatentImage", "pos": [-95.59717559814453, -571.0374755859375], "size": [300, 110], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [27]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1], "color": "#323", "bgcolor": "#535"}, {"id": 42, "type": "Note", "pos": [-75.59688568115234, -421.0376281738281], "size": [260, 210], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "Note - Empty Latent Image", "properties": {"text": ""}, "widgets_values": ["This node sets the image's resolution in Width and Height.\n\nNOTE: For SDXL, it is recommended to use trained values listed below:\n - 1024 x 1024\n - 1152 x 896\n - 896  x 1152\n - 1216 x 832\n - 832  x 1216\n - 1344 x 768\n - 768  x 1344\n - 1536 x 640\n - 640  x 1536"], "color": "#323", "bgcolor": "#535"}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-90, -50], "size": [350, 100], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [10]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [3, 5]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": []}], "title": "Load Checkpoint - BASE", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "sd_xl_base_1.0.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["sd_xl_base_1.0.safetensors"], "color": "#323", "bgcolor": "#535"}, {"id": 45, "type": "PrimitiveNode", "pos": [295.5135192871094, -570.4500122070312], "size": [210, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "steps"}, "links": [38, 41]}], "title": "steps", "properties": {"Run widget replace on values": false}, "widgets_values": [25, "fixed"], "color": "#432", "bgcolor": "#653"}, {"id": 47, "type": "PrimitiveNode", "pos": [296.2197570800781, -439.43743896484375], "size": [210, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "end_at_step"}, "slot_index": 0, "links": [43, 44]}], "title": "end_at_step", "properties": {"Run widget replace on values": false}, "widgets_values": [20, "fixed"], "color": "#432", "bgcolor": "#653"}, {"id": 48, "type": "Note", "pos": [295.3782043457031, -303.24786376953125], "size": [213.91000366210938, 110.16999816894531], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["These can be used to control the total sampling steps and the step at which the sampling switches to the refiner."], "color": "#432", "bgcolor": "#653"}, {"id": 17, "type": "VAEDecode", "pos": [1197.0247802734375, 599.3170166015625], "size": [200, 50], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 25}, {"name": "vae", "type": "VAE", "link": 34}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [28]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": [], "color": "#332922", "bgcolor": "#593930"}, {"id": 41, "type": "Note", "pos": [1195.4132080078125, 699.4724731445312], "size": [312.02081298828125, 120], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "title": "Note - <PERSON><PERSON>r", "properties": {"text": ""}, "widgets_values": ["This node will take the latent data from the KSampler and, using the VAE, it will decode it into visible data\n\nVAE = Latent --> Visible\n\nThis can then be sent to the Save Image node to be saved as a PNG."], "color": "#332922", "bgcolor": "#593930"}, {"id": 7, "type": "CLIPTextEncode", "pos": [311.707763671875, 121.12776947021484], "size": [397.553466796875, 142.57814025878906], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}, {"name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 46}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [12]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark"], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "CLIPTextEncode", "pos": [301.707763671875, -48.872215270996094], "size": [405.0923156738281, 124.19273376464844], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}, {"name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 45}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [11]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["evening sunset scenery blue sky nature, glass bottle with a galaxy in it"], "color": "#232", "bgcolor": "#353"}, {"id": 36, "type": "Note", "pos": [-70, 100], "size": [315.70001220703125, 147.9600067138672], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "title": "Note - Load Checkpoint BASE", "properties": {"text": ""}, "widgets_values": ["This is a checkpoint model loader. \n - This is set up automatically with the optimal settings for whatever SD model version you choose to use.\n - In this example, it is for the Base SDXL model\n - This node is also used for SD1.5 and SD2.x models\n \nNOTE: When loading in another person's workflow, be sure to manually choose your own *local* model. This also applies to LoRas and all their deviations"], "color": "#432", "bgcolor": "#653"}, {"id": 10, "type": "KSamplerAdvanced", "pos": [808.7730102539062, -57.895355224609375], "size": [300, 334], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 10}, {"name": "positive", "type": "CONDITIONING", "link": 11}, {"name": "negative", "type": "CONDITIONING", "link": 12}, {"name": "latent_image", "type": "LATENT", "link": 27}, {"name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 41}, {"name": "end_at_step", "type": "INT", "widget": {"name": "end_at_step"}, "link": 43}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [13]}], "title": "<PERSON><PERSON><PERSON><PERSON> (Advanced) - BASE", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "KSamplerAdvanced"}, "widgets_values": ["enable", 721897303308196, "randomize", 25, 8, "euler", "normal", 0, 20, "enable"]}, {"id": 15, "type": "CLIPTextEncode", "pos": [316.0187072753906, 637.33154296875], "size": [391.591552734375, 148.68138122558594], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 19}, {"name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 47}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [23]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["evening sunset scenery blue sky nature, glass bottle with a galaxy in it"], "color": "#232", "bgcolor": "#353"}, {"id": 16, "type": "CLIPTextEncode", "pos": [315.9026794433594, 837.3108520507812], "size": [388.5469970703125, 121.34178161621094], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 20}, {"name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 48}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [24]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark"], "color": "#322", "bgcolor": "#533"}, {"id": 11, "type": "KSamplerAdvanced", "pos": [819.1201782226562, 608.375], "size": [300, 340], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 14}, {"name": "positive", "type": "CONDITIONING", "link": 23}, {"name": "negative", "type": "CONDITIONING", "link": 24}, {"name": "latent_image", "type": "LATENT", "link": 13}, {"name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 38}, {"name": "start_at_step", "type": "INT", "widget": {"name": "start_at_step"}, "link": 44}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [25]}], "title": "<PERSON><PERSON><PERSON><PERSON> (Advanced) - REFINER", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "KSamplerAdvanced"}, "widgets_values": ["disable", 0, "fixed", 25, 8, "euler", "normal", 20, 10000, "disable"]}, {"id": 12, "type": "CheckpointLoaderSimple", "pos": [-80.37397003173828, 658.974609375], "size": [325.4814453125, 99.73925018310547], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [14]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [19, 20]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [34]}], "title": "Load Checkpoint - REFINER", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "sd_xl_refiner_1.0.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-refiner-1.0/resolve/main/sd_xl_refiner_1.0.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["sd_xl_refiner_1.0.safetensors"], "color": "#323", "bgcolor": "#535"}, {"id": 37, "type": "Note", "pos": [-75.28781127929688, 814.1362915039062], "size": [320.29852294921875, 140.3228302001953], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "title": "Note - Load Checkpoint REFINER", "properties": {"text": ""}, "widgets_values": ["This is a checkpoint model loader. \n - This is set up automatically with the optimal settings for whatever SD model version you choose to use.\n - In this example, it is for the Refiner SDXL model\n\nNOTE: When loading in another person's workflow, be sure to manually choose your own *local* model. This also applies to LoRas and all their deviations."], "color": "#432", "bgcolor": "#653"}, {"id": 52, "type": "Note", "pos": [-449.9957580566406, 322.2831726074219], "size": [284.3299865722656, 123.88999938964844], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [], "title": "Note - Text Prompts", "properties": {"text": ""}, "widgets_values": ["These nodes are where you include the text for:\n - what you want in the picture (Positive Prompt, Green)\n - or what you don't want in the picture (Negative Prompt, Red)\n\nThis node type is called a \"PrimitiveNode\" if you are searching for the node type."], "color": "#323", "bgcolor": "#535"}, {"id": 51, "type": "PrimitiveNode", "pos": [-458.9957580566406, -76.71602630615234], "size": [300, 160], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "widget": {"name": "text"}, "slot_index": 0, "links": [45, 47]}], "title": "Positive Prompt (Text)", "properties": {"Run widget replace on values": false}, "widgets_values": ["evening sunset scenery blue sky nature, glass bottle with a galaxy in it"], "color": "#232", "bgcolor": "#353"}, {"id": 50, "type": "PrimitiveNode", "pos": [-458.9957580566406, 123.28369903564453], "size": [300, 160], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "widget": {"name": "text"}, "slot_index": 0, "links": [46, 48]}], "title": "Negative Prompt (Text)", "properties": {"Run widget replace on values": false}, "widgets_values": ["text, watermark"], "color": "#322", "bgcolor": "#533"}, {"id": 43, "type": "Note", "pos": [319.5082702636719, 1008.0806274414062], "size": [382.79046630859375, 111.91925048828125], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [], "title": "Note - CLIP Encode (REFINER)", "properties": {"text": ""}, "widgets_values": ["These nodes receive the text from the prompt and use the optimal CLIP settings for the specified checkpoint model (in this case: SDXL Refiner)"], "color": "#432", "bgcolor": "#653"}, {"id": 39, "type": "Note", "pos": [311.707763671875, 311.12799072265625], "size": [390.1957702636719, 149.67234802246094], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [], "title": "Note - CLIP Encode (BASE)", "properties": {"text": ""}, "widgets_values": ["These nodes receive the text from the prompt and use the optimal CLIP settings for the specified checkpoint model (in this case: SDXL Base)"], "color": "#432", "bgcolor": "#653"}, {"id": 49, "type": "<PERSON>downNote", "pos": [-386.6277770996094, -321.32781982421875], "size": [225, 88], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/sdxl/)"], "color": "#432", "bgcolor": "#653"}, {"id": 40, "type": "Note", "pos": [1156.8466796875, -59.93302536010742], "size": [451.5, 424.4200134277344], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [], "title": "Note - KSampler  ADVANCED General Information", "properties": {"text": ""}, "widgets_values": ["Here are the settings that SHOULD stay in place if you want this workflow to work correctly:\n - add_noise: enable = This adds random noise into the picture so the model can denoise it\n\n - return_with_leftover_noise: enable = This sends the latent image data and all it's leftover noise to the next KSampler node.\n\nThe settings to pay attention to:\n - control_after_generate = generates a new random seed after each workflow job completed.\n - steps = This is the amount of iterations you would like to run the positive and negative CLIP prompts through. Each Step will add (positive) or remove (negative) pixels based on what stable diffusion \"thinks\" should be there according to the model's training\n - cfg = This is how much you want SDXL to adhere to the prompt. Lower CFG gives you more creative but often blurrier results. Higher CFG (recommended max 10) gives you stricter results according to the CLIP prompt. If the CFG value is too high, it can also result in \"burn-in\" where the edges of the picture become even stronger, often highlighting details in unnatural ways.\n - sampler_name = This is the sampler type, and unfortunately different samplers and schedulers have better results with fewer steps, while others have better success with higher steps. This will require experimentation on your part!\n - scheduler = The algorithm/method used to choose the timesteps to denoise the picture.\n - start_at_step = This is the step number the KSampler will start out it's process of de-noising the picture or \"removing the random noise to reveal the picture within\". The first KSampler usually starts with Step 0. Starting at step 0 is the same as setting denoise to 1.0 in the regular Sampler node.\n - end_at_step = This is the step number the KSampler will stop it's process of de-noising the picture. If there is any remaining leftover noise and return_with_leftover_noise is enabled, then it will pass on the left over noise to the next KSampler (assuming there is another one)."], "color": "#432", "bgcolor": "#653"}, {"id": 19, "type": "SaveImage", "pos": [1558.4725341796875, 553.4407958984375], "size": [565.77001953125, 596.3800048828125], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 28}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33"}, "widgets_values": ["ComfyUI"]}], "links": [[3, 4, 1, 6, 0, "CLIP"], [5, 4, 1, 7, 0, "CLIP"], [10, 4, 0, 10, 0, "MODEL"], [11, 6, 0, 10, 1, "CONDITIONING"], [12, 7, 0, 10, 2, "CONDITIONING"], [13, 10, 0, 11, 3, "LATENT"], [14, 12, 0, 11, 0, "MODEL"], [19, 12, 1, 15, 0, "CLIP"], [20, 12, 1, 16, 0, "CLIP"], [23, 15, 0, 11, 1, "CONDITIONING"], [24, 16, 0, 11, 2, "CONDITIONING"], [25, 11, 0, 17, 0, "LATENT"], [27, 5, 0, 10, 3, "LATENT"], [28, 17, 0, 19, 0, "IMAGE"], [34, 12, 2, 17, 1, "VAE"], [38, 45, 0, 11, 4, "INT"], [41, 45, 0, 10, 4, "INT"], [43, 47, 0, 10, 5, "INT"], [44, 47, 0, 11, 5, "INT"], [45, 51, 0, 6, 1, "STRING"], [46, 50, 0, 7, 1, "STRING"], [47, 51, 0, 15, 1, "STRING"], [48, 50, 0, 16, 1, "STRING"]], "groups": [{"id": 1, "title": "Base Prompt", "bounding": [281.707763671875, -128.8721160888672, 439.7534484863281, 600.5301513671875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Refiner Prompt", "bounding": [285.9026794433594, 567.3108520507812, 442.127685546875, 588.2311401367188], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Load in BASE SDXL Model", "bounding": [-100, -130, 359.4917907714844, 403.9964904785156], "color": "#a1309b", "font_size": 24, "flags": {}}, {"id": 5, "title": "Load in REFINER SDXL Model", "bounding": [-95.2878189086914, 564.1364135742188, 361.54791259765625, 403.3152160644531], "color": "#a1309b", "font_size": 24, "flags": {}}, {"id": 6, "title": "Empty Latent Image", "bounding": [-115.09705352783203, -652.1575317382812, 353.29144287109375, 461.8235168457031], "color": "#a1309b", "font_size": 24, "flags": {}}, {"id": 7, "title": "VAE Decoder", "bounding": [1165.2672119140625, 521.2222900390625, 358.535400390625, 332.57080078125], "color": "#b06634", "font_size": 24, "flags": {}}, {"id": 8, "title": "Step Control", "bounding": [266.23602294921875, -652.185302734375, 274.54791259765625, 472.66363525390625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "Text Prompts", "bounding": [-471.7359313964844, -166.89599609375, 339, 622], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 11, "title": "Base", "bounding": [-110, -173.60000610351562, 1228.77294921875, 655.2579956054688], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 12, "title": "Refiner", "bounding": [-105.2878189086914, 520.5363159179688, 1234.4083251953125, 645.005615234375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.78, "offset": [769.4692037080354, -11.500945460989897]}, "frontendVersion": "1.18.9"}, "version": 0.4}