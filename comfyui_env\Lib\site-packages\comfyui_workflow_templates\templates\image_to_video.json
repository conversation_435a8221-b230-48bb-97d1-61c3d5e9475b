{"last_node_id": 24, "last_link_id": 41, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [867.8, 375.7], "size": [315, 262], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 39}, {"name": "positive", "type": "CONDITIONING", "link": 40}, {"name": "negative", "type": "CONDITIONING", "link": 17}, {"name": "latent_image", "type": "LATENT", "link": 18}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [237514639057514, "randomize", 20, 2.5, "euler", "karras", 1]}, {"id": 8, "type": "VAEDecode", "pos": [1207.8, 375.7], "size": [210, 46], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 26}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [10], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 10, "type": "SaveAnimatedWEBP", "pos": [1459, 376], "size": [741.67, 564.59], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 10}], "outputs": [], "properties": {"Node name for S&R": "SaveAnimatedWEBP"}, "widgets_values": ["ComfyUI", 10, false, 85, "default"]}, {"id": 12, "type": "SVD_img2vid_Conditioning", "pos": [487.8, 395.7], "size": [315, 218], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 24}, {"name": "init_image", "type": "IMAGE", "link": 41, "slot_index": 1}, {"name": "vae", "type": "VAE", "link": 25}], "outputs": [{"name": "positive", "type": "CONDITIONING", "shape": 3, "links": [40], "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "shape": 3, "links": [17], "slot_index": 1}, {"name": "latent", "type": "LATENT", "shape": 3, "links": [18], "slot_index": 2}], "properties": {"Node name for S&R": "SVD_img2vid_Conditioning"}, "widgets_values": [1024, 576, 14, 127, 6, 0]}, {"id": 14, "type": "VideoLinearCFGGuidance", "pos": [487.8, 265.7], "size": [315, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 23}], "outputs": [{"name": "MODEL", "type": "MODEL", "shape": 3, "links": [39], "slot_index": 0}], "properties": {"Node name for S&R": "VideoLinearCFGGuidance"}, "widgets_values": [1]}, {"id": 15, "type": "ImageOnlyCheckpointLoader", "pos": [55, 267], "size": [369.6, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "shape": 3, "links": [23], "slot_index": 0}, {"name": "CLIP_VISION", "type": "CLIP_VISION", "shape": 3, "links": [24], "slot_index": 1}, {"name": "VAE", "type": "VAE", "shape": 3, "links": [25, 26], "slot_index": 2}], "properties": {"Node name for S&R": "ImageOnlyCheckpointLoader"}, "widgets_values": ["svd.safetensors"]}, {"id": 23, "type": "LoadImage", "pos": [106, 441], "size": [315, 314.0], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "shape": 3, "links": [41]}, {"name": "MASK", "type": "MASK", "shape": 3, "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["mountains.png", "image"]}, {"id": 24, "type": "<PERSON>downNote", "pos": [105, 810], "size": [225, 60], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/video/#image-to-video)"], "color": "#432", "bgcolor": "#653"}], "links": [[7, 3, 0, 8, 0, "LATENT"], [10, 8, 0, 10, 0, "IMAGE"], [17, 12, 1, 3, 2, "CONDITIONING"], [18, 12, 2, 3, 3, "LATENT"], [23, 15, 0, 14, 0, "MODEL"], [24, 15, 1, 12, 0, "CLIP_VISION"], [25, 15, 2, 12, 2, "VAE"], [26, 15, 2, 8, 1, "VAE"], [39, 14, 0, 3, 0, "MODEL"], [40, 12, 0, 3, 1, "CONDITIONING"], [41, 23, 0, 12, 1, "IMAGE"]], "groups": [{"id": 1, "title": "Image to Video", "bounding": [480, 195, 954, 478], "color": "#8A8", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.96, "offset": [255.53, 68.37]}}, "version": 0.4, "models": [{"name": "svd.safetensors", "url": "https://huggingface.co/stabilityai/stable-video-diffusion-img2vid/resolve/main/svd.safetensors?download=true", "directory": "checkpoints"}]}