{"version": 3, "file": "ManualConfigurationView-jB_evWIR.js", "sources": ["../../src/views/ManualConfigurationView.vue"], "sourcesContent": ["<template>\n  <BaseViewTemplate dark>\n    <!-- Installation Path Section -->\n    <div\n      class=\"comfy-installer grow flex flex-col gap-4 text-neutral-300 max-w-110\"\n    >\n      <h2 class=\"text-2xl font-semibold text-neutral-100\">\n        {{ $t('install.manualConfiguration.title') }}\n      </h2>\n\n      <p class=\"m-1 text-neutral-300\">\n        <Tag\n          icon=\"pi pi-exclamation-triangle\"\n          severity=\"warn\"\n          :value=\"t('icon.exclamation-triangle')\"\n        />\n        <strong class=\"ml-2\">{{\n          $t('install.gpuSelection.customComfyNeedsPython')\n        }}</strong>\n      </p>\n\n      <div>\n        <p class=\"m-1 mb-4\">\n          {{ $t('install.manualConfiguration.requirements') }}:\n        </p>\n        <ul class=\"m-0\">\n          <li>{{ $t('install.gpuSelection.customManualVenv') }}</li>\n          <li>{{ $t('install.gpuSelection.customInstallRequirements') }}</li>\n        </ul>\n      </div>\n\n      <p class=\"m-1\">{{ $t('install.manualConfiguration.createVenv') }}:</p>\n\n      <Panel :header=\"t('install.manualConfiguration.virtualEnvironmentPath')\">\n        <span class=\"font-mono\">{{ `${basePath}${sep}.venv${sep}` }}</span>\n      </Panel>\n\n      <p class=\"m-1\">\n        {{ $t('install.manualConfiguration.restartWhenFinished') }}\n      </p>\n\n      <Button\n        class=\"place-self-end\"\n        :label=\"t('menuLabels.Restart')\"\n        severity=\"warn\"\n        icon=\"pi pi-refresh\"\n        @click=\"restartApp('Manual configuration complete')\"\n      />\n    </div>\n  </BaseViewTemplate>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport Panel from 'primevue/panel'\nimport Tag from 'primevue/tag'\nimport { onMounted, ref } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { electronAPI } from '@/utils/envUtil'\nimport BaseViewTemplate from '@/views/templates/BaseViewTemplate.vue'\n\nconst { t } = useI18n()\n\nconst electron = electronAPI()\n\nconst basePath = ref<string | null>(null)\nconst sep = ref<'\\\\' | '/'>('/')\n\nconst restartApp = (message?: string) => electron.restartApp(message)\n\nonMounted(async () => {\n  basePath.value = await electron.getBasePath()\n  if (basePath.value.indexOf('/') === -1) sep.value = '\\\\'\n})\n</script>\n\n<style scoped>\n.p-tag {\n  --p-tag-gap: 0.5rem;\n}\n\n.comfy-installer {\n  margin-top: max(1rem, max(0px, calc((100vh - 42rem) * 0.5)));\n}\n</style>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AA8DM,UAAA,EAAE,MAAM;AAEd,UAAM,WAAW;AAEX,UAAA,WAAW,IAAmB,IAAI;AAClC,UAAA,MAAM,IAAgB,GAAG;AAE/B,UAAM,aAAa,wBAAC,YAAqB,SAAS,WAAW,OAAO,GAAjD;AAEnB,cAAU,YAAY;AACX,eAAA,QAAQ,MAAM,SAAS,YAAY;AAC5C,UAAI,SAAS,MAAM,QAAQ,GAAG,MAAM,QAAQ,QAAQ;AAAA,IAAA,CACrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}