{"last_node_id": 78, "last_link_id": 215, "nodes": [{"id": 16, "type": "KSamplerSelect", "pos": [484, 751], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "shape": 3, "links": [19]}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 17, "type": "BasicScheduler", "pos": [478, 860], "size": [315, 106], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 190, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "shape": 3, "links": [20]}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 1]}, {"id": 26, "type": "FluxGuidance", "pos": [520, 100], "size": [317.4, 58], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 175}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "shape": 3, "links": [129], "slot_index": 0}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [6], "color": "#233", "bgcolor": "#355"}, {"id": 45, "type": "EmptyHunyuanLatentVideo", "pos": [475.54, 432.67], "size": [315, 130], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [180], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyHunyuanLatentVideo"}, "widgets_values": [848, 480, 73, 1]}, {"id": 22, "type": "BasicGuider", "pos": [600, 0], "size": [222.35, 46], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 195, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 129, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "shape": 3, "links": [30], "slot_index": 0}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 67, "type": "ModelSamplingSD3", "pos": [360, 0], "size": [210, 58], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 209}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [195], "slot_index": 0}], "properties": {"Node name for S&R": "ModelSamplingSD3"}, "widgets_values": [7]}, {"id": 10, "type": "VAELoader", "pos": [0, 420], "size": [350, 60], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "shape": 3, "links": [206, 211], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["hunyuan_video_vae_bf16.safetensors"]}, {"id": 11, "type": "DualCLIPLoader", "pos": [0, 270], "size": [350, 106], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "shape": 3, "links": [205], "slot_index": 0}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "llava_llama3_fp8_scaled.safetensors", "hunyuan_video", "default"]}, {"id": 73, "type": "VAEDecodeTiled", "pos": [1150, 200], "size": [210, 150], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 210}, {"name": "vae", "type": "VAE", "link": 211}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [215], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecodeTiled"}, "widgets_values": [256, 64, 64, 8]}, {"id": 8, "type": "VAEDecode", "pos": [1150, 90], "size": [210, 46], "flags": {}, "order": 15, "mode": 2, "inputs": [{"name": "samples", "type": "LATENT", "link": 181}, {"name": "vae", "type": "VAE", "link": 206}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 74, "type": "Note", "pos": [1150, 360], "size": [210, 170], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Use the tiled decode node by default because most people will need it.\n\nLower the tile_size and overlap if you run out of memory."], "color": "#432", "bgcolor": "#653"}, {"id": 12, "type": "UNETLoader", "pos": [0, 150], "size": [350, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "shape": 3, "links": [190, 209], "slot_index": 0}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["hun<PERSON>_video_t2v_720p_bf16.safetensors", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 77, "type": "Note", "pos": [0, 0], "size": [350, 110], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Select a fp8 weight_dtype if you are running out of  memory."], "color": "#432", "bgcolor": "#653"}, {"id": 13, "type": "SamplerCustomAdvanced", "pos": [860, 200], "size": [272.36, 124.54], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 37, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 30, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 19, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 20, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 180, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "shape": 3, "links": [181, 210], "slot_index": 0}, {"name": "denoised_output", "type": "LATENT", "shape": 3, "links": null}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 44, "type": "CLIPTextEncode", "pos": [420, 200], "size": [422.85, 164.31], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 205}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [175], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["anime style anime girl with massive fennec ears and one big fluffy tail, she has blonde hair long hair blue eyes wearing a pink sweater and a long blue skirt walking in a beautiful outdoor scenery with snow mountains in the background"], "color": "#232", "bgcolor": "#353"}, {"id": 75, "type": "SaveAnimatedWEBP", "pos": [1410, 200], "size": [315, 366], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 215}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI", 24, false, 80, "default"]}, {"id": 25, "type": "RandomNoise", "pos": [479, 618], "size": [315, 82], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "shape": 3, "links": [37]}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [1, "randomize"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 78, "type": "<PERSON>downNote", "pos": [0, 525], "size": [225, 60], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/hunyuan_video/)"], "color": "#432", "bgcolor": "#653"}], "links": [[19, 16, 0, 13, 2, "SAMPLER"], [20, 17, 0, 13, 3, "SIGMAS"], [30, 22, 0, 13, 1, "GUIDER"], [37, 25, 0, 13, 0, "NOISE"], [129, 26, 0, 22, 1, "CONDITIONING"], [175, 44, 0, 26, 0, "CONDITIONING"], [180, 45, 0, 13, 4, "LATENT"], [181, 13, 0, 8, 0, "LATENT"], [190, 12, 0, 17, 0, "MODEL"], [195, 67, 0, 22, 0, "MODEL"], [205, 11, 0, 44, 0, "CLIP"], [206, 10, 0, 8, 1, "VAE"], [209, 12, 0, 67, 0, "MODEL"], [210, 13, 0, 73, 0, "LATENT"], [211, 10, 0, 73, 1, "VAE"], [215, 73, 0, 75, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"groupNodes": {}, "ds": {"scale": 0.86, "offset": [315.94, 195.23]}}, "version": 0.4, "models": [{"name": "hunyuan_video_vae_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/vae/hunyuan_video_vae_bf16.safetensors?download=true", "directory": "vae"}, {"name": "llava_llama3_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/text_encoders/llava_llama3_fp8_scaled.safetensors?download=true", "directory": "text_encoders"}, {"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors?download=true", "directory": "text_encoders"}, {"name": "hun<PERSON>_video_t2v_720p_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/HunyuanVideo_repackaged/resolve/main/split_files/diffusion_models/hunyuan_video_t2v_720p_bf16.safetensors?download=true", "directory": "diffusion_models"}]}