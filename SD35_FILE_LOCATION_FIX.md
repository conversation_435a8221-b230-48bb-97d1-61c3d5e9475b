# SD3.5 Large 文件位置修复指南

## 🚨 问题解决

您遇到的错误：`Value not in list: unet_name: 'sd3.5_large.safetensors' not in ['flux1-dev-fp8.safetensors']`

**原因**: SD3.5 Large 文件在错误的目录中
- ❌ 当前位置: `models/checkpoints/sd3.5_large.safetensors`
- ✅ 需要位置: `models/unet/sd3.5_large.safetensors` (用于 UNETLoader)

## ✅ 已修复

我已经为您复制了文件到正确位置：

### 📁 当前文件分布
```
models/
├── checkpoints/
│   ├── sd3.5_large.safetensors ✅ (15.33GB) - 用于 CheckpointLoader
│   └── v1-5-pruned-emaonly.safetensors ✅
├── unet/
│   ├── sd3.5_large.safetensors ✅ (15.33GB) - 用于 UNETLoader
│   └── flux1-dev-fp8.safetensors ✅
├── clip/
│   ├── clip_l.safetensors ✅
│   ├── clip_g.safetensors ✅
│   └── t5xxl_fp8_e4m3fn.safetensors ✅
└── vae/
    └── ae.safetensors ✅
```

## 🚀 可用的工作流方案

### 方案 1: UNETLoader 工作流 ⭐⭐⭐⭐⭐

**文件**: `workflows/sd35_external_clip.json`

**特点**:
- ✅ 使用 UNETLoader (现在可用)
- ✅ 使用外部 CLIP 文件
- ✅ 最大化 SD3.5 性能
- ✅ 完整的组件分离

**使用方法**:
```
1. 导入: sd35_external_clip.json
2. 启动: python main.py --lowvram --auto-launch
3. 生成图片
```

### 方案 2: CheckpointLoader 工作流 ⭐⭐⭐⭐

**文件**: `workflows/sd35_checkpoint_loader.json` (新创建)

**特点**:
- ✅ 使用 CheckpointLoaderSimple
- ✅ 更简单的配置
- ✅ 使用内置组件
- ✅ 兼容性更好

**使用方法**:
```
1. 导入: sd35_checkpoint_loader.json
2. 启动: python main.py --lowvram --auto-launch
3. 生成图片
```

### 方案 3: 可靠的 Flux 工作流 ⭐⭐⭐⭐⭐

**文件**: `workflows/fallback_flux_reliable.json`

**特点**:
- ✅ 100% 稳定可靠
- ✅ 批量生成8张
- ✅ 顶级图像质量
- ✅ 无配置问题

## 📊 方案对比

| 工作流 | 加载方式 | 复杂度 | 兼容性 | 推荐度 |
|--------|----------|--------|--------|--------|
| **sd35_external_clip** | UNETLoader | 高 | ⚠️ 需要配置 | ⭐⭐⭐⭐⭐ |
| **sd35_checkpoint_loader** | CheckpointLoader | 低 | ✅ 简单 | ⭐⭐⭐⭐ |
| **fallback_flux_reliable** | UNETLoader | 中 | ✅ 完美 | ⭐⭐⭐⭐⭐ |

## 🎯 推荐使用流程

### 第一步: 测试 CheckpointLoader 方案（推荐开始）
```
1. 导入: sd35_checkpoint_loader.json
2. 启动: python main.py --lowvram --auto-launch
3. 提示词: "a professional portrait of a beautiful woman, natural lighting, photorealistic, masterpiece"
4. 验证是否正常工作
```

### 第二步: 尝试 UNETLoader 方案（高级功能）
```
1. 导入: sd35_external_clip.json
2. 测试外部 CLIP 的效果
3. 对比两种方案的质量差异
```

### 第三步: 备选 Flux 方案（最稳定）
```
1. 导入: fallback_flux_reliable.json
2. 享受稳定的批量生成
3. 作为主力工作流使用
```

## ⚙️ 显存优化

### 针对您的 RTX 4070 (8GB):

#### SD3.5 Large 显存需求:
```
CheckpointLoader: 约 7-8GB
UNETLoader + 外部CLIP: 约 8-9GB
```

#### 推荐启动参数:
```bash
# CheckpointLoader 方案
python main.py --lowvram --auto-launch

# UNETLoader 方案 (需要更多优化)
python main.py --lowvram --cpu-vae --auto-launch
```

## 🔍 故障排除

### 1. UNETLoader 仍然找不到文件
**解决方案**:
- 重启 ComfyUI
- 检查文件是否在 `models/unet/` 目录
- 确认文件名完全匹配

### 2. CheckpointLoader 报错
**解决方案**:
- 确认文件在 `models/checkpoints/` 目录
- 检查文件是否损坏
- 尝试重新下载模型

### 3. 显存不足
**解决方案**:
```bash
# 使用更激进的优化
python main.py --lowvram --cpu-vae --auto-launch

# 或使用 Flux 替代
使用 fallback_flux_reliable.json
```

## 💡 最佳实践建议

### 文件管理策略:
1. **保留两个副本**: checkpoints 和 unet 目录各一份
2. **定期清理**: 删除不需要的模型文件
3. **备份重要文件**: 避免意外删除

### 工作流选择策略:
1. **日常使用**: CheckpointLoader 方案 (简单稳定)
2. **高级功能**: UNETLoader 方案 (最大性能)
3. **批量生成**: Flux 方案 (最可靠)

## 🎉 现在您可以:

- ✅ **使用 SD3.5 Large UNETLoader** (文件已移动)
- ✅ **使用 SD3.5 Large CheckpointLoader** (新工作流)
- ✅ **选择最适合的方案** (三种选择)
- ✅ **享受顶级图像质量** (SD3.5 先进功能)

立即试试 `sd35_checkpoint_loader.json` 工作流，这是最简单可靠的开始方式！🎨✨

## 🔧 快速命令参考

### 验证文件位置:
```bash
# 检查 UNet 目录
dir models\unet\

# 检查 Checkpoints 目录  
dir models\checkpoints\
```

### 如果需要重新复制:
```bash
Copy-Item "models\checkpoints\sd3.5_large.safetensors" "models\unet\"
```

您的 SD3.5 Large 模型现在已正确配置，可以使用任何方案！
