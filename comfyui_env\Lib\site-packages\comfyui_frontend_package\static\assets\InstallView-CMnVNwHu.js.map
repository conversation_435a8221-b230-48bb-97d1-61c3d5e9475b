{"version": 3, "file": "InstallView-CMnVNwHu.js", "sources": ["../../src/components/install/DesktopSettingsConfiguration.vue", "../../../../../../../assets/images/nvidia-logo.svg", "../../../../../../../assets/images/apple-mps-logo.png", "../../../../../../../assets/images/manual-configuration.svg", "../../src/components/install/GpuPicker.vue", "../../src/components/install/InstallLocationPicker.vue", "../../src/components/install/MigrationPicker.vue", "../../src/components/install/mirror/MirrorItem.vue", "../../src/components/install/MirrorsConfiguration.vue", "../../src/views/InstallView.vue"], "sourcesContent": ["<template>\n  <div class=\"flex flex-col gap-6 w-[600px]\">\n    <div class=\"flex flex-col gap-4\">\n      <h2 class=\"text-2xl font-semibold text-neutral-100\">\n        {{ $t('install.desktopAppSettings') }}\n      </h2>\n\n      <p class=\"text-neutral-400 my-0\">\n        {{ $t('install.desktopAppSettingsDescription') }}\n      </p>\n    </div>\n\n    <div class=\"flex flex-col bg-neutral-800 p-4 rounded-lg\">\n      <!-- Auto Update Setting -->\n      <div class=\"flex items-center gap-4\">\n        <div class=\"flex-1\">\n          <h3 class=\"text-lg font-medium text-neutral-100\">\n            {{ $t('install.settings.autoUpdate') }}\n          </h3>\n          <p class=\"text-sm text-neutral-400 mt-1\">\n            {{ $t('install.settings.autoUpdateDescription') }}\n          </p>\n        </div>\n        <ToggleSwitch v-model=\"autoUpdate\" />\n      </div>\n\n      <Divider />\n\n      <!-- Metrics Collection Setting -->\n      <div class=\"flex items-center gap-4\">\n        <div class=\"flex-1\">\n          <h3 class=\"text-lg font-medium text-neutral-100\">\n            {{ $t('install.settings.allowMetrics') }}\n          </h3>\n          <p class=\"text-sm text-neutral-400 mt-1\">\n            {{ $t('install.settings.allowMetricsDescription') }}\n          </p>\n          <a\n            href=\"#\"\n            class=\"text-sm text-blue-400 hover:text-blue-300 mt-1 inline-block\"\n            @click.prevent=\"showMetricsInfo\"\n          >\n            {{ $t('install.settings.learnMoreAboutData') }}\n          </a>\n        </div>\n        <ToggleSwitch v-model=\"allowMetrics\" />\n      </div>\n    </div>\n\n    <!-- Info Dialog -->\n    <Dialog\n      v-model:visible=\"showDialog\"\n      modal\n      :header=\"$t('install.settings.dataCollectionDialog.title')\"\n    >\n      <div class=\"text-neutral-300\">\n        <h4 class=\"font-medium mb-2\">\n          {{ $t('install.settings.dataCollectionDialog.whatWeCollect') }}\n        </h4>\n        <ul class=\"list-disc pl-6 space-y-1\">\n          <li>\n            {{\n              $t('install.settings.dataCollectionDialog.collect.errorReports')\n            }}\n          </li>\n          <li>\n            {{ $t('install.settings.dataCollectionDialog.collect.systemInfo') }}\n          </li>\n          <li>\n            {{\n              $t(\n                'install.settings.dataCollectionDialog.collect.userJourneyEvents'\n              )\n            }}\n          </li>\n        </ul>\n\n        <h4 class=\"font-medium mt-4 mb-2\">\n          {{ $t('install.settings.dataCollectionDialog.whatWeDoNotCollect') }}\n        </h4>\n        <ul class=\"list-disc pl-6 space-y-1\">\n          <li>\n            {{\n              $t(\n                'install.settings.dataCollectionDialog.doNotCollect.personalInformation'\n              )\n            }}\n          </li>\n          <li>\n            {{\n              $t(\n                'install.settings.dataCollectionDialog.doNotCollect.workflowContents'\n              )\n            }}\n          </li>\n          <li>\n            {{\n              $t(\n                'install.settings.dataCollectionDialog.doNotCollect.fileSystemInformation'\n              )\n            }}\n          </li>\n          <li>\n            {{\n              $t(\n                'install.settings.dataCollectionDialog.doNotCollect.customNodeConfigurations'\n              )\n            }}\n          </li>\n        </ul>\n\n        <div class=\"mt-4\">\n          <a\n            href=\"https://comfy.org/privacy\"\n            target=\"_blank\"\n            class=\"text-blue-400 hover:text-blue-300 underline\"\n          >\n            {{ $t('install.settings.dataCollectionDialog.viewFullPolicy') }}\n          </a>\n        </div>\n      </div>\n    </Dialog>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Dialog from 'primevue/dialog'\nimport Divider from 'primevue/divider'\nimport ToggleSwitch from 'primevue/toggleswitch'\nimport { ref } from 'vue'\n\nconst showDialog = ref(false)\nconst autoUpdate = defineModel<boolean>('autoUpdate', { required: true })\nconst allowMetrics = defineModel<boolean>('allowMetrics', { required: true })\n\nconst showMetricsInfo = () => {\n  showDialog.value = true\n}\n</script>\n", "export default \"__VITE_PUBLIC_ASSET__fe43f3f5__\"", "export default \"__VITE_PUBLIC_ASSET__2bae65e3__\"", "export default \"__VITE_PUBLIC_ASSET__57befc1b__\"", "<template>\n  <div class=\"flex flex-col gap-6 w-[600px] h-[30rem] select-none\">\n    <!-- Installation Path Section -->\n    <div class=\"grow flex flex-col gap-4 text-neutral-300\">\n      <h2 class=\"text-2xl font-semibold text-neutral-100\">\n        {{ $t('install.gpuSelection.selectGpu') }}\n      </h2>\n\n      <p class=\"m-1 text-neutral-400\">\n        {{ $t('install.gpuSelection.selectGpuDescription') }}:\n      </p>\n\n      <!-- GPU Selection buttons -->\n      <div\n        class=\"flex gap-2 text-center transition-opacity\"\n        :class=\"{ selected: selected }\"\n      >\n        <!-- NVIDIA -->\n        <div\n          v-if=\"platform !== 'darwin'\"\n          class=\"gpu-button\"\n          :class=\"{ selected: selected === 'nvidia' }\"\n          role=\"button\"\n          @click=\"pickGpu('nvidia')\"\n        >\n          <img\n            class=\"m-12\"\n            alt=\"NVIDIA logo\"\n            width=\"196\"\n            height=\"32\"\n            src=\"/assets/images/nvidia-logo.svg\"\n          />\n        </div>\n        <!-- MPS -->\n        <div\n          v-if=\"platform === 'darwin'\"\n          class=\"gpu-button\"\n          :class=\"{ selected: selected === 'mps' }\"\n          role=\"button\"\n          @click=\"pickGpu('mps')\"\n        >\n          <img\n            class=\"rounded-lg hover-brighten\"\n            alt=\"Apple Metal Performance Shaders Logo\"\n            width=\"292\"\n            ratio\n            src=\"/assets/images/apple-mps-logo.png\"\n          />\n        </div>\n        <!-- Manual configuration -->\n        <div\n          class=\"gpu-button\"\n          :class=\"{ selected: selected === 'unsupported' }\"\n          role=\"button\"\n          @click=\"pickGpu('unsupported')\"\n        >\n          <img\n            class=\"m-12\"\n            alt=\"Manual configuration\"\n            width=\"196\"\n            src=\"/assets/images/manual-configuration.svg\"\n          />\n        </div>\n      </div>\n\n      <!-- Details on selected GPU -->\n      <p v-if=\"selected === 'nvidia'\" class=\"m-1\">\n        <Tag icon=\"pi pi-check\" severity=\"success\" :value=\"'CUDA'\" />\n        {{ $t('install.gpuSelection.nvidiaDescription') }}\n      </p>\n\n      <p v-if=\"selected === 'mps'\" class=\"m-1\">\n        <Tag icon=\"pi pi-check\" severity=\"success\" :value=\"'MPS'\" />\n        {{ $t('install.gpuSelection.mpsDescription') }}\n      </p>\n\n      <div v-if=\"selected === 'unsupported'\" class=\"text-neutral-300\">\n        <p class=\"m-1\">\n          <Tag\n            icon=\"pi pi-exclamation-triangle\"\n            severity=\"warn\"\n            :value=\"t('icon.exclamation-triangle')\"\n          />\n          {{ $t('install.gpuSelection.customSkipsPython') }}\n        </p>\n\n        <ul>\n          <li>\n            <strong>\n              {{ $t('install.gpuSelection.customComfyNeedsPython') }}\n            </strong>\n          </li>\n          <li>{{ $t('install.gpuSelection.customManualVenv') }}</li>\n          <li>{{ $t('install.gpuSelection.customInstallRequirements') }}</li>\n          <li>{{ $t('install.gpuSelection.customMayNotWork') }}</li>\n        </ul>\n      </div>\n\n      <div v-if=\"selected === 'cpu'\">\n        <p class=\"m-1\">\n          <Tag\n            icon=\"pi pi-exclamation-triangle\"\n            severity=\"warn\"\n            :value=\"t('icon.exclamation-triangle')\"\n          />\n          {{ $t('install.gpuSelection.cpuModeDescription') }}\n        </p>\n        <p class=\"m-1\">\n          {{ $t('install.gpuSelection.cpuModeDescription2') }}\n        </p>\n      </div>\n    </div>\n\n    <div\n      class=\"transition-opacity flex gap-3 h-0\"\n      :class=\"{\n        'opacity-40': selected && selected !== 'cpu'\n      }\"\n    >\n      <ToggleSwitch\n        v-model=\"cpuMode\"\n        input-id=\"cpu-mode\"\n        class=\"-translate-y-40\"\n      />\n      <label for=\"cpu-mode\" class=\"select-none\">\n        {{ $t('install.gpuSelection.enableCpuMode') }}\n      </label>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport type { TorchDeviceType } from '@comfyorg/comfyui-electron-types'\nimport Tag from 'primevue/tag'\nimport ToggleSwitch from 'primevue/toggleswitch'\nimport { computed } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { electronAPI } from '@/utils/envUtil'\n\nconst { t } = useI18n()\n\nconst cpuMode = computed({\n  get: () => selected.value === 'cpu',\n  set: (value) => {\n    selected.value = value ? 'cpu' : null\n  }\n})\nconst selected = defineModel<TorchDeviceType | null>('device', {\n  required: true\n})\n\nconst electron = electronAPI()\nconst platform = electron.getPlatform()\n\nconst pickGpu = (value: typeof selected.value) => {\n  const newValue = selected.value === value ? null : value\n  selected.value = newValue\n}\n</script>\n\n<style scoped>\n.p-tag {\n  --p-tag-gap: 0.5rem;\n}\n\n.hover-brighten {\n  @apply transition-colors;\n  transition-property: filter, box-shadow;\n\n  &:hover {\n    filter: brightness(107%) contrast(105%);\n    box-shadow: 0 0 0.25rem #ffffff79;\n  }\n}\n.p-accordioncontent-content {\n  @apply bg-neutral-900 rounded-lg transition-colors;\n}\n\ndiv.selected {\n  .gpu-button:not(.selected) {\n    @apply opacity-50 hover:opacity-100;\n  }\n}\n\n.gpu-button {\n  @apply w-1/2 m-0 cursor-pointer rounded-lg flex flex-col items-center justify-around bg-neutral-800 bg-opacity-50 hover:bg-opacity-75 transition-colors;\n\n  &.selected {\n    @apply opacity-100 bg-neutral-700 bg-opacity-50 hover:bg-opacity-60;\n  }\n}\n\n.disabled {\n  @apply pointer-events-none opacity-40;\n}\n\n.p-card-header {\n  @apply text-center grow;\n}\n\n.p-card-body {\n  @apply text-center pt-0;\n}\n</style>\n", "<template>\n  <div class=\"flex flex-col gap-6 w-[600px]\">\n    <!-- Installation Path Section -->\n    <div class=\"flex flex-col gap-4\">\n      <h2 class=\"text-2xl font-semibold text-neutral-100\">\n        {{ $t('install.chooseInstallationLocation') }}\n      </h2>\n\n      <p class=\"text-neutral-400 my-0\">\n        {{ $t('install.installLocationDescription') }}\n      </p>\n\n      <div class=\"flex gap-2\">\n        <IconField class=\"flex-1\">\n          <InputText\n            v-model=\"installPath\"\n            class=\"w-full\"\n            :class=\"{ 'p-invalid': pathError }\"\n            @update:model-value=\"validatePath\"\n            @focus=\"onFocus\"\n          />\n          <InputIcon\n            v-tooltip.top=\"$t('install.installLocationTooltip')\"\n            class=\"pi pi-info-circle\"\n          />\n        </IconField>\n        <Button icon=\"pi pi-folder\" class=\"w-12\" @click=\"browsePath\" />\n      </div>\n\n      <Message v-if=\"pathError\" severity=\"error\" class=\"whitespace-pre-line\">\n        {{ pathError }}\n      </Message>\n      <Message v-if=\"pathExists\" severity=\"warn\">\n        {{ $t('install.pathExists') }}\n      </Message>\n      <Message v-if=\"nonDefaultDrive\" severity=\"warn\">\n        {{ $t('install.nonDefaultDrive') }}\n      </Message>\n    </div>\n\n    <!-- System Paths Info -->\n    <div class=\"bg-neutral-800 p-4 rounded-lg\">\n      <h3 class=\"text-lg font-medium mt-0 mb-3 text-neutral-100\">\n        {{ $t('install.systemLocations') }}\n      </h3>\n      <div class=\"flex flex-col gap-2\">\n        <div class=\"flex items-center gap-2\">\n          <i class=\"pi pi-folder text-neutral-400\" />\n          <span class=\"text-neutral-400\">App Data:</span>\n          <span class=\"text-neutral-200\">{{ appData }}</span>\n          <span\n            v-tooltip=\"$t('install.appDataLocationTooltip')\"\n            class=\"pi pi-info-circle\"\n          />\n        </div>\n        <div class=\"flex items-center gap-2\">\n          <i class=\"pi pi-desktop text-neutral-400\" />\n          <span class=\"text-neutral-400\">App Path:</span>\n          <span class=\"text-neutral-200\">{{ appPath }}</span>\n          <span\n            v-tooltip=\"$t('install.appPathLocationTooltip')\"\n            class=\"pi pi-info-circle\"\n          />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport IconField from 'primevue/iconfield'\nimport InputIcon from 'primevue/inputicon'\nimport InputText from 'primevue/inputtext'\nimport Message from 'primevue/message'\nimport { onMounted, ref } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { electronAPI } from '@/utils/envUtil'\n\nconst { t } = useI18n()\n\nconst installPath = defineModel<string>('installPath', { required: true })\nconst pathError = defineModel<string>('pathError', { required: true })\nconst pathExists = ref(false)\nconst nonDefaultDrive = ref(false)\nconst appData = ref('')\nconst appPath = ref('')\nconst inputTouched = ref(false)\n\nconst electron = electronAPI()\n\n// Get system paths on component mount\nonMounted(async () => {\n  const paths = await electron.getSystemPaths()\n  appData.value = paths.appData\n  appPath.value = paths.appPath\n  installPath.value = paths.defaultInstallPath\n\n  await validatePath(paths.defaultInstallPath)\n})\n\nconst validatePath = async (path: string | undefined) => {\n  try {\n    pathError.value = ''\n    pathExists.value = false\n    nonDefaultDrive.value = false\n    const validation = await electron.validateInstallPath(path ?? '')\n\n    // Create a pre-formatted list of errors\n    if (!validation.isValid) {\n      const errors: string[] = []\n      if (validation.cannotWrite) errors.push(t('install.cannotWrite'))\n      if (validation.freeSpace < validation.requiredSpace) {\n        const requiredGB = validation.requiredSpace / 1024 / 1024 / 1024\n        errors.push(`${t('install.insufficientFreeSpace')}: ${requiredGB} GB`)\n      }\n      if (validation.parentMissing) errors.push(t('install.parentMissing'))\n      if (validation.isOneDrive) errors.push(t('install.isOneDrive'))\n\n      if (validation.error)\n        errors.push(`${t('install.unhandledError')}: ${validation.error}`)\n      pathError.value = errors.join('\\n')\n    }\n\n    if (validation.isNonDefaultDrive) nonDefaultDrive.value = true\n    if (validation.exists) pathExists.value = true\n  } catch (error) {\n    pathError.value = t('install.pathValidationFailed')\n  }\n}\n\nconst browsePath = async () => {\n  try {\n    const result = await electron.showDirectoryPicker()\n    if (result) {\n      installPath.value = result\n      await validatePath(result)\n    }\n  } catch (error) {\n    pathError.value = t('install.failedToSelectDirectory')\n  }\n}\n\nconst onFocus = async () => {\n  if (!inputTouched.value) {\n    inputTouched.value = true\n    return\n  }\n  // Refresh validation on re-focus\n  await validatePath(installPath.value)\n}\n</script>\n", "<template>\n  <div class=\"flex flex-col gap-6 w-[600px]\">\n    <!-- Source Location Section -->\n    <div class=\"flex flex-col gap-4\">\n      <h2 class=\"text-2xl font-semibold text-neutral-100\">\n        {{ $t('install.migrateFromExistingInstallation') }}\n      </h2>\n\n      <p class=\"text-neutral-400 my-0\">\n        {{ $t('install.migrationSourcePathDescription') }}\n      </p>\n\n      <div class=\"flex gap-2\">\n        <InputText\n          v-model=\"sourcePath\"\n          placeholder=\"Select existing ComfyUI installation (optional)\"\n          class=\"flex-1\"\n          :class=\"{ 'p-invalid': pathError }\"\n          @update:model-value=\"validateSource\"\n        />\n        <Button icon=\"pi pi-folder\" class=\"w-12\" @click=\"browsePath\" />\n      </div>\n\n      <Message v-if=\"pathError\" severity=\"error\">\n        {{ pathError }}\n      </Message>\n    </div>\n\n    <!-- Migration Options -->\n    <div\n      v-if=\"isValidSource\"\n      class=\"flex flex-col gap-4 bg-neutral-800 p-4 rounded-lg\"\n    >\n      <h3 class=\"text-lg mt-0 font-medium text-neutral-100\">\n        {{ $t('install.selectItemsToMigrate') }}\n      </h3>\n\n      <div class=\"flex flex-col gap-3\">\n        <div\n          v-for=\"item in migrationItems\"\n          :key=\"item.id\"\n          class=\"flex items-center gap-3 p-2 hover:bg-neutral-700 rounded\"\n          @click=\"item.selected = !item.selected\"\n        >\n          <Checkbox\n            v-model=\"item.selected\"\n            :input-id=\"item.id\"\n            :binary=\"true\"\n            @click.stop\n          />\n          <div>\n            <label :for=\"item.id\" class=\"text-neutral-200 font-medium\">\n              {{ item.label }}\n            </label>\n            <p class=\"text-sm text-neutral-400 my-1\">\n              {{ item.description }}\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Skip Migration -->\n    <div v-else class=\"text-neutral-400 italic\">\n      {{ $t('install.migrationOptional') }}\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { MigrationItems } from '@comfyorg/comfyui-electron-types'\nimport Button from 'primevue/button'\nimport Checkbox from 'primevue/checkbox'\nimport InputText from 'primevue/inputtext'\nimport Message from 'primevue/message'\nimport { computed, ref, watchEffect } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { electronAPI } from '@/utils/envUtil'\n\nconst { t } = useI18n()\n\nconst electron = electronAPI()\n\nconst sourcePath = defineModel<string>('sourcePath', { required: false })\nconst migrationItemIds = defineModel<string[]>('migrationItemIds', {\n  required: false\n})\n\nconst migrationItems = ref(\n  MigrationItems.map((item) => ({\n    ...item,\n    selected: true\n  }))\n)\n\nconst pathError = ref('')\nconst isValidSource = computed(\n  () => sourcePath.value !== '' && pathError.value === ''\n)\n\nconst validateSource = async (sourcePath: string | undefined) => {\n  if (!sourcePath) {\n    pathError.value = ''\n    return\n  }\n\n  try {\n    pathError.value = ''\n    const validation = await electron.validateComfyUISource(sourcePath)\n\n    if (!validation.isValid) pathError.value = validation.error ?? 'ERROR'\n  } catch (error) {\n    console.error(error)\n    pathError.value = t('install.pathValidationFailed')\n  }\n}\n\nconst browsePath = async () => {\n  try {\n    const result = await electron.showDirectoryPicker()\n    if (result) {\n      sourcePath.value = result\n      await validateSource(result)\n    }\n  } catch (error) {\n    console.error(error)\n    pathError.value = t('install.failedToSelectDirectory')\n  }\n}\n\nwatchEffect(() => {\n  migrationItemIds.value = migrationItems.value\n    .filter((item) => item.selected)\n    .map((item) => item.id)\n})\n</script>\n", "<template>\n  <div class=\"flex flex-col items-center gap-4\">\n    <div class=\"w-full\">\n      <h3 class=\"text-lg font-medium text-neutral-100\">\n        {{ $t(`settings.${normalizedSettingId}.name`) }}\n      </h3>\n      <p class=\"text-sm text-neutral-400 mt-1\">\n        {{ $t(`settings.${normalizedSettingId}.tooltip`) }}\n      </p>\n    </div>\n    <UrlInput\n      v-model=\"modelValue\"\n      :validate-url-fn=\"\n        (mirror: string) =>\n          checkMirrorReachable(mirror + (item.validationPathSuffix ?? ''))\n      \"\n      @state-change=\"validationState = $event\"\n    />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, onMounted, ref, watch } from 'vue'\n\nimport UrlInput from '@/components/common/UrlInput.vue'\nimport { UVMirror } from '@/constants/uvMirrors'\nimport { normalizeI18nKey } from '@/utils/formatUtil'\nimport { checkMirrorReachable } from '@/utils/networkUtil'\nimport { ValidationState } from '@/utils/validationUtil'\n\nconst { item } = defineProps<{\n  item: UVMirror\n}>()\n\nconst emit = defineEmits<{\n  'state-change': [state: ValidationState]\n}>()\n\nconst modelValue = defineModel<string>('modelValue', { required: true })\nconst validationState = ref<ValidationState>(ValidationState.IDLE)\n\nconst normalizedSettingId = computed(() => {\n  return normalizeI18nKey(item.settingId)\n})\n\nonMounted(() => {\n  modelValue.value = item.mirror\n})\n\nwatch(validationState, (newState) => {\n  emit('state-change', newState)\n\n  // Set fallback mirror if default mirror is invalid\n  if (\n    newState === ValidationState.INVALID &&\n    modelValue.value === item.mirror\n  ) {\n    modelValue.value = item.fallbackMirror\n  }\n})\n</script>\n", "<template>\n  <Panel\n    :header=\"$t('install.settings.mirrorSettings')\"\n    toggleable\n    :collapsed=\"!showMirrorInputs\"\n    pt:root=\"bg-neutral-800 border-none w-[600px]\"\n  >\n    <template\n      v-for=\"([item, modelValue], index) in mirrors\"\n      :key=\"item.settingId + item.mirror\"\n    >\n      <Divider v-if=\"index > 0\" />\n\n      <MirrorItem\n        v-model=\"modelValue.value\"\n        :item=\"item\"\n        @state-change=\"validationStates[index] = $event\"\n      />\n    </template>\n    <template #icons>\n      <i\n        v-tooltip=\"validationStateTooltip\"\n        :class=\"{\n          'pi pi-spin pi-spinner text-neutral-400':\n            validationState === ValidationState.LOADING,\n          'pi pi-check text-green-500':\n            validationState === ValidationState.VALID,\n          'pi pi-times text-red-500':\n            validationState === ValidationState.INVALID\n        }\"\n      />\n    </template>\n  </Panel>\n</template>\n\n<script setup lang=\"ts\">\nimport {\n  TorchDeviceType,\n  TorchMirrorUrl\n} from '@comfyorg/comfyui-electron-types'\nimport Divider from 'primevue/divider'\nimport Panel from 'primevue/panel'\nimport { ModelRef, computed, onMounted, ref } from 'vue'\n\nimport MirrorItem from '@/components/install/mirror/MirrorItem.vue'\nimport { PYPI_MIRROR, PYTHON_MIRROR, UVMirror } from '@/constants/uvMirrors'\nimport { t } from '@/i18n'\nimport { isInChina } from '@/utils/networkUtil'\nimport { ValidationState, mergeValidationStates } from '@/utils/validationUtil'\n\nconst showMirrorInputs = ref(false)\nconst { device } = defineProps<{ device: TorchDeviceType | null }>()\nconst pythonMirror = defineModel<string>('pythonMirror', { required: true })\nconst pypiMirror = defineModel<string>('pypiMirror', { required: true })\nconst torchMirror = defineModel<string>('torchMirror', { required: true })\n\nconst getTorchMirrorItem = (device: TorchDeviceType): UVMirror => {\n  const settingId = 'Comfy-Desktop.UV.TorchInstallMirror'\n  switch (device) {\n    case 'mps':\n      return {\n        settingId,\n        mirror: TorchMirrorUrl.NightlyCpu,\n        fallbackMirror: TorchMirrorUrl.NightlyCpu\n      }\n    case 'nvidia':\n      return {\n        settingId,\n        mirror: TorchMirrorUrl.Cuda,\n        fallbackMirror: TorchMirrorUrl.Cuda\n      }\n    case 'cpu':\n    default:\n      return {\n        settingId,\n        mirror: PYPI_MIRROR.mirror,\n        fallbackMirror: PYPI_MIRROR.fallbackMirror\n      }\n  }\n}\n\nconst userIsInChina = ref(false)\nonMounted(async () => {\n  userIsInChina.value = await isInChina()\n})\n\nconst useFallbackMirror = (mirror: UVMirror) => ({\n  ...mirror,\n  mirror: mirror.fallbackMirror\n})\n\nconst mirrors = computed<[UVMirror, ModelRef<string>][]>(() =>\n  (\n    [\n      [PYTHON_MIRROR, pythonMirror],\n      [PYPI_MIRROR, pypiMirror],\n      [getTorchMirrorItem(device ?? 'cpu'), torchMirror]\n    ] as [UVMirror, ModelRef<string>][]\n  ).map(([item, modelValue]) => [\n    userIsInChina.value ? useFallbackMirror(item) : item,\n    modelValue\n  ])\n)\n\nconst validationStates = ref<ValidationState[]>(\n  mirrors.value.map(() => ValidationState.IDLE)\n)\nconst validationState = computed(() => {\n  return mergeValidationStates(validationStates.value)\n})\nconst validationStateTooltip = computed(() => {\n  switch (validationState.value) {\n    case ValidationState.INVALID:\n      return t('install.settings.mirrorsUnreachable')\n    case ValidationState.VALID:\n      return t('install.settings.mirrorsReachable')\n    default:\n      return t('install.settings.checkingMirrors')\n  }\n})\n</script>\n", "<template>\n  <BaseViewTemplate dark>\n    <!-- h-full to make sure the stepper does not layout shift between steps\n    as for each step the stepper height is different. Inherit the center element\n    placement from BaseViewTemplate would cause layout shift. -->\n    <Stepper\n      class=\"h-full p-8 2xl:p-16\"\n      value=\"0\"\n      @update:value=\"handleStepChange\"\n    >\n      <StepList class=\"select-none\">\n        <Step value=\"0\">\n          {{ $t('install.gpu') }}\n        </Step>\n        <Step value=\"1\" :disabled=\"noGpu\">\n          {{ $t('install.installLocation') }}\n        </Step>\n        <Step value=\"2\" :disabled=\"noGpu || hasError || highestStep < 1\">\n          {{ $t('install.migration') }}\n        </Step>\n        <Step value=\"3\" :disabled=\"noGpu || hasError || highestStep < 2\">\n          {{ $t('install.desktopSettings') }}\n        </Step>\n      </StepList>\n      <StepPanels>\n        <StepPanel v-slot=\"{ activateCallback }\" value=\"0\">\n          <GpuPicker v-model:device=\"device\" />\n          <div class=\"flex pt-6 justify-end\">\n            <Button\n              :label=\"$t('g.next')\"\n              icon=\"pi pi-arrow-right\"\n              icon-pos=\"right\"\n              :disabled=\"typeof device !== 'string'\"\n              @click=\"activateCallback('1')\"\n            />\n          </div>\n        </StepPanel>\n        <StepPanel v-slot=\"{ activateCallback }\" value=\"1\">\n          <InstallLocationPicker\n            v-model:installPath=\"installPath\"\n            v-model:pathError=\"pathError\"\n          />\n          <div class=\"flex pt-6 justify-between\">\n            <Button\n              :label=\"$t('g.back')\"\n              severity=\"secondary\"\n              icon=\"pi pi-arrow-left\"\n              @click=\"activateCallback('0')\"\n            />\n            <Button\n              :label=\"$t('g.next')\"\n              icon=\"pi pi-arrow-right\"\n              icon-pos=\"right\"\n              :disabled=\"pathError !== ''\"\n              @click=\"activateCallback('2')\"\n            />\n          </div>\n        </StepPanel>\n        <StepPanel v-slot=\"{ activateCallback }\" value=\"2\">\n          <MigrationPicker\n            v-model:sourcePath=\"migrationSourcePath\"\n            v-model:migrationItemIds=\"migrationItemIds\"\n          />\n          <div class=\"flex pt-6 justify-between\">\n            <Button\n              :label=\"$t('g.back')\"\n              severity=\"secondary\"\n              icon=\"pi pi-arrow-left\"\n              @click=\"activateCallback('1')\"\n            />\n            <Button\n              :label=\"$t('g.next')\"\n              icon=\"pi pi-arrow-right\"\n              icon-pos=\"right\"\n              @click=\"activateCallback('3')\"\n            />\n          </div>\n        </StepPanel>\n        <StepPanel v-slot=\"{ activateCallback }\" value=\"3\">\n          <DesktopSettingsConfiguration\n            v-model:autoUpdate=\"autoUpdate\"\n            v-model:allowMetrics=\"allowMetrics\"\n          />\n          <MirrorsConfiguration\n            v-model:pythonMirror=\"pythonMirror\"\n            v-model:pypiMirror=\"pypiMirror\"\n            v-model:torchMirror=\"torchMirror\"\n            :device=\"device\"\n            class=\"mt-6\"\n          />\n          <div class=\"flex mt-6 justify-between\">\n            <Button\n              :label=\"$t('g.back')\"\n              severity=\"secondary\"\n              icon=\"pi pi-arrow-left\"\n              @click=\"activateCallback('2')\"\n            />\n            <Button\n              :label=\"$t('g.install')\"\n              icon=\"pi pi-check\"\n              icon-pos=\"right\"\n              :disabled=\"hasError\"\n              @click=\"install()\"\n            />\n          </div>\n        </StepPanel>\n      </StepPanels>\n    </Stepper>\n  </BaseViewTemplate>\n</template>\n\n<script setup lang=\"ts\">\nimport type {\n  InstallOptions,\n  TorchDeviceType\n} from '@comfyorg/comfyui-electron-types'\nimport Button from 'primevue/button'\nimport Step from 'primevue/step'\nimport StepList from 'primevue/steplist'\nimport StepPanel from 'primevue/steppanel'\nimport StepPanels from 'primevue/steppanels'\nimport Stepper from 'primevue/stepper'\nimport { computed, onMounted, ref, toRaw } from 'vue'\nimport { useRouter } from 'vue-router'\n\nimport DesktopSettingsConfiguration from '@/components/install/DesktopSettingsConfiguration.vue'\nimport GpuPicker from '@/components/install/GpuPicker.vue'\nimport InstallLocationPicker from '@/components/install/InstallLocationPicker.vue'\nimport MigrationPicker from '@/components/install/MigrationPicker.vue'\nimport MirrorsConfiguration from '@/components/install/MirrorsConfiguration.vue'\nimport { electronAPI } from '@/utils/envUtil'\nimport BaseViewTemplate from '@/views/templates/BaseViewTemplate.vue'\n\nconst device = ref<TorchDeviceType | null>(null)\n\nconst installPath = ref('')\nconst pathError = ref('')\n\nconst migrationSourcePath = ref('')\nconst migrationItemIds = ref<string[]>([])\n\nconst autoUpdate = ref(true)\nconst allowMetrics = ref(true)\nconst pythonMirror = ref('')\nconst pypiMirror = ref('')\nconst torchMirror = ref('')\n\n/** Forces each install step to be visited at least once. */\nconst highestStep = ref(0)\n\nconst handleStepChange = (value: string | number) => {\n  setHighestStep(value)\n\n  electronAPI().Events.trackEvent('install_stepper_change', {\n    step: value\n  })\n}\n\nconst setHighestStep = (value: string | number) => {\n  const int = typeof value === 'number' ? value : parseInt(value, 10)\n  if (!isNaN(int) && int > highestStep.value) highestStep.value = int\n}\n\nconst hasError = computed(() => pathError.value !== '')\nconst noGpu = computed(() => typeof device.value !== 'string')\n\nconst electron = electronAPI()\nconst router = useRouter()\nconst install = async () => {\n  const options: InstallOptions = {\n    installPath: installPath.value,\n    autoUpdate: autoUpdate.value,\n    allowMetrics: allowMetrics.value,\n    migrationSourcePath: migrationSourcePath.value,\n    migrationItemIds: toRaw(migrationItemIds.value),\n    pythonMirror: pythonMirror.value,\n    pypiMirror: pypiMirror.value,\n    torchMirror: torchMirror.value,\n    // @ts-expect-error fixme ts strict error\n    device: device.value\n  }\n  electron.installComfyUI(options)\n\n  const nextPage =\n    options.device === 'unsupported' ? '/manual-configuration' : '/server-start'\n  await router.push(nextPage)\n}\n\nonMounted(async () => {\n  if (!electron) return\n\n  const detectedGpu = await electron.Config.getDetectedGpu()\n  if (detectedGpu === 'mps' || detectedGpu === 'nvidia') {\n    device.value = detectedGpu\n  }\n\n  electronAPI().Events.trackEvent('install_stepper_change', {\n    step: '0',\n    gpu: detectedGpu\n  })\n})\n</script>\n\n<style scoped>\n:deep(.p-steppanel) {\n  @apply bg-transparent;\n}\n</style>\n"], "names": ["_useModel", "t", "sourcePath"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIM,UAAA,aAAa,IAAI,KAAK;AACtB,UAAA,aAAaA,kBAAqB,YAAgC;AAClE,UAAA,eAAeA,SAAoB,SAAC,cAAkC;AAE5E,UAAM,kBAAkB,6BAAM;AAC5B,iBAAW,QAAQ;AAAA,IAAA,GADG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvIxB,MAAe,aAAA,KAAA,IAAA,IAAA,0BAAA,YAAA,GAAA,EAAA;ACAf,MAAe,aAAA,KAAA,IAAA,IAAA,6BAAA,YAAA,GAAA,EAAA;ACAf,MAAe,aAAA,KAAA,IAAA,IAAA,mCAAA,YAAA,GAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4IT,UAAA,EAAE,GAAAC,OAAM;AAEd,UAAM,UAAU,SAAS;AAAA,MACvB,KAAK,6BAAM,SAAS,UAAU,OAAzB;AAAA,MACL,KAAK,wBAAC,UAAU;AACL,iBAAA,QAAQ,QAAQ,QAAQ;AAAA,MACnC,GAFK;AAAA,IAEL,CACD;AACK,UAAA,WAAWD,SAAmC,SAAC,QAEpD;AAED,UAAM,WAAW;AACX,UAAA,WAAW,SAAS;AAEpB,UAAA,UAAU,wBAAC,UAAiC;AAChD,YAAM,WAAW,SAAS,UAAU,QAAQ,OAAO;AACnD,eAAS,QAAQ;AAAA,IAAA,GAFH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3EV,UAAA,EAAE,GAAAC,OAAM;AAER,UAAA,cAAcD,kBAAoB,aAAiC;AACnE,UAAA,YAAYA,SAAoB,SAAA,WAA+B;AAC/D,UAAA,aAAa,IAAI,KAAK;AACtB,UAAA,kBAAkB,IAAI,KAAK;AAC3B,UAAA,UAAU,IAAI,EAAE;AAChB,UAAA,UAAU,IAAI,EAAE;AAChB,UAAA,eAAe,IAAI,KAAK;AAE9B,UAAM,WAAW;AAGjB,cAAU,YAAY;AACd,YAAA,QAAQ,MAAM,SAAS;AAC7B,cAAQ,QAAQ,MAAM;AACtB,cAAQ,QAAQ,MAAM;AACtB,kBAAY,QAAQ,MAAM;AAEpB,YAAA,aAAa,MAAM,kBAAkB;AAAA,IAAA,CAC5C;AAEK,UAAA,eAAe,8BAAO,SAA6B;AACnD,UAAA;AACF,kBAAU,QAAQ;AAClB,mBAAW,QAAQ;AACnB,wBAAgB,QAAQ;AACxB,cAAM,aAAa,MAAM,SAAS,oBAAoB,QAAQ,EAAE;AAG5D,YAAA,CAAC,WAAW,SAAS;AACvB,gBAAM,SAAmB,CAAA;AACzB,cAAI,WAAW,YAAa,QAAO,KAAKC,GAAE,qBAAqB,CAAC;AAC5D,cAAA,WAAW,YAAY,WAAW,eAAe;AACnD,kBAAM,aAAa,WAAW,gBAAgB,OAAO,OAAO;AAC5D,mBAAO,KAAK,GAAGA,GAAE,+BAA+B,CAAC,KAAK,UAAU,KAAK;AAAA,UACvE;AACA,cAAI,WAAW,cAAe,QAAO,KAAKA,GAAE,uBAAuB,CAAC;AACpE,cAAI,WAAW,WAAY,QAAO,KAAKA,GAAE,oBAAoB,CAAC;AAE9D,cAAI,WAAW;AACN,mBAAA,KAAK,GAAGA,GAAE,wBAAwB,CAAC,KAAK,WAAW,KAAK,EAAE;AACzD,oBAAA,QAAQ,OAAO,KAAK,IAAI;AAAA,QACpC;AAEI,YAAA,WAAW,kBAAmB,iBAAgB,QAAQ;AACtD,YAAA,WAAW,OAAQ,YAAW,QAAQ;AAAA,eACnC,OAAO;AACJ,kBAAA,QAAQA,GAAE,8BAA8B;AAAA,MACpD;AAAA,IAAA,GA3BmB;AA8BrB,UAAM,aAAa,mCAAY;AACzB,UAAA;AACI,cAAA,SAAS,MAAM,SAAS;AAC9B,YAAI,QAAQ;AACV,sBAAY,QAAQ;AACpB,gBAAM,aAAa,MAAM;AAAA,QAC3B;AAAA,eACO,OAAO;AACJ,kBAAA,QAAQA,GAAE,iCAAiC;AAAA,MACvD;AAAA,IAAA,GATiB;AAYnB,UAAM,UAAU,mCAAY;AACtB,UAAA,CAAC,aAAa,OAAO;AACvB,qBAAa,QAAQ;AACrB;AAAA,MACF;AAEM,YAAA,aAAa,YAAY,KAAK;AAAA,IAAA,GANtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChEV,UAAA,EAAE,GAAAA,OAAM;AAEd,UAAM,WAAW;AAEX,UAAA,aAAaD,kBAAoB,YAAiC;AAClE,UAAA,mBAAmBA,SAAqB,SAAC,kBAE9C;AAED,UAAM,iBAAiB;AAAA,MACrB,eAAe,IAAI,CAAC,UAAU;AAAA,QAC5B,GAAG;AAAA,QACH,UAAU;AAAA,MAAA,EACV;AAAA,IAAA;AAGE,UAAA,YAAY,IAAI,EAAE;AACxB,UAAM,gBAAgB;AAAA,MACpB,MAAM,WAAW,UAAU,MAAM,UAAU,UAAU;AAAA,IAAA;AAGjD,UAAA,iBAAiB,8BAAOE,gBAAmC;AAC/D,UAAI,CAACA,aAAY;AACf,kBAAU,QAAQ;AAClB;AAAA,MACF;AAEI,UAAA;AACF,kBAAU,QAAQ;AAClB,cAAM,aAAa,MAAM,SAAS,sBAAsBA,WAAU;AAElE,YAAI,CAAC,WAAW,QAAmB,WAAA,QAAQ,WAAW,SAAS;AAAA,eACxD,OAAO;AACd,gBAAQ,MAAM,KAAK;AACT,kBAAA,QAAQD,GAAE,8BAA8B;AAAA,MACpD;AAAA,IAAA,GAdqB;AAiBvB,UAAM,aAAa,mCAAY;AACzB,UAAA;AACI,cAAA,SAAS,MAAM,SAAS;AAC9B,YAAI,QAAQ;AACV,qBAAW,QAAQ;AACnB,gBAAM,eAAe,MAAM;AAAA,QAC7B;AAAA,eACO,OAAO;AACd,gBAAQ,MAAM,KAAK;AACT,kBAAA,QAAQA,GAAE,iCAAiC;AAAA,MACvD;AAAA,IAAA,GAViB;AAanB,gBAAY,MAAM;AAChB,uBAAiB,QAAQ,eAAe,MACrC,OAAO,CAAC,SAAS,KAAK,QAAQ,EAC9B,IAAI,CAAC,SAAS,KAAK,EAAE;AAAA,IAAA,CACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrGD,UAAM,OAAO;AAIP,UAAA,aAAaD,kBAAoB,YAAgC;AACjE,UAAA,kBAAkB,IAAqB,gBAAgB,IAAI;AAE3D,UAAA,sBAAsB,SAAS,MAAM;AAClC,aAAA,iBAAiB,QAAI,KAAC,SAAS;AAAA,IAAA,CACvC;AAED,cAAU,MAAM;AACH,iBAAA,QAAQ,QAAI,KAAC;AAAA,IAAA,CACzB;AAEK,UAAA,iBAAiB,CAAC,aAAa;AACnC,WAAK,gBAAgB,QAAQ;AAG7B,UACE,aAAa,gBAAgB,WAC7B,WAAW,UAAU,QAAA,KAAK,QAC1B;AACW,mBAAA,QAAQ,QAAA,KAAK;AAAA,MAC1B;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTK,UAAA,mBAAmB,IAAI,KAAK;AAE5B,UAAA,eAAeA,SAAmB,SAAC,cAAkC;AACrE,UAAA,aAAaA,kBAAoB,YAAgC;AACjE,UAAA,cAAcA,kBAAoB,aAAiC;AAEnE,UAAA,qBAAqB,wBAAC,WAAsC;AAChE,YAAM,YAAY;AAClB,cAAQ,QAAQ;AAAA,QACd,KAAK;AACI,iBAAA;AAAA,YACL;AAAA,YACA,QAAQ,eAAe;AAAA,YACvB,gBAAgB,eAAe;AAAA,UAAA;AAAA,QAEnC,KAAK;AACI,iBAAA;AAAA,YACL;AAAA,YACA,QAAQ,eAAe;AAAA,YACvB,gBAAgB,eAAe;AAAA,UAAA;AAAA,QAEnC,KAAK;AAAA,QACL;AACS,iBAAA;AAAA,YACL;AAAA,YACA,QAAQ,YAAY;AAAA,YACpB,gBAAgB,YAAY;AAAA,UAAA;AAAA,MAElC;AAAA,IAAA,GAtByB;AAyBrB,UAAA,gBAAgB,IAAI,KAAK;AAC/B,cAAU,YAAY;AACN,oBAAA,QAAQ,MAAM;IAAU,CACvC;AAEK,UAAA,oBAAoB,wBAAC,YAAsB;AAAA,MAC/C,GAAG;AAAA,MACH,QAAQ,OAAO;AAAA,IAAA,IAFS;AAK1B,UAAM,UAAU;AAAA,MAAyC,MAErD;AAAA,QACE,CAAC,eAAe,YAAY;AAAA,QAC5B,CAAC,aAAa,UAAU;AAAA,QACxB,CAAC,mBAAmB,QAAM,UAAI,KAAK,GAAG,WAAW;AAAA,QAEnD,IAAI,CAAC,CAAC,MAAM,UAAU,MAAM;AAAA,QAC5B,cAAc,QAAQ,kBAAkB,IAAI,IAAI;AAAA,QAChD;AAAA,MAAA,CACD;AAAA,IAAA;AAGH,UAAM,mBAAmB;AAAA,MACvB,QAAQ,MAAM,IAAI,MAAM,gBAAgB,IAAI;AAAA,IAAA;AAExC,UAAA,kBAAkB,SAAS,MAAM;AAC9B,aAAA,sBAAsB,iBAAiB,KAAK;AAAA,IAAA,CACpD;AACK,UAAA,yBAAyB,SAAS,MAAM;AAC5C,cAAQ,gBAAgB,OAAO;AAAA,QAC7B,KAAK,gBAAgB;AACnB,iBAAO,EAAE,qCAAqC;AAAA,QAChD,KAAK,gBAAgB;AACnB,iBAAO,EAAE,mCAAmC;AAAA,QAC9C;AACE,iBAAO,EAAE,kCAAkC;AAAA,MAC/C;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACcK,UAAA,SAAS,IAA4B,IAAI;AAEzC,UAAA,cAAc,IAAI,EAAE;AACpB,UAAA,YAAY,IAAI,EAAE;AAElB,UAAA,sBAAsB,IAAI,EAAE;AAC5B,UAAA,mBAAmB,IAAc,CAAA,CAAE;AAEnC,UAAA,aAAa,IAAI,IAAI;AACrB,UAAA,eAAe,IAAI,IAAI;AACvB,UAAA,eAAe,IAAI,EAAE;AACrB,UAAA,aAAa,IAAI,EAAE;AACnB,UAAA,cAAc,IAAI,EAAE;AAGpB,UAAA,cAAc,IAAI,CAAC;AAEnB,UAAA,mBAAmB,wBAAC,UAA2B;AACnD,qBAAe,KAAK;AAER,oBAAE,OAAO,WAAW,0BAA0B;AAAA,QACxD,MAAM;AAAA,MAAA,CACP;AAAA,IAAA,GALsB;AAQnB,UAAA,iBAAiB,wBAAC,UAA2B;AACjD,YAAM,MAAM,OAAO,UAAU,WAAW,QAAQ,SAAS,OAAO,EAAE;AAC9D,UAAA,CAAC,MAAM,GAAG,KAAK,MAAM,YAAY,mBAAmB,QAAQ;AAAA,IAAA,GAF3C;AAKvB,UAAM,WAAW,SAAS,MAAM,UAAU,UAAU,EAAE;AACtD,UAAM,QAAQ,SAAS,MAAM,OAAO,OAAO,UAAU,QAAQ;AAE7D,UAAM,WAAW;AACjB,UAAM,SAAS;AACf,UAAM,UAAU,mCAAY;AAC1B,YAAM,UAA0B;AAAA,QAC9B,aAAa,YAAY;AAAA,QACzB,YAAY,WAAW;AAAA,QACvB,cAAc,aAAa;AAAA,QAC3B,qBAAqB,oBAAoB;AAAA,QACzC,kBAAkB,MAAM,iBAAiB,KAAK;AAAA,QAC9C,cAAc,aAAa;AAAA,QAC3B,YAAY,WAAW;AAAA,QACvB,aAAa,YAAY;AAAA;AAAA,QAEzB,QAAQ,OAAO;AAAA,MAAA;AAEjB,eAAS,eAAe,OAAO;AAE/B,YAAM,WACJ,QAAQ,WAAW,gBAAgB,0BAA0B;AACzD,YAAA,OAAO,KAAK,QAAQ;AAAA,IAAA,GAjBZ;AAoBhB,cAAU,YAAY;AACpB,UAAI,CAAC,SAAU;AAEf,YAAM,cAAc,MAAM,SAAS,OAAO,eAAe;AACrD,UAAA,gBAAgB,SAAS,gBAAgB,UAAU;AACrD,eAAO,QAAQ;AAAA,MACjB;AAEY,oBAAE,OAAO,WAAW,0BAA0B;AAAA,QACxD,MAAM;AAAA,QACN,KAAK;AAAA,MAAA,CACN;AAAA,IAAA,CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}