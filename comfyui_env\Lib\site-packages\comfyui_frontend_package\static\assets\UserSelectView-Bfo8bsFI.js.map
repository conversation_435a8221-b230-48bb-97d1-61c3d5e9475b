{"version": 3, "file": "UserSelectView-Bfo8bsFI.js", "sources": ["../../src/views/UserSelectView.vue"], "sourcesContent": ["<template>\n  <BaseViewTemplate dark>\n    <main\n      id=\"comfy-user-selection\"\n      class=\"min-w-84 relative rounded-lg bg-[var(--comfy-menu-bg)] p-5 px-10 shadow-lg\"\n    >\n      <h1 class=\"my-2.5 mb-7 font-normal\">ComfyUI</h1>\n      <div class=\"flex w-full flex-col items-center\">\n        <div class=\"flex w-full flex-col gap-2\">\n          <label for=\"new-user-input\">{{ $t('userSelect.newUser') }}:</label>\n          <InputText\n            id=\"new-user-input\"\n            v-model=\"newUsername\"\n            :placeholder=\"$t('userSelect.enterUsername')\"\n            @keyup.enter=\"login\"\n          />\n        </div>\n        <Divider />\n        <div class=\"flex w-full flex-col gap-2\">\n          <label for=\"existing-user-select\"\n            >{{ $t('userSelect.existingUser') }}:</label\n          >\n          <Select\n            v-model=\"selectedUser\"\n            class=\"w-full\"\n            input-id=\"existing-user-select\"\n            :options=\"userStore.users\"\n            option-label=\"username\"\n            :placeholder=\"$t('userSelect.selectUser')\"\n            :disabled=\"createNewUser\"\n          />\n          <Message v-if=\"error\" severity=\"error\">\n            {{ error }}\n          </Message>\n        </div>\n        <footer class=\"mt-5\">\n          <Button :label=\"$t('userSelect.next')\" @click=\"login\" />\n        </footer>\n      </div>\n    </main>\n  </BaseViewTemplate>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport Divider from 'primevue/divider'\nimport InputText from 'primevue/inputtext'\nimport Message from 'primevue/message'\nimport Select from 'primevue/select'\nimport { computed, onMounted, ref } from 'vue'\nimport { useRouter } from 'vue-router'\n\nimport { User, useUserStore } from '@/stores/userStore'\nimport BaseViewTemplate from '@/views/templates/BaseViewTemplate.vue'\n\nconst userStore = useUserStore()\nconst router = useRouter()\n\nconst selectedUser = ref<User | null>(null)\nconst newUsername = ref('')\nconst loginError = ref('')\n\nconst createNewUser = computed(() => newUsername.value.trim() !== '')\nconst newUserExistsError = computed(() => {\n  return userStore.users.find((user) => user.username === newUsername.value)\n    ? `User \"${newUsername.value}\" already exists`\n    : ''\n})\nconst error = computed(() => newUserExistsError.value || loginError.value)\n\nconst login = async () => {\n  try {\n    const user = createNewUser.value\n      ? await userStore.createUser(newUsername.value)\n      : selectedUser.value\n\n    if (!user) {\n      throw new Error('No user selected')\n    }\n\n    await userStore.login(user)\n    await router.push('/')\n  } catch (err) {\n    loginError.value = err instanceof Error ? err.message : JSON.stringify(err)\n  }\n}\n\nonMounted(async () => {\n  if (!userStore.initialized) {\n    await userStore.initialize()\n  }\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAuDA,UAAM,YAAY;AAClB,UAAM,SAAS;AAET,UAAA,eAAe,IAAiB,IAAI;AACpC,UAAA,cAAc,IAAI,EAAE;AACpB,UAAA,aAAa,IAAI,EAAE;AAEzB,UAAM,gBAAgB,SAAS,MAAM,YAAY,MAAM,KAAA,MAAW,EAAE;AAC9D,UAAA,qBAAqB,SAAS,MAAM;AACxC,aAAO,UAAU,MAAM,KAAK,CAAC,SAAS,KAAK,aAAa,YAAY,KAAK,IACrE,SAAS,YAAY,KAAK,qBAC1B;AAAA,IAAA,CACL;AACD,UAAM,QAAQ,SAAS,MAAM,mBAAmB,SAAS,WAAW,KAAK;AAEzE,UAAM,QAAQ,mCAAY;AACpB,UAAA;AACI,cAAA,OAAO,cAAc,QACvB,MAAM,UAAU,WAAW,YAAY,KAAK,IAC5C,aAAa;AAEjB,YAAI,CAAC,MAAM;AACH,gBAAA,IAAI,MAAM,kBAAkB;AAAA,QACpC;AAEM,cAAA,UAAU,MAAM,IAAI;AACpB,cAAA,OAAO,KAAK,GAAG;AAAA,eACd,KAAK;AACZ,mBAAW,QAAQ,eAAe,QAAQ,IAAI,UAAU,KAAK,UAAU,GAAG;AAAA,MAC5E;AAAA,IAAA,GAdY;AAiBd,cAAU,YAAY;AAChB,UAAA,CAAC,UAAU,aAAa;AAC1B,cAAM,UAAU;MAClB;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}