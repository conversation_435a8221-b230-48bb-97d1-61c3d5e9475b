# ComfyUI 工作流配置指南

## 📋 您当前拥有的模型

### ✅ 可用模型
1. **Stable Diffusion 1.5**: `v1-5-pruned-emaonly.safetensors`
2. **Flux Dev UNet**: `flux1-dev-fp8.safetensors`
3. **CLIP-L**: `clip_l.safetensors`
4. **T5-XXL**: `t5xxl_fp8_e4m3fn.safetensors`

### ❌ 缺少的模型
- **Flux VAE**: `ae.safetensors` (Flux 工作流需要)

## 🚀 推荐工作流

### 工作流 1: SD1.5 基础工作流 ⭐ (推荐开始)

**文件**: `workflows/sd15_basic.json`

**特点**:
- ✅ 使用您现有的 SD1.5 模型
- ✅ 无需额外下载
- ✅ 适合初学者
- ✅ 生成速度快

**使用方法**:
1. 拖拽 `workflows/sd15_basic.json` 到 ComfyUI 界面
2. 修改提示词
3. 点击 "Queue Prompt" 生成

**参数说明**:
- **分辨率**: 512x512 (SD1.5 最佳)
- **步数**: 25 (平衡质量和速度)
- **CFG**: 7.5 (引导强度)
- **采样器**: euler_a (快速且质量好)

### 工作流 2: Flux 高级工作流 🔥 (需要额外下载)

**文件**: `workflows/flux_basic.json`

**特点**:
- 🔥 最新的 Flux 模型
- 🎨 更高质量的图像
- 📐 支持更大分辨率 (1024x1024)
- ⚡ 需要下载 Flux VAE

**缺少的文件**:
需要下载 Flux VAE: `ae.safetensors`
- 下载地址: https://huggingface.co/black-forest-labs/FLUX.1-dev/resolve/main/ae.safetensors
- 放置位置: `models/vae/ae.safetensors`

## 🎯 立即可用的工作流

### 使用 SD1.5 工作流 (推荐)

1. **导入工作流**:
   ```
   拖拽 workflows/sd15_basic.json 到 ComfyUI 界面
   ```

2. **修改提示词**:
   - **正面提示**: 描述您想要的图像
   - **负面提示**: 描述不想要的内容

3. **调整参数**:
   - **种子**: 改变数值获得不同结果
   - **步数**: 20-30 (更多步数 = 更好质量，但更慢)
   - **CFG**: 7-12 (更高 = 更严格遵循提示词)

4. **生成图像**:
   点击 "Queue Prompt" 按钮

## 📝 示例提示词

### 风景类
```
正面: "a beautiful mountain landscape at sunset, golden hour lighting, highly detailed, photorealistic, 8k"
负面: "blurry, low quality, bad composition, overexposed"
```

### 人物类
```
正面: "portrait of a young woman, natural lighting, detailed face, professional photography"
负面: "blurry, distorted face, bad anatomy, low quality"
```

### 艺术类
```
正面: "digital art, fantasy castle, magical atmosphere, vibrant colors, detailed architecture"
负面: "blurry, low resolution, bad perspective, ugly"
```

## ⚙️ 性能优化建议

### 针对您的 RTX 4070 (8GB VRAM)

1. **SD1.5 模型** (推荐):
   - 分辨率: 512x512 或 768x768
   - 批量大小: 1-2
   - 无需特殊设置

2. **如果使用 Flux** (需要更多显存):
   ```bash
   # 启动时使用低显存模式
   python main.py --lowvram
   ```

3. **显存不足时**:
   ```bash
   # 极低显存模式
   python main.py --novram
   
   # 或使用 CPU VAE
   python main.py --cpu-vae
   ```

## 🔄 工作流切换

### 在 ComfyUI 中切换工作流:
1. 点击 "Clear" 清空当前工作流
2. 拖拽新的 `.json` 文件到界面
3. 确认加载

### 保存自定义工作流:
1. 调整好参数后点击 "Save"
2. 选择保存位置
3. 输入文件名 (如 `my_custom_workflow.json`)

## 📦 扩展模型下载 (可选)

### 如果想使用 Flux 工作流:
```bash
# 下载 Flux VAE
# 访问: https://huggingface.co/black-forest-labs/FLUX.1-dev/resolve/main/ae.safetensors
# 保存到: models/vae/ae.safetensors
```

### 其他推荐模型:
1. **更好的 VAE** (提升图像质量):
   - `vae-ft-mse-840000-ema-pruned.safetensors`
   - 放置: `models/vae/`

2. **LoRA 模型** (风格调整):
   - 下载各种风格 LoRA
   - 放置: `models/loras/`

## 🎉 开始创作

现在您可以:
1. ✅ 使用 SD1.5 工作流立即开始创作
2. 🔄 尝试不同的提示词和参数
3. 📁 查看 `output/` 目录中的生成结果
4. 🎨 探索更多工作流和模型

祝您创作愉快！🎨✨
