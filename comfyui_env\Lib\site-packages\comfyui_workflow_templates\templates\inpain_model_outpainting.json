{"last_node_id": 34, "last_link_id": 89, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [940, 180], "size": [315, 474], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 80}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 72}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [42]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": [476514576444494, "randomize", 20, 7, "dpmpp_2m", "karras", 1, ""]}, {"id": 6, "type": "CLIPTextEncode", "pos": [432, 158], "size": [422.81243896484375, 161.2169189453125], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 81}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["a close-up of a delicate pink rose with velvety petals,reflecting soft ambient light,Dark green-toned light\n\nThe background consists of blurred pink roses and green foliage, creating a dreamy and harmonious depth. \n\n(soft lighting, dim background, cinematic lighting, realistic shading, gentle contrast, warm tones), "]}, {"id": 7, "type": "CLIPTextEncode", "pos": [434, 371], "size": [425.2799987792969, 180.61000061035156], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 82}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["watermark, text"]}, {"id": 8, "type": "VAEDecode", "pos": [1422, 387], "size": [210, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 42}, {"name": "vae", "type": "VAE", "link": 83}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [22]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1671, 384], "size": [360.54998779296875, 441.5299987792969], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 22}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["ComfyUI", ""]}, {"id": 20, "type": "LoadImage", "pos": [-115.77914428710938, 726.8905029296875], "size": [344, 346], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [85]}, {"name": "MASK", "type": "MASK", "slot_index": 1, "links": []}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["input (1).png", "image", ""]}, {"id": 26, "type": "VAEEncodeForInpaint", "pos": [617, 720], "size": [226.8000030517578, 98], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 87}, {"name": "vae", "type": "VAE", "link": 84}, {"name": "mask", "type": "MASK", "link": 86}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [72]}], "properties": {"Node name for S&R": "VAEEncodeForInpaint", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": [10]}, {"id": 29, "type": "CheckpointLoaderSimple", "pos": [17, 303], "size": [315, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [80]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [81, 82]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [83, 84]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "cnr_id": "comfy-core", "ver": "0.3.18", "models": [{"name": "512-inpainting-ema.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-2-inpainting/resolve/main/512-inpainting-ema.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["512-inpainting-ema.safetensors"]}, {"id": 30, "type": "ImagePadForOutpaint", "pos": [252.03269958496094, 726.62841796875], "size": [315, 174], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 85}], "outputs": [{"name": "IMAGE", "shape": 3, "type": "IMAGE", "slot_index": 0, "links": [87]}, {"name": "MASK", "shape": 3, "type": "MASK", "slot_index": 1, "links": [86]}], "properties": {"Node name for S&R": "ImagePadForOutpaint", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": [40, 40, 40, 128, 10]}, {"id": 34, "type": "<PERSON>downNote", "pos": [16, 448], "size": [336, 128], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [Outpaint - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/inpaint/#outpainting) — Overview\n> \n> [Outpainting - docs.comfy.org](https://docs.comfy.org/tutorials/basic/outpaint) — Explanation of concepts and step-by-step tutorial"], "color": "#432", "bgcolor": "#653"}], "links": [[4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [22, 8, 0, 9, 0, "IMAGE"], [42, 3, 0, 8, 0, "LATENT"], [72, 26, 0, 3, 3, "LATENT"], [80, 29, 0, 3, 0, "MODEL"], [81, 29, 1, 6, 0, "CLIP"], [82, 29, 1, 7, 0, "CLIP"], [83, 29, 2, 8, 1, "VAE"], [84, 29, 2, 26, 1, "VAE"], [85, 20, 0, 30, 0, "IMAGE"], [86, 30, 1, 26, 2, "MASK"], [87, 30, 0, 26, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Load image and pad for outpainting", "bounding": [-120, 600, 1038, 509], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {}, "version": 0.4}