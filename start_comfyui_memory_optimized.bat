@echo off
echo Starting ComfyUI - Memory Optimized GPU Mode...
echo.

REM 激活虚拟环境
call comfyui_env\Scripts\activate.bat

REM 显示 GPU 信息
echo Virtual environment activated.
echo Checking GPU status...
python -c "import torch; print('GPU Available:', torch.cuda.is_available()); print('GPU Name:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'No GPU'); print('VRAM Total:', round(torch.cuda.get_device_properties(0).total_memory/1024**3, 1), 'GB' if torch.cuda.is_available() else '')"
echo.

REM 启动 ComfyUI (内存优化模式)
echo Starting ComfyUI server in MEMORY OPTIMIZED mode...
echo Using --lowvram --cpu-vae for maximum memory efficiency
echo Best for large batch generation and SD3.5 Large model
echo.
echo ComfyUI will be available at: http://127.0.0.1:8188
echo Press Ctrl+C to stop the server.
echo.

python main.py --lowvram --cpu-vae --auto-launch

pause
