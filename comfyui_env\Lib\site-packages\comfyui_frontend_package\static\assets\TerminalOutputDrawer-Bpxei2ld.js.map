{"version": 3, "file": "TerminalOutputDrawer-Bpxei2ld.js", "sources": ["../../node_modules/@xterm/addon-serialize/lib/addon-serialize.js", "../../src/composables/bottomPanelTabs/useTerminalBuffer.ts", "../../src/components/maintenance/TerminalOutputDrawer.vue"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define([],t):\"object\"==typeof exports?exports.SerializeAddon=t():e.SerializeAddon=t()}(this,(()=>(()=>{\"use strict\";var e={930:(e,t,s)=>{Object.defineProperty(t,\"__esModule\",{value:!0}),t.ColorContrastCache=void 0;const r=s(485);t.ColorContrastCache=class{constructor(){this._color=new r.TwoKeyMap,this._css=new r.TwoKeyMap}setCss(e,t,s){this._css.set(e,t,s)}getCss(e,t){return this._css.get(e,t)}setColor(e,t,s){this._color.set(e,t,s)}getColor(e,t){return this._color.get(e,t)}clear(){this._color.clear(),this._css.clear()}}},997:function(e,t,s){var r=this&&this.__decorate||function(e,t,s,r){var o,i=arguments.length,n=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,s):r;if(\"object\"==typeof Reflect&&\"function\"==typeof Reflect.decorate)n=Reflect.decorate(e,t,s,r);else for(var l=e.length-1;l>=0;l--)(o=e[l])&&(n=(i<3?o(n):i>3?o(t,s,n):o(t,s))||n);return i>3&&n&&Object.defineProperty(t,s,n),n},o=this&&this.__param||function(e,t){return function(s,r){t(s,r,e)}};Object.defineProperty(t,\"__esModule\",{value:!0}),t.ThemeService=t.DEFAULT_ANSI_COLORS=void 0;const i=s(930),n=s(160),l=s(345),a=s(859),c=s(97),h=n.css.toColor(\"#ffffff\"),u=n.css.toColor(\"#000000\"),_=n.css.toColor(\"#ffffff\"),d=n.css.toColor(\"#000000\"),C={css:\"rgba(255, 255, 255, 0.3)\",rgba:4294967117};t.DEFAULT_ANSI_COLORS=Object.freeze((()=>{const e=[n.css.toColor(\"#2e3436\"),n.css.toColor(\"#cc0000\"),n.css.toColor(\"#4e9a06\"),n.css.toColor(\"#c4a000\"),n.css.toColor(\"#3465a4\"),n.css.toColor(\"#75507b\"),n.css.toColor(\"#06989a\"),n.css.toColor(\"#d3d7cf\"),n.css.toColor(\"#555753\"),n.css.toColor(\"#ef2929\"),n.css.toColor(\"#8ae234\"),n.css.toColor(\"#fce94f\"),n.css.toColor(\"#729fcf\"),n.css.toColor(\"#ad7fa8\"),n.css.toColor(\"#34e2e2\"),n.css.toColor(\"#eeeeec\")],t=[0,95,135,175,215,255];for(let s=0;s<216;s++){const r=t[s/36%6|0],o=t[s/6%6|0],i=t[s%6];e.push({css:n.channels.toCss(r,o,i),rgba:n.channels.toRgba(r,o,i)})}for(let t=0;t<24;t++){const s=8+10*t;e.push({css:n.channels.toCss(s,s,s),rgba:n.channels.toRgba(s,s,s)})}return e})());let f=t.ThemeService=class extends a.Disposable{get colors(){return this._colors}constructor(e){super(),this._optionsService=e,this._contrastCache=new i.ColorContrastCache,this._halfContrastCache=new i.ColorContrastCache,this._onChangeColors=this.register(new l.EventEmitter),this.onChangeColors=this._onChangeColors.event,this._colors={foreground:h,background:u,cursor:_,cursorAccent:d,selectionForeground:void 0,selectionBackgroundTransparent:C,selectionBackgroundOpaque:n.color.blend(u,C),selectionInactiveBackgroundTransparent:C,selectionInactiveBackgroundOpaque:n.color.blend(u,C),ansi:t.DEFAULT_ANSI_COLORS.slice(),contrastCache:this._contrastCache,halfContrastCache:this._halfContrastCache},this._updateRestoreColors(),this._setTheme(this._optionsService.rawOptions.theme),this.register(this._optionsService.onSpecificOptionChange(\"minimumContrastRatio\",(()=>this._contrastCache.clear()))),this.register(this._optionsService.onSpecificOptionChange(\"theme\",(()=>this._setTheme(this._optionsService.rawOptions.theme))))}_setTheme(e={}){const s=this._colors;if(s.foreground=g(e.foreground,h),s.background=g(e.background,u),s.cursor=g(e.cursor,_),s.cursorAccent=g(e.cursorAccent,d),s.selectionBackgroundTransparent=g(e.selectionBackground,C),s.selectionBackgroundOpaque=n.color.blend(s.background,s.selectionBackgroundTransparent),s.selectionInactiveBackgroundTransparent=g(e.selectionInactiveBackground,s.selectionBackgroundTransparent),s.selectionInactiveBackgroundOpaque=n.color.blend(s.background,s.selectionInactiveBackgroundTransparent),s.selectionForeground=e.selectionForeground?g(e.selectionForeground,n.NULL_COLOR):void 0,s.selectionForeground===n.NULL_COLOR&&(s.selectionForeground=void 0),n.color.isOpaque(s.selectionBackgroundTransparent)){const e=.3;s.selectionBackgroundTransparent=n.color.opacity(s.selectionBackgroundTransparent,e)}if(n.color.isOpaque(s.selectionInactiveBackgroundTransparent)){const e=.3;s.selectionInactiveBackgroundTransparent=n.color.opacity(s.selectionInactiveBackgroundTransparent,e)}if(s.ansi=t.DEFAULT_ANSI_COLORS.slice(),s.ansi[0]=g(e.black,t.DEFAULT_ANSI_COLORS[0]),s.ansi[1]=g(e.red,t.DEFAULT_ANSI_COLORS[1]),s.ansi[2]=g(e.green,t.DEFAULT_ANSI_COLORS[2]),s.ansi[3]=g(e.yellow,t.DEFAULT_ANSI_COLORS[3]),s.ansi[4]=g(e.blue,t.DEFAULT_ANSI_COLORS[4]),s.ansi[5]=g(e.magenta,t.DEFAULT_ANSI_COLORS[5]),s.ansi[6]=g(e.cyan,t.DEFAULT_ANSI_COLORS[6]),s.ansi[7]=g(e.white,t.DEFAULT_ANSI_COLORS[7]),s.ansi[8]=g(e.brightBlack,t.DEFAULT_ANSI_COLORS[8]),s.ansi[9]=g(e.brightRed,t.DEFAULT_ANSI_COLORS[9]),s.ansi[10]=g(e.brightGreen,t.DEFAULT_ANSI_COLORS[10]),s.ansi[11]=g(e.brightYellow,t.DEFAULT_ANSI_COLORS[11]),s.ansi[12]=g(e.brightBlue,t.DEFAULT_ANSI_COLORS[12]),s.ansi[13]=g(e.brightMagenta,t.DEFAULT_ANSI_COLORS[13]),s.ansi[14]=g(e.brightCyan,t.DEFAULT_ANSI_COLORS[14]),s.ansi[15]=g(e.brightWhite,t.DEFAULT_ANSI_COLORS[15]),e.extendedAnsi){const r=Math.min(s.ansi.length-16,e.extendedAnsi.length);for(let o=0;o<r;o++)s.ansi[o+16]=g(e.extendedAnsi[o],t.DEFAULT_ANSI_COLORS[o+16])}this._contrastCache.clear(),this._halfContrastCache.clear(),this._updateRestoreColors(),this._onChangeColors.fire(this.colors)}restoreColor(e){this._restoreColor(e),this._onChangeColors.fire(this.colors)}_restoreColor(e){if(void 0!==e)switch(e){case 256:this._colors.foreground=this._restoreColors.foreground;break;case 257:this._colors.background=this._restoreColors.background;break;case 258:this._colors.cursor=this._restoreColors.cursor;break;default:this._colors.ansi[e]=this._restoreColors.ansi[e]}else for(let e=0;e<this._restoreColors.ansi.length;++e)this._colors.ansi[e]=this._restoreColors.ansi[e]}modifyColors(e){e(this._colors),this._onChangeColors.fire(this.colors)}_updateRestoreColors(){this._restoreColors={foreground:this._colors.foreground,background:this._colors.background,cursor:this._colors.cursor,ansi:this._colors.ansi.slice()}}};function g(e,t){if(void 0!==e)try{return n.css.toColor(e)}catch{}return t}t.ThemeService=f=r([o(0,c.IOptionsService)],f)},160:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:!0}),t.contrastRatio=t.toPaddedHex=t.rgba=t.rgb=t.css=t.color=t.channels=t.NULL_COLOR=void 0;let s=0,r=0,o=0,i=0;var n,l,a,c,h;function u(e){const t=e.toString(16);return t.length<2?\"0\"+t:t}function _(e,t){return e<t?(t+.05)/(e+.05):(e+.05)/(t+.05)}t.NULL_COLOR={css:\"#00000000\",rgba:0},function(e){e.toCss=function(e,t,s,r){return void 0!==r?`#${u(e)}${u(t)}${u(s)}${u(r)}`:`#${u(e)}${u(t)}${u(s)}`},e.toRgba=function(e,t,s,r=255){return(e<<24|t<<16|s<<8|r)>>>0},e.toColor=function(t,s,r,o){return{css:e.toCss(t,s,r,o),rgba:e.toRgba(t,s,r,o)}}}(n||(t.channels=n={})),function(e){function t(e,t){return i=Math.round(255*t),[s,r,o]=h.toChannels(e.rgba),{css:n.toCss(s,r,o,i),rgba:n.toRgba(s,r,o,i)}}e.blend=function(e,t){if(i=(255&t.rgba)/255,1===i)return{css:t.css,rgba:t.rgba};const l=t.rgba>>24&255,a=t.rgba>>16&255,c=t.rgba>>8&255,h=e.rgba>>24&255,u=e.rgba>>16&255,_=e.rgba>>8&255;return s=h+Math.round((l-h)*i),r=u+Math.round((a-u)*i),o=_+Math.round((c-_)*i),{css:n.toCss(s,r,o),rgba:n.toRgba(s,r,o)}},e.isOpaque=function(e){return 255==(255&e.rgba)},e.ensureContrastRatio=function(e,t,s){const r=h.ensureContrastRatio(e.rgba,t.rgba,s);if(r)return n.toColor(r>>24&255,r>>16&255,r>>8&255)},e.opaque=function(e){const t=(255|e.rgba)>>>0;return[s,r,o]=h.toChannels(t),{css:n.toCss(s,r,o),rgba:t}},e.opacity=t,e.multiplyOpacity=function(e,s){return i=255&e.rgba,t(e,i*s/255)},e.toColorRGB=function(e){return[e.rgba>>24&255,e.rgba>>16&255,e.rgba>>8&255]}}(l||(t.color=l={})),function(e){let t,l;try{const e=document.createElement(\"canvas\");e.width=1,e.height=1;const s=e.getContext(\"2d\",{willReadFrequently:!0});s&&(t=s,t.globalCompositeOperation=\"copy\",l=t.createLinearGradient(0,0,1,1))}catch{}e.toColor=function(e){if(e.match(/#[\\da-f]{3,8}/i))switch(e.length){case 4:return s=parseInt(e.slice(1,2).repeat(2),16),r=parseInt(e.slice(2,3).repeat(2),16),o=parseInt(e.slice(3,4).repeat(2),16),n.toColor(s,r,o);case 5:return s=parseInt(e.slice(1,2).repeat(2),16),r=parseInt(e.slice(2,3).repeat(2),16),o=parseInt(e.slice(3,4).repeat(2),16),i=parseInt(e.slice(4,5).repeat(2),16),n.toColor(s,r,o,i);case 7:return{css:e,rgba:(parseInt(e.slice(1),16)<<8|255)>>>0};case 9:return{css:e,rgba:parseInt(e.slice(1),16)>>>0}}const a=e.match(/rgba?\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*(,\\s*(0|1|\\d?\\.(\\d+))\\s*)?\\)/);if(a)return s=parseInt(a[1]),r=parseInt(a[2]),o=parseInt(a[3]),i=Math.round(255*(void 0===a[5]?1:parseFloat(a[5]))),n.toColor(s,r,o,i);if(!t||!l)throw new Error(\"css.toColor: Unsupported css format\");if(t.fillStyle=l,t.fillStyle=e,\"string\"!=typeof t.fillStyle)throw new Error(\"css.toColor: Unsupported css format\");if(t.fillRect(0,0,1,1),[s,r,o,i]=t.getImageData(0,0,1,1).data,255!==i)throw new Error(\"css.toColor: Unsupported css format\");return{rgba:n.toRgba(s,r,o,i),css:e}}}(a||(t.css=a={})),function(e){function t(e,t,s){const r=e/255,o=t/255,i=s/255;return.2126*(r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4))+.7152*(o<=.03928?o/12.92:Math.pow((o+.055)/1.055,2.4))+.0722*(i<=.03928?i/12.92:Math.pow((i+.055)/1.055,2.4))}e.relativeLuminance=function(e){return t(e>>16&255,e>>8&255,255&e)},e.relativeLuminance2=t}(c||(t.rgb=c={})),function(e){function t(e,t,s){const r=e>>24&255,o=e>>16&255,i=e>>8&255;let n=t>>24&255,l=t>>16&255,a=t>>8&255,h=_(c.relativeLuminance2(n,l,a),c.relativeLuminance2(r,o,i));for(;h<s&&(n>0||l>0||a>0);)n-=Math.max(0,Math.ceil(.1*n)),l-=Math.max(0,Math.ceil(.1*l)),a-=Math.max(0,Math.ceil(.1*a)),h=_(c.relativeLuminance2(n,l,a),c.relativeLuminance2(r,o,i));return(n<<24|l<<16|a<<8|255)>>>0}function l(e,t,s){const r=e>>24&255,o=e>>16&255,i=e>>8&255;let n=t>>24&255,l=t>>16&255,a=t>>8&255,h=_(c.relativeLuminance2(n,l,a),c.relativeLuminance2(r,o,i));for(;h<s&&(n<255||l<255||a<255);)n=Math.min(255,n+Math.ceil(.1*(255-n))),l=Math.min(255,l+Math.ceil(.1*(255-l))),a=Math.min(255,a+Math.ceil(.1*(255-a))),h=_(c.relativeLuminance2(n,l,a),c.relativeLuminance2(r,o,i));return(n<<24|l<<16|a<<8|255)>>>0}e.blend=function(e,t){if(i=(255&t)/255,1===i)return t;const l=t>>24&255,a=t>>16&255,c=t>>8&255,h=e>>24&255,u=e>>16&255,_=e>>8&255;return s=h+Math.round((l-h)*i),r=u+Math.round((a-u)*i),o=_+Math.round((c-_)*i),n.toRgba(s,r,o)},e.ensureContrastRatio=function(e,s,r){const o=c.relativeLuminance(e>>8),i=c.relativeLuminance(s>>8);if(_(o,i)<r){if(i<o){const i=t(e,s,r),n=_(o,c.relativeLuminance(i>>8));if(n<r){const t=l(e,s,r);return n>_(o,c.relativeLuminance(t>>8))?i:t}return i}const n=l(e,s,r),a=_(o,c.relativeLuminance(n>>8));if(a<r){const i=t(e,s,r);return a>_(o,c.relativeLuminance(i>>8))?n:i}return n}},e.reduceLuminance=t,e.increaseLuminance=l,e.toChannels=function(e){return[e>>24&255,e>>16&255,e>>8&255,255&e]}}(h||(t.rgba=h={})),t.toPaddedHex=u,t.contrastRatio=_},345:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:!0}),t.runAndSubscribe=t.forwardEvent=t.EventEmitter=void 0,t.EventEmitter=class{constructor(){this._listeners=[],this._disposed=!1}get event(){return this._event||(this._event=e=>(this._listeners.push(e),{dispose:()=>{if(!this._disposed)for(let t=0;t<this._listeners.length;t++)if(this._listeners[t]===e)return void this._listeners.splice(t,1)}})),this._event}fire(e,t){const s=[];for(let e=0;e<this._listeners.length;e++)s.push(this._listeners[e]);for(let r=0;r<s.length;r++)s[r].call(void 0,e,t)}dispose(){this.clearListeners(),this._disposed=!0}clearListeners(){this._listeners&&(this._listeners.length=0)}},t.forwardEvent=function(e,t){return e((e=>t.fire(e)))},t.runAndSubscribe=function(e,t){return t(void 0),e((e=>t(e)))}},859:(e,t)=>{function s(e){for(const t of e)t.dispose();e.length=0}Object.defineProperty(t,\"__esModule\",{value:!0}),t.getDisposeArrayDisposable=t.disposeArray=t.toDisposable=t.MutableDisposable=t.Disposable=void 0,t.Disposable=class{constructor(){this._disposables=[],this._isDisposed=!1}dispose(){this._isDisposed=!0;for(const e of this._disposables)e.dispose();this._disposables.length=0}register(e){return this._disposables.push(e),e}unregister(e){const t=this._disposables.indexOf(e);-1!==t&&this._disposables.splice(t,1)}},t.MutableDisposable=class{constructor(){this._isDisposed=!1}get value(){return this._isDisposed?void 0:this._value}set value(e){this._isDisposed||e===this._value||(this._value?.dispose(),this._value=e)}clear(){this.value=void 0}dispose(){this._isDisposed=!0,this._value?.dispose(),this._value=void 0}},t.toDisposable=function(e){return{dispose:e}},t.disposeArray=s,t.getDisposeArrayDisposable=function(e){return{dispose:()=>s(e)}}},485:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:!0}),t.FourKeyMap=t.TwoKeyMap=void 0;class s{constructor(){this._data={}}set(e,t,s){this._data[e]||(this._data[e]={}),this._data[e][t]=s}get(e,t){return this._data[e]?this._data[e][t]:void 0}clear(){this._data={}}}t.TwoKeyMap=s,t.FourKeyMap=class{constructor(){this._data=new s}set(e,t,r,o,i){this._data.get(e,t)||this._data.set(e,t,new s),this._data.get(e,t).set(r,o,i)}get(e,t,s,r){return this._data.get(e,t)?.get(s,r)}clear(){this._data.clear()}}},726:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:!0}),t.createDecorator=t.getServiceDependencies=t.serviceRegistry=void 0;const s=\"di$target\",r=\"di$dependencies\";t.serviceRegistry=new Map,t.getServiceDependencies=function(e){return e[r]||[]},t.createDecorator=function(e){if(t.serviceRegistry.has(e))return t.serviceRegistry.get(e);const o=function(e,t,i){if(3!==arguments.length)throw new Error(\"@IServiceName-decorator can only be used to decorate a parameter\");!function(e,t,o){t[s]===t?t[r].push({id:e,index:o}):(t[r]=[{id:e,index:o}],t[s]=t)}(o,e,i)};return o.toString=()=>e,t.serviceRegistry.set(e,o),o}},97:(e,t,s)=>{Object.defineProperty(t,\"__esModule\",{value:!0}),t.IDecorationService=t.IUnicodeService=t.IOscLinkService=t.IOptionsService=t.ILogService=t.LogLevelEnum=t.IInstantiationService=t.ICharsetService=t.ICoreService=t.ICoreMouseService=t.IBufferService=void 0;const r=s(726);var o;t.IBufferService=(0,r.createDecorator)(\"BufferService\"),t.ICoreMouseService=(0,r.createDecorator)(\"CoreMouseService\"),t.ICoreService=(0,r.createDecorator)(\"CoreService\"),t.ICharsetService=(0,r.createDecorator)(\"CharsetService\"),t.IInstantiationService=(0,r.createDecorator)(\"InstantiationService\"),function(e){e[e.TRACE=0]=\"TRACE\",e[e.DEBUG=1]=\"DEBUG\",e[e.INFO=2]=\"INFO\",e[e.WARN=3]=\"WARN\",e[e.ERROR=4]=\"ERROR\",e[e.OFF=5]=\"OFF\"}(o||(t.LogLevelEnum=o={})),t.ILogService=(0,r.createDecorator)(\"LogService\"),t.IOptionsService=(0,r.createDecorator)(\"OptionsService\"),t.IOscLinkService=(0,r.createDecorator)(\"OscLinkService\"),t.IUnicodeService=(0,r.createDecorator)(\"UnicodeService\"),t.IDecorationService=(0,r.createDecorator)(\"DecorationService\")}},t={};function s(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,s),i.exports}var r={};return(()=>{var e=r;Object.defineProperty(e,\"__esModule\",{value:!0}),e.HTMLSerializeHandler=e.SerializeAddon=void 0;const t=s(997);function o(e,t,s){return Math.max(t,Math.min(e,s))}class i{constructor(e){this._buffer=e}serialize(e,t){const s=this._buffer.getNullCell(),r=this._buffer.getNullCell();let o=s;const i=e.start.y,n=e.end.y,l=e.start.x,a=e.end.x;this._beforeSerialize(n-i,i,n);for(let t=i;t<=n;t++){const i=this._buffer.getLine(t);if(i){const n=t===e.start.y?l:0,c=t===e.end.y?a:i.length;for(let e=n;e<c;e++){const n=i.getCell(e,o===s?r:s);n?(this._nextCell(n,o,t,e),o=n):console.warn(`Can't get cell at row=${t}, col=${e}`)}}this._rowEnd(t,t===n)}return this._afterSerialize(),this._serializeString(t)}_nextCell(e,t,s,r){}_rowEnd(e,t){}_beforeSerialize(e,t,s){}_afterSerialize(){}_serializeString(e){return\"\"}}function n(e,t){return e.getFgColorMode()===t.getFgColorMode()&&e.getFgColor()===t.getFgColor()}function l(e,t){return e.getBgColorMode()===t.getBgColorMode()&&e.getBgColor()===t.getBgColor()}function a(e,t){return e.isInverse()===t.isInverse()&&e.isBold()===t.isBold()&&e.isUnderline()===t.isUnderline()&&e.isOverline()===t.isOverline()&&e.isBlink()===t.isBlink()&&e.isInvisible()===t.isInvisible()&&e.isItalic()===t.isItalic()&&e.isDim()===t.isDim()&&e.isStrikethrough()===t.isStrikethrough()}class c extends i{constructor(e,t){super(e),this._terminal=t,this._rowIndex=0,this._allRows=new Array,this._allRowSeparators=new Array,this._currentRow=\"\",this._nullCellCount=0,this._cursorStyle=this._buffer.getNullCell(),this._cursorStyleRow=0,this._cursorStyleCol=0,this._backgroundCell=this._buffer.getNullCell(),this._firstRow=0,this._lastCursorRow=0,this._lastCursorCol=0,this._lastContentCursorRow=0,this._lastContentCursorCol=0,this._thisRowLastChar=this._buffer.getNullCell(),this._thisRowLastSecondChar=this._buffer.getNullCell(),this._nextRowFirstChar=this._buffer.getNullCell()}_beforeSerialize(e,t,s){this._allRows=new Array(e),this._lastContentCursorRow=t,this._lastCursorRow=t,this._firstRow=t}_rowEnd(e,t){this._nullCellCount>0&&!l(this._cursorStyle,this._backgroundCell)&&(this._currentRow+=`\u001b[${this._nullCellCount}X`);let s=\"\";if(!t){e-this._firstRow>=this._terminal.rows&&this._buffer.getLine(this._cursorStyleRow)?.getCell(this._cursorStyleCol,this._backgroundCell);const t=this._buffer.getLine(e),r=this._buffer.getLine(e+1);if(r.isWrapped){s=\"\";const o=t.getCell(t.length-1,this._thisRowLastChar),i=t.getCell(t.length-2,this._thisRowLastSecondChar),n=r.getCell(0,this._nextRowFirstChar),a=n.getWidth()>1;let c=!1;(n.getChars()&&a?this._nullCellCount<=1:this._nullCellCount<=0)&&((o.getChars()||0===o.getWidth())&&l(o,n)&&(c=!0),a&&(i.getChars()||0===i.getWidth())&&l(o,n)&&l(i,n)&&(c=!0)),c||(s=\"-\".repeat(this._nullCellCount+1),s+=\"\u001b[1D\u001b[1X\",this._nullCellCount>0&&(s+=\"\u001b[A\",s+=`\u001b[${t.length-this._nullCellCount}C`,s+=`\u001b[${this._nullCellCount}X`,s+=`\u001b[${t.length-this._nullCellCount}D`,s+=\"\u001b[B\"),this._lastContentCursorRow=e+1,this._lastContentCursorCol=0,this._lastCursorRow=e+1,this._lastCursorCol=0)}else s=\"\\r\\n\",this._lastCursorRow=e+1,this._lastCursorCol=0}this._allRows[this._rowIndex]=this._currentRow,this._allRowSeparators[this._rowIndex++]=s,this._currentRow=\"\",this._nullCellCount=0}_diffStyle(e,t){const s=[],r=!n(e,t),o=!l(e,t),i=!a(e,t);if(r||o||i)if(e.isAttributeDefault())t.isAttributeDefault()||s.push(0);else{if(r){const t=e.getFgColor();e.isFgRGB()?s.push(38,2,t>>>16&255,t>>>8&255,255&t):e.isFgPalette()?t>=16?s.push(38,5,t):s.push(8&t?90+(7&t):30+(7&t)):s.push(39)}if(o){const t=e.getBgColor();e.isBgRGB()?s.push(48,2,t>>>16&255,t>>>8&255,255&t):e.isBgPalette()?t>=16?s.push(48,5,t):s.push(8&t?100+(7&t):40+(7&t)):s.push(49)}i&&(e.isInverse()!==t.isInverse()&&s.push(e.isInverse()?7:27),e.isBold()!==t.isBold()&&s.push(e.isBold()?1:22),e.isUnderline()!==t.isUnderline()&&s.push(e.isUnderline()?4:24),e.isOverline()!==t.isOverline()&&s.push(e.isOverline()?53:55),e.isBlink()!==t.isBlink()&&s.push(e.isBlink()?5:25),e.isInvisible()!==t.isInvisible()&&s.push(e.isInvisible()?8:28),e.isItalic()!==t.isItalic()&&s.push(e.isItalic()?3:23),e.isDim()!==t.isDim()&&s.push(e.isDim()?2:22),e.isStrikethrough()!==t.isStrikethrough()&&s.push(e.isStrikethrough()?9:29))}return s}_nextCell(e,t,s,r){if(0===e.getWidth())return;const o=\"\"===e.getChars(),i=this._diffStyle(e,this._cursorStyle);if(o?!l(this._cursorStyle,e):i.length>0){this._nullCellCount>0&&(l(this._cursorStyle,this._backgroundCell)||(this._currentRow+=`\u001b[${this._nullCellCount}X`),this._currentRow+=`\u001b[${this._nullCellCount}C`,this._nullCellCount=0),this._lastContentCursorRow=this._lastCursorRow=s,this._lastContentCursorCol=this._lastCursorCol=r,this._currentRow+=`\u001b[${i.join(\";\")}m`;const e=this._buffer.getLine(s);void 0!==e&&(e.getCell(r,this._cursorStyle),this._cursorStyleRow=s,this._cursorStyleCol=r)}o?this._nullCellCount+=e.getWidth():(this._nullCellCount>0&&(l(this._cursorStyle,this._backgroundCell)||(this._currentRow+=`\u001b[${this._nullCellCount}X`),this._currentRow+=`\u001b[${this._nullCellCount}C`,this._nullCellCount=0),this._currentRow+=e.getChars(),this._lastContentCursorRow=this._lastCursorRow=s,this._lastContentCursorCol=this._lastCursorCol=r+e.getWidth())}_serializeString(e){let t=this._allRows.length;this._buffer.length-this._firstRow<=this._terminal.rows&&(t=this._lastContentCursorRow+1-this._firstRow,this._lastCursorCol=this._lastContentCursorCol,this._lastCursorRow=this._lastContentCursorRow);let s=\"\";for(let e=0;e<t;e++)s+=this._allRows[e],e+1<t&&(s+=this._allRowSeparators[e]);if(!e){const e=this._buffer.baseY+this._buffer.cursorY,t=this._buffer.cursorX,o=e=>{e>0?s+=`\u001b[${e}C`:e<0&&(s+=`\u001b[${-e}D`)};(e!==this._lastCursorRow||t!==this._lastCursorCol)&&((r=e-this._lastCursorRow)>0?s+=`\u001b[${r}B`:r<0&&(s+=`\u001b[${-r}A`),o(t-this._lastCursorCol))}var r;const o=this._terminal._core._inputHandler._curAttrData,i=this._diffStyle(o,this._cursorStyle);return i.length>0&&(s+=`\u001b[${i.join(\";\")}m`),s}}e.SerializeAddon=class{activate(e){this._terminal=e}_serializeBufferByScrollback(e,t,s){const r=t.length,i=void 0===s?r:o(s+e.rows,0,r);return this._serializeBufferByRange(e,t,{start:r-i,end:r-1},!1)}_serializeBufferByRange(e,t,s,r){return new c(t,e).serialize({start:{x:0,y:\"number\"==typeof s.start?s.start:s.start.line},end:{x:e.cols,y:\"number\"==typeof s.end?s.end:s.end.line}},r)}_serializeBufferAsHTML(e,t){const s=e.buffer.active,r=new h(s,e,t);if(!t.onlySelection){const i=s.length,n=t.scrollback,l=void 0===n?i:o(n+e.rows,0,i);return r.serialize({start:{x:0,y:i-l},end:{x:e.cols,y:i-1}})}const i=this._terminal?.getSelectionPosition();return void 0!==i?r.serialize({start:{x:i.start.x,y:i.start.y},end:{x:i.end.x,y:i.end.y}}):\"\"}_serializeModes(e){let t=\"\";const s=e.modes;if(s.applicationCursorKeysMode&&(t+=\"\u001b[?1h\"),s.applicationKeypadMode&&(t+=\"\u001b[?66h\"),s.bracketedPasteMode&&(t+=\"\u001b[?2004h\"),s.insertMode&&(t+=\"\u001b[4h\"),s.originMode&&(t+=\"\u001b[?6h\"),s.reverseWraparoundMode&&(t+=\"\u001b[?45h\"),s.sendFocusMode&&(t+=\"\u001b[?1004h\"),!1===s.wraparoundMode&&(t+=\"\u001b[?7l\"),\"none\"!==s.mouseTrackingMode)switch(s.mouseTrackingMode){case\"x10\":t+=\"\u001b[?9h\";break;case\"vt200\":t+=\"\u001b[?1000h\";break;case\"drag\":t+=\"\u001b[?1002h\";break;case\"any\":t+=\"\u001b[?1003h\"}return t}serialize(e){if(!this._terminal)throw new Error(\"Cannot use addon until it has been loaded\");let t=e?.range?this._serializeBufferByRange(this._terminal,this._terminal.buffer.normal,e.range,!0):this._serializeBufferByScrollback(this._terminal,this._terminal.buffer.normal,e?.scrollback);return e?.excludeAltBuffer||\"alternate\"!==this._terminal.buffer.active.type||(t+=`\u001b[?1049h\u001b[H${this._serializeBufferByScrollback(this._terminal,this._terminal.buffer.alternate,void 0)}`),e?.excludeModes||(t+=this._serializeModes(this._terminal)),t}serializeAsHTML(e){if(!this._terminal)throw new Error(\"Cannot use addon until it has been loaded\");return this._serializeBufferAsHTML(this._terminal,e||{})}dispose(){}};class h extends i{constructor(e,s,r){super(e),this._terminal=s,this._options=r,this._currentRow=\"\",this._htmlContent=\"\",s._core._themeService?this._ansiColors=s._core._themeService.colors.ansi:this._ansiColors=t.DEFAULT_ANSI_COLORS}_padStart(e,t,s){return t>>=0,s=s??\" \",e.length>t?e:((t-=e.length)>s.length&&(s+=s.repeat(t/s.length)),s.slice(0,t)+e)}_beforeSerialize(e,t,s){this._htmlContent+=\"<html><body>\\x3c!--StartFragment--\\x3e<pre>\";let r=\"#000000\",o=\"#ffffff\";this._options.includeGlobalBackground&&(r=this._terminal.options.theme?.foreground??\"#ffffff\",o=this._terminal.options.theme?.background??\"#000000\");const i=[];i.push(\"color: \"+r+\";\"),i.push(\"background-color: \"+o+\";\"),i.push(\"font-family: \"+this._terminal.options.fontFamily+\";\"),i.push(\"font-size: \"+this._terminal.options.fontSize+\"px;\"),this._htmlContent+=\"<div style='\"+i.join(\" \")+\"'>\"}_afterSerialize(){this._htmlContent+=\"</div>\",this._htmlContent+=\"</pre>\\x3c!--EndFragment--\\x3e</body></html>\"}_rowEnd(e,t){this._htmlContent+=\"<div><span>\"+this._currentRow+\"</span></div>\",this._currentRow=\"\"}_getHexColor(e,t){const s=t?e.getFgColor():e.getBgColor();return(t?e.isFgRGB():e.isBgRGB())?\"#\"+[s>>16&255,s>>8&255,255&s].map((e=>this._padStart(e.toString(16),2,\"0\"))).join(\"\"):(t?e.isFgPalette():e.isBgPalette())?this._ansiColors[s].css:void 0}_diffStyle(e,t){const s=[],r=!n(e,t),o=!l(e,t),i=!a(e,t);if(r||o||i){const t=this._getHexColor(e,!0);t&&s.push(\"color: \"+t+\";\");const r=this._getHexColor(e,!1);return r&&s.push(\"background-color: \"+r+\";\"),e.isInverse()&&s.push(\"color: #000000; background-color: #BFBFBF;\"),e.isBold()&&s.push(\"font-weight: bold;\"),e.isUnderline()&&e.isOverline()?s.push(\"text-decoration: overline underline;\"):e.isUnderline()?s.push(\"text-decoration: underline;\"):e.isOverline()&&s.push(\"text-decoration: overline;\"),e.isBlink()&&s.push(\"text-decoration: blink;\"),e.isInvisible()&&s.push(\"visibility: hidden;\"),e.isItalic()&&s.push(\"font-style: italic;\"),e.isDim()&&s.push(\"opacity: 0.5;\"),e.isStrikethrough()&&s.push(\"text-decoration: line-through;\"),s}}_nextCell(e,t,s,r){if(0===e.getWidth())return;const o=\"\"===e.getChars(),i=this._diffStyle(e,t);i&&(this._currentRow+=0===i.length?\"</span><span>\":\"</span><span style='\"+i.join(\" \")+\"'>\"),this._currentRow+=o?\" \":e.getChars()}_serializeString(){return this._htmlContent}}e.HTMLSerializeHandler=h})(),r})()));\n//# sourceMappingURL=addon-serialize.js.map", "import { SerializeAddon } from '@xterm/addon-serialize'\nimport { Terminal } from '@xterm/xterm'\nimport { markRaw, onMounted, onUnmounted } from 'vue'\n\nexport function useTerminalBuffer() {\n  const serializeAddon = new SerializeAddon()\n  const terminal = markRaw(new Terminal({ convertEol: true }))\n\n  const copyTo = (destinationTerminal: Terminal) => {\n    destinationTerminal.write(serializeAddon.serialize())\n  }\n\n  const write = (message: string) => terminal.write(message)\n\n  const serialize = () => serializeAddon.serialize()\n\n  onMounted(() => {\n    terminal.loadAddon(serializeAddon)\n  })\n\n  onUnmounted(() => {\n    terminal.dispose()\n  })\n\n  return {\n    copyTo,\n    serialize,\n    write\n  }\n}\n", "<template>\n  <Drawer\n    v-model:visible=\"terminalVisible\"\n    :header\n    position=\"bottom\"\n    style=\"height: max(50vh, 34rem)\"\n  >\n    <BaseTerminal @created=\"terminalCreated\" @unmounted=\"terminalUnmounted\" />\n  </Drawer>\n</template>\n\n<script setup lang=\"ts\">\nimport { Terminal } from '@xterm/xterm'\nimport Drawer from 'primevue/drawer'\nimport { Ref, onMounted } from 'vue'\n\nimport BaseTerminal from '@/components/bottomPanel/tabs/terminal/BaseTerminal.vue'\nimport type { useTerminal } from '@/composables/bottomPanelTabs/useTerminal'\nimport { useTerminalBuffer } from '@/composables/bottomPanelTabs/useTerminalBuffer'\nimport { electronAPI } from '@/utils/envUtil'\n\n// Model\nconst terminalVisible = defineModel<boolean>({ required: true })\nconst props = defineProps<{\n  header: string\n  defaultMessage: string\n}>()\n\nconst electron = electronAPI()\n\n/** The actual output of all terminal commands - not rendered */\nconst buffer = useTerminalBuffer()\nlet xterm: Terminal | null = null\n\n// Created and destroyed with the Drawer - contents copied from hidden buffer\nconst terminalCreated = (\n  { terminal, useAutoSize }: ReturnType<typeof useTerminal>,\n  root: Ref<HTMLElement | undefined>\n) => {\n  xterm = terminal\n  useAutoSize({ root, autoRows: true, autoCols: true })\n  terminal.write(props.defaultMessage)\n  buffer.copyTo(terminal)\n\n  terminal.options.cursorBlink = false\n  terminal.options.cursorStyle = 'bar'\n  terminal.options.cursorInactiveStyle = 'bar'\n  terminal.options.disableStdin = true\n}\n\nconst terminalUnmounted = () => {\n  xterm = null\n}\n\nonMounted(async () => {\n  electron.onLogMessage((message: string) => {\n    buffer.write(message)\n    xterm?.write(message)\n  })\n})\n</script>\n"], "names": ["define", "this", "e", "t", "s", "r", "o", "i", "n", "l", "a", "c", "h", "u", "_", "SerializeAddon", "Terminal", "_useModel"], "mappings": ";;;;;;;;AAAA,GAAC,SAAS,GAAE,GAAE;AAAC,IAA0B,OAAwB,OAAe,UAAA,EAAG,IAAC,QAAsCA,SAAO,CAAA,GAAG,CAAC,IAAE,OAAyB,QAAuB,iBAAA,EAAG,IAAC,EAAE,iBAAe,EAAG;AAAA,EAAA,EAAEC,gBAAM,OAAK,MAAI;AAAC;AAAa,QAAI,IAAE,EAAC,KAAI,CAACC,IAAEC,IAAEC,OAAI;AAAC,aAAO,eAAeD,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,qBAAmB;AAAO,YAAME,KAAED,GAAE,GAAG;AAAE,MAAAD,GAAE,qBAAmB,MAAK;AAAA,QAAC,cAAa;AAAC,eAAK,SAAO,IAAIE,GAAE,aAAU,KAAK,OAAK,IAAIA,GAAE;AAAA,QAAS;AAAA,QAAC,OAAOH,IAAEC,IAAEC,IAAE;AAAC,eAAK,KAAK,IAAIF,IAAEC,IAAEC,EAAC;AAAA,QAAC;AAAA,QAAC,OAAOF,IAAEC,IAAE;AAAC,iBAAO,KAAK,KAAK,IAAID,IAAEC,EAAC;AAAA,QAAC;AAAA,QAAC,SAASD,IAAEC,IAAEC,IAAE;AAAC,eAAK,OAAO,IAAIF,IAAEC,IAAEC,EAAC;AAAA,QAAC;AAAA,QAAC,SAASF,IAAEC,IAAE;AAAC,iBAAO,KAAK,OAAO,IAAID,IAAEC,EAAC;AAAA,QAAC;AAAA,QAAC,QAAO;AAAC,eAAK,OAAO,MAAK,GAAG,KAAK,KAAK;QAAO;AAAA,MAAC;AAAA,IAAC,GAAE,KAAI,SAASD,IAAEC,IAAEC,IAAE;AAAC,UAAIC,KAAE,QAAM,KAAK,cAAY,SAASH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAIC,IAAEC,KAAE,UAAU,QAAOC,KAAED,KAAE,IAAEJ,KAAE,SAAOE,KAAEA,KAAE,OAAO,yBAAyBF,IAAEC,EAAC,IAAEC;AAAE,YAAG,YAAU,OAAO,WAAS,cAAY,OAAO,QAAQ,SAAS,CAAAG,KAAE,QAAQ,SAASN,IAAEC,IAAEC,IAAEC,EAAC;AAAA,YAAO,UAAQI,KAAEP,GAAE,SAAO,GAAEO,MAAG,GAAEA,KAAI,EAACH,KAAEJ,GAAEO,EAAC,OAAKD,MAAGD,KAAE,IAAED,GAAEE,EAAC,IAAED,KAAE,IAAED,GAAEH,IAAEC,IAAEI,EAAC,IAAEF,GAAEH,IAAEC,EAAC,MAAII;AAAG,eAAOD,KAAE,KAAGC,MAAG,OAAO,eAAeL,IAAEC,IAAEI,EAAC,GAAEA;AAAA,MAAC,GAAE,IAAE,QAAM,KAAK,WAAS,SAASN,IAAEC,IAAE;AAAC,eAAO,SAASC,IAAEC,IAAE;AAAC,UAAAF,GAAEC,IAAEC,IAAEH,EAAC;AAAA,QAAC;AAAA,MAAC;AAAE,aAAO,eAAeC,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,eAAaA,GAAE,sBAAoB;AAAO,YAAM,IAAEC,GAAE,GAAG,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,EAAE,GAAE,IAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,IAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,IAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,IAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,IAAE,EAAC,KAAI,4BAA2B,MAAK,WAAU;AAAE,MAAAD,GAAE,sBAAoB,OAAO,QAAQ,MAAI;AAAC,cAAMD,KAAE,CAAC,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,GAAE,EAAE,IAAI,QAAQ,SAAS,CAAC,GAAEC,KAAE,CAAC,GAAE,IAAG,KAAI,KAAI,KAAI,GAAG;AAAE,iBAAQC,KAAE,GAAEA,KAAE,KAAIA,MAAI;AAAC,gBAAMC,KAAEF,GAAEC,KAAE,KAAG,IAAE,CAAC,GAAEE,KAAEH,GAAEC,KAAE,IAAE,IAAE,CAAC,GAAEG,KAAEJ,GAAEC,KAAE,CAAC;AAAE,UAAAF,GAAE,KAAK,EAAC,KAAI,EAAE,SAAS,MAAMG,IAAEC,IAAEC,EAAC,GAAE,MAAK,EAAE,SAAS,OAAOF,IAAEC,IAAEC,EAAC,EAAC,CAAC;AAAA,QAAC;AAAC,iBAAQJ,KAAE,GAAEA,KAAE,IAAGA,MAAI;AAAC,gBAAMC,KAAE,IAAE,KAAGD;AAAE,UAAAD,GAAE,KAAK,EAAC,KAAI,EAAE,SAAS,MAAME,IAAEA,IAAEA,EAAC,GAAE,MAAK,EAAE,SAAS,OAAOA,IAAEA,IAAEA,EAAC,EAAC,CAAC;AAAA,QAAC;AAAC,eAAOF;AAAA,MAAC,IAAI;AAAE,UAAI,IAAEC,GAAE,eAAa,cAAc,EAAE,WAAU;AAAA,QAAC,IAAI,SAAQ;AAAC,iBAAO,KAAK;AAAA,QAAO;AAAA,QAAC,YAAYD,IAAE;AAAC,gBAAK,GAAG,KAAK,kBAAgBA,IAAE,KAAK,iBAAe,IAAI,EAAE,sBAAmB,KAAK,qBAAmB,IAAI,EAAE,sBAAmB,KAAK,kBAAgB,KAAK,SAAS,IAAI,EAAE,cAAY,GAAE,KAAK,iBAAe,KAAK,gBAAgB,OAAM,KAAK,UAAQ,EAAC,YAAW,GAAE,YAAW,GAAE,QAAO,GAAE,cAAa,GAAE,qBAAoB,QAAO,gCAA+B,GAAE,2BAA0B,EAAE,MAAM,MAAM,GAAE,CAAC,GAAE,wCAAuC,GAAE,mCAAkC,EAAE,MAAM,MAAM,GAAE,CAAC,GAAE,MAAKC,GAAE,oBAAoB,MAAK,GAAG,eAAc,KAAK,gBAAe,mBAAkB,KAAK,mBAAkB,GAAE,KAAK,qBAAoB,GAAG,KAAK,UAAU,KAAK,gBAAgB,WAAW,KAAK,GAAE,KAAK,SAAS,KAAK,gBAAgB,uBAAuB,wBAAwB,MAAI,KAAK,eAAe,MAAK,CAAI,CAAA,GAAE,KAAK,SAAS,KAAK,gBAAgB,uBAAuB,SAAS,MAAI,KAAK,UAAU,KAAK,gBAAgB,WAAW,KAAK,CAAG,CAAA;AAAA,QAAC;AAAA,QAAC,UAAUD,KAAE,CAAE,GAAC;AAAC,gBAAME,KAAE,KAAK;AAAQ,cAAGA,GAAE,aAAW,EAAEF,GAAE,YAAW,CAAC,GAAEE,GAAE,aAAW,EAAEF,GAAE,YAAW,CAAC,GAAEE,GAAE,SAAO,EAAEF,GAAE,QAAO,CAAC,GAAEE,GAAE,eAAa,EAAEF,GAAE,cAAa,CAAC,GAAEE,GAAE,iCAA+B,EAAEF,GAAE,qBAAoB,CAAC,GAAEE,GAAE,4BAA0B,EAAE,MAAM,MAAMA,GAAE,YAAWA,GAAE,8BAA8B,GAAEA,GAAE,yCAAuC,EAAEF,GAAE,6BAA4BE,GAAE,8BAA8B,GAAEA,GAAE,oCAAkC,EAAE,MAAM,MAAMA,GAAE,YAAWA,GAAE,sCAAsC,GAAEA,GAAE,sBAAoBF,GAAE,sBAAoB,EAAEA,GAAE,qBAAoB,EAAE,UAAU,IAAE,QAAOE,GAAE,wBAAsB,EAAE,eAAaA,GAAE,sBAAoB,SAAQ,EAAE,MAAM,SAASA,GAAE,8BAA8B,GAAE;AAAC,kBAAMF,KAAE;AAAG,YAAAE,GAAE,iCAA+B,EAAE,MAAM,QAAQA,GAAE,gCAA+BF,EAAC;AAAA,UAAC;AAAC,cAAG,EAAE,MAAM,SAASE,GAAE,sCAAsC,GAAE;AAAC,kBAAMF,KAAE;AAAG,YAAAE,GAAE,yCAAuC,EAAE,MAAM,QAAQA,GAAE,wCAAuCF,EAAC;AAAA,UAAC;AAAC,cAAGE,GAAE,OAAKD,GAAE,oBAAoB,SAAQC,GAAE,KAAK,CAAC,IAAE,EAAEF,GAAE,OAAMC,GAAE,oBAAoB,CAAC,CAAC,GAAEC,GAAE,KAAK,CAAC,IAAE,EAAEF,GAAE,KAAIC,GAAE,oBAAoB,CAAC,CAAC,GAAEC,GAAE,KAAK,CAAC,IAAE,EAAEF,GAAE,OAAMC,GAAE,oBAAoB,CAAC,CAAC,GAAEC,GAAE,KAAK,CAAC,IAAE,EAAEF,GAAE,QAAOC,GAAE,oBAAoB,CAAC,CAAC,GAAEC,GAAE,KAAK,CAAC,IAAE,EAAEF,GAAE,MAAKC,GAAE,oBAAoB,CAAC,CAAC,GAAEC,GAAE,KAAK,CAAC,IAAE,EAAEF,GAAE,SAAQC,GAAE,oBAAoB,CAAC,CAAC,GAAEC,GAAE,KAAK,CAAC,IAAE,EAAEF,GAAE,MAAKC,GAAE,oBAAoB,CAAC,CAAC,GAAEC,GAAE,KAAK,CAAC,IAAE,EAAEF,GAAE,OAAMC,GAAE,oBAAoB,CAAC,CAAC,GAAEC,GAAE,KAAK,CAAC,IAAE,EAAEF,GAAE,aAAYC,GAAE,oBAAoB,CAAC,CAAC,GAAEC,GAAE,KAAK,CAAC,IAAE,EAAEF,GAAE,WAAUC,GAAE,oBAAoB,CAAC,CAAC,GAAEC,GAAE,KAAK,EAAE,IAAE,EAAEF,GAAE,aAAYC,GAAE,oBAAoB,EAAE,CAAC,GAAEC,GAAE,KAAK,EAAE,IAAE,EAAEF,GAAE,cAAaC,GAAE,oBAAoB,EAAE,CAAC,GAAEC,GAAE,KAAK,EAAE,IAAE,EAAEF,GAAE,YAAWC,GAAE,oBAAoB,EAAE,CAAC,GAAEC,GAAE,KAAK,EAAE,IAAE,EAAEF,GAAE,eAAcC,GAAE,oBAAoB,EAAE,CAAC,GAAEC,GAAE,KAAK,EAAE,IAAE,EAAEF,GAAE,YAAWC,GAAE,oBAAoB,EAAE,CAAC,GAAEC,GAAE,KAAK,EAAE,IAAE,EAAEF,GAAE,aAAYC,GAAE,oBAAoB,EAAE,CAAC,GAAED,GAAE,cAAa;AAAC,kBAAMG,KAAE,KAAK,IAAID,GAAE,KAAK,SAAO,IAAGF,GAAE,aAAa,MAAM;AAAE,qBAAQI,KAAE,GAAEA,KAAED,IAAEC,KAAI,CAAAF,GAAE,KAAKE,KAAE,EAAE,IAAE,EAAEJ,GAAE,aAAaI,EAAC,GAAEH,GAAE,oBAAoBG,KAAE,EAAE,CAAC;AAAA,UAAC;AAAC,eAAK,eAAe,MAAO,GAAC,KAAK,mBAAmB,MAAO,GAAC,KAAK,qBAAoB,GAAG,KAAK,gBAAgB,KAAK,KAAK,MAAM;AAAA,QAAC;AAAA,QAAC,aAAaJ,IAAE;AAAC,eAAK,cAAcA,EAAC,GAAE,KAAK,gBAAgB,KAAK,KAAK,MAAM;AAAA,QAAC;AAAA,QAAC,cAAcA,IAAE;AAAC,cAAG,WAASA,GAAE,SAAOA,IAAC;AAAA,YAAE,KAAK;AAAI,mBAAK,QAAQ,aAAW,KAAK,eAAe;AAAW;AAAA,YAAM,KAAK;AAAI,mBAAK,QAAQ,aAAW,KAAK,eAAe;AAAW;AAAA,YAAM,KAAK;AAAI,mBAAK,QAAQ,SAAO,KAAK,eAAe;AAAO;AAAA,YAAM;AAAQ,mBAAK,QAAQ,KAAKA,EAAC,IAAE,KAAK,eAAe,KAAKA,EAAC;AAAA,UAAC;AAAA,cAAM,UAAQA,KAAE,GAAEA,KAAE,KAAK,eAAe,KAAK,QAAO,EAAEA,GAAE,MAAK,QAAQ,KAAKA,EAAC,IAAE,KAAK,eAAe,KAAKA,EAAC;AAAA,QAAC;AAAA,QAAC,aAAaA,IAAE;AAAC,UAAAA,GAAE,KAAK,OAAO,GAAE,KAAK,gBAAgB,KAAK,KAAK,MAAM;AAAA,QAAC;AAAA,QAAC,uBAAsB;AAAC,eAAK,iBAAe,EAAC,YAAW,KAAK,QAAQ,YAAW,YAAW,KAAK,QAAQ,YAAW,QAAO,KAAK,QAAQ,QAAO,MAAK,KAAK,QAAQ,KAAK,MAAK,EAAE;AAAA,QAAC;AAAA,MAAC;AAAE,eAAS,EAAEA,IAAEC,IAAE;AAAC,YAAG,WAASD,GAAE,KAAG;AAAC,iBAAO,EAAE,IAAI,QAAQA,EAAC;AAAA,QAAC,QAAM;AAAA,QAAE;AAAA,eAAOC;AAAA,MAAC;AAAhE;AAAiE,MAAAA,GAAE,eAAa,IAAEE,GAAE,CAAC,EAAE,GAAE,EAAE,eAAe,CAAC,GAAE,CAAC;AAAA,IAAC,GAAE,KAAI,CAACH,IAAEC,OAAI;AAAC,aAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,gBAAcA,GAAE,cAAYA,GAAE,OAAKA,GAAE,MAAIA,GAAE,MAAIA,GAAE,QAAMA,GAAE,WAASA,GAAE,aAAW;AAAO,UAAIC,KAAE,GAAEC,KAAE,GAAE,IAAE,GAAE,IAAE;AAAE,UAAI,GAAE,GAAE,GAAE,GAAE;AAAE,eAAS,EAAEH,IAAE;AAAC,cAAMC,KAAED,GAAE,SAAS,EAAE;AAAE,eAAOC,GAAE,SAAO,IAAE,MAAIA,KAAEA;AAAA,MAAC;AAArD;AAAsD,eAAS,EAAED,IAAEC,IAAE;AAAC,eAAOD,KAAEC,MAAGA,KAAE,SAAMD,KAAE,SAAMA,KAAE,SAAMC,KAAE;AAAA,MAAI;AAAjD;AAAkD,MAAAA,GAAE,aAAW,EAAC,KAAI,aAAY,MAAK,EAAC,GAAE,SAASD,IAAE;AAAC,QAAAA,GAAE,QAAM,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,iBAAO,WAASA,KAAE,IAAI,EAAEH,EAAC,CAAC,GAAG,EAAEC,EAAC,CAAC,GAAG,EAAEC,EAAC,CAAC,GAAG,EAAEC,EAAC,CAAC,KAAG,IAAI,EAAEH,EAAC,CAAC,GAAG,EAAEC,EAAC,CAAC,GAAG,EAAEC,EAAC,CAAC;AAAA,QAAE,GAAEF,GAAE,SAAO,SAASA,IAAEC,IAAEC,IAAEC,KAAE,KAAI;AAAC,kBAAOH,MAAG,KAAGC,MAAG,KAAGC,MAAG,IAAEC,QAAK;AAAA,QAAC,GAAEH,GAAE,UAAQ,SAASC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,iBAAM,EAAC,KAAIJ,GAAE,MAAMC,IAAEC,IAAEC,IAAEC,EAAC,GAAE,MAAKJ,GAAE,OAAOC,IAAEC,IAAEC,IAAEC,EAAC,EAAC;AAAA,QAAC;AAAA,MAAC,EAAE,MAAIH,GAAE,WAAS,IAAE,CAAE,EAAC,GAAE,SAASD,IAAE;AAAC,iBAASC,GAAED,IAAEC,IAAE;AAAC,iBAAO,IAAE,KAAK,MAAM,MAAIA,EAAC,GAAE,CAACC,IAAEC,IAAE,CAAC,IAAE,EAAE,WAAWH,GAAE,IAAI,GAAE,EAAC,KAAI,EAAE,MAAME,IAAEC,IAAE,GAAE,CAAC,GAAE,MAAK,EAAE,OAAOD,IAAEC,IAAE,GAAE,CAAC,EAAC;AAAA,QAAC;AAA5G,eAAAF,IAAA;AAA6G,QAAAD,GAAE,QAAM,SAASA,IAAEC,IAAE;AAAC,cAAG,KAAG,MAAIA,GAAE,QAAM,KAAI,MAAI,EAAE,QAAM,EAAC,KAAIA,GAAE,KAAI,MAAKA,GAAE,KAAI;AAAE,gBAAMM,KAAEN,GAAE,QAAM,KAAG,KAAIO,KAAEP,GAAE,QAAM,KAAG,KAAIQ,KAAER,GAAE,QAAM,IAAE,KAAIS,KAAEV,GAAE,QAAM,KAAG,KAAIW,KAAEX,GAAE,QAAM,KAAG,KAAIY,KAAEZ,GAAE,QAAM,IAAE;AAAI,iBAAOE,KAAEQ,KAAE,KAAK,OAAOH,KAAEG,MAAG,CAAC,GAAEP,KAAEQ,KAAE,KAAK,OAAOH,KAAEG,MAAG,CAAC,GAAE,IAAEC,KAAE,KAAK,OAAOH,KAAEG,MAAG,CAAC,GAAE,EAAC,KAAI,EAAE,MAAMV,IAAEC,IAAE,CAAC,GAAE,MAAK,EAAE,OAAOD,IAAEC,IAAE,CAAC,EAAC;AAAA,QAAC,GAAEH,GAAE,WAAS,SAASA,IAAE;AAAC,iBAAO,QAAM,MAAIA,GAAE;AAAA,QAAK,GAAEA,GAAE,sBAAoB,SAASA,IAAEC,IAAEC,IAAE;AAAC,gBAAMC,KAAE,EAAE,oBAAoBH,GAAE,MAAKC,GAAE,MAAKC,EAAC;AAAE,cAAGC,GAAE,QAAO,EAAE,QAAQA,MAAG,KAAG,KAAIA,MAAG,KAAG,KAAIA,MAAG,IAAE,GAAG;AAAA,QAAC,GAAEH,GAAE,SAAO,SAASA,IAAE;AAAC,gBAAMC,MAAG,MAAID,GAAE,UAAQ;AAAE,iBAAM,CAACE,IAAEC,IAAE,CAAC,IAAE,EAAE,WAAWF,EAAC,GAAE,EAAC,KAAI,EAAE,MAAMC,IAAEC,IAAE,CAAC,GAAE,MAAKF,GAAC;AAAA,QAAC,GAAED,GAAE,UAAQC,IAAED,GAAE,kBAAgB,SAASA,IAAEE,IAAE;AAAC,iBAAO,IAAE,MAAIF,GAAE,MAAKC,GAAED,IAAE,IAAEE,KAAE,GAAG;AAAA,QAAC,GAAEF,GAAE,aAAW,SAASA,IAAE;AAAC,iBAAM,CAACA,GAAE,QAAM,KAAG,KAAIA,GAAE,QAAM,KAAG,KAAIA,GAAE,QAAM,IAAE,GAAG;AAAA,QAAC;AAAA,MAAC,EAAE,MAAIC,GAAE,QAAM,IAAE,CAAA,EAAG,GAAE,SAASD,IAAE;AAAC,YAAIC,IAAEM;AAAE,YAAG;AAAC,gBAAMP,KAAE,SAAS,cAAc,QAAQ;AAAE,UAAAA,GAAE,QAAM,GAAEA,GAAE,SAAO;AAAE,gBAAME,KAAEF,GAAE,WAAW,MAAK,EAAC,oBAAmB,KAAE,CAAC;AAAE,UAAAE,OAAID,KAAEC,IAAED,GAAE,2BAAyB,QAAOM,KAAEN,GAAE,qBAAqB,GAAE,GAAE,GAAE,CAAC;AAAA,QAAE,QAAM;AAAA,QAAA;AAAE,QAAAD,GAAE,UAAQ,SAASA,IAAE;AAAC,cAAGA,GAAE,MAAM,gBAAgB,EAAE,SAAOA,GAAE,QAAM;AAAA,YAAE,KAAK;AAAE,qBAAOE,KAAE,SAASF,GAAE,MAAM,GAAE,CAAC,EAAE,OAAO,CAAC,GAAE,EAAE,GAAEG,KAAE,SAASH,GAAE,MAAM,GAAE,CAAC,EAAE,OAAO,CAAC,GAAE,EAAE,GAAE,IAAE,SAASA,GAAE,MAAM,GAAE,CAAC,EAAE,OAAO,CAAC,GAAE,EAAE,GAAE,EAAE,QAAQE,IAAEC,IAAE,CAAC;AAAA,YAAE,KAAK;AAAE,qBAAOD,KAAE,SAASF,GAAE,MAAM,GAAE,CAAC,EAAE,OAAO,CAAC,GAAE,EAAE,GAAEG,KAAE,SAASH,GAAE,MAAM,GAAE,CAAC,EAAE,OAAO,CAAC,GAAE,EAAE,GAAE,IAAE,SAASA,GAAE,MAAM,GAAE,CAAC,EAAE,OAAO,CAAC,GAAE,EAAE,GAAE,IAAE,SAASA,GAAE,MAAM,GAAE,CAAC,EAAE,OAAO,CAAC,GAAE,EAAE,GAAE,EAAE,QAAQE,IAAEC,IAAE,GAAE,CAAC;AAAA,YAAE,KAAK;AAAE,qBAAM,EAAC,KAAIH,IAAE,OAAM,SAASA,GAAE,MAAM,CAAC,GAAE,EAAE,KAAG,IAAE,SAAO,EAAC;AAAA,YAAE,KAAK;AAAE,qBAAM,EAAC,KAAIA,IAAE,MAAK,SAASA,GAAE,MAAM,CAAC,GAAE,EAAE,MAAI,EAAC;AAAA,UAAC;AAAC,gBAAMQ,KAAER,GAAE,MAAM,oFAAoF;AAAE,cAAGQ,GAAE,QAAON,KAAE,SAASM,GAAE,CAAC,CAAC,GAAEL,KAAE,SAASK,GAAE,CAAC,CAAC,GAAE,IAAE,SAASA,GAAE,CAAC,CAAC,GAAE,IAAE,KAAK,MAAM,OAAK,WAASA,GAAE,CAAC,IAAE,IAAE,WAAWA,GAAE,CAAC,CAAC,EAAE,GAAE,EAAE,QAAQN,IAAEC,IAAE,GAAE,CAAC;AAAE,cAAG,CAACF,MAAG,CAACM,GAAE,OAAM,IAAI,MAAM,qCAAqC;AAAE,cAAGN,GAAE,YAAUM,IAAEN,GAAE,YAAUD,IAAE,YAAU,OAAOC,GAAE,UAAU,OAAM,IAAI,MAAM,qCAAqC;AAAE,cAAGA,GAAE,SAAS,GAAE,GAAE,GAAE,CAAC,GAAE,CAACC,IAAEC,IAAE,GAAE,CAAC,IAAEF,GAAE,aAAa,GAAE,GAAE,GAAE,CAAC,EAAE,MAAK,QAAM,EAAE,OAAM,IAAI,MAAM,qCAAqC;AAAE,iBAAM,EAAC,MAAK,EAAE,OAAOC,IAAEC,IAAE,GAAE,CAAC,GAAE,KAAIH,GAAC;AAAA,QAAC;AAAA,MAAC,EAAE,MAAIC,GAAE,MAAI,IAAE,CAAA,EAAG,GAAE,SAASD,IAAE;AAAC,iBAASC,GAAED,IAAEC,IAAEC,IAAE;AAAC,gBAAMC,KAAEH,KAAE,KAAII,KAAEH,KAAE,KAAII,KAAEH,KAAE;AAAI,iBAAM,UAAOC,MAAG,UAAOA,KAAE,QAAM,KAAK,KAAKA,KAAE,SAAM,OAAM,GAAG,KAAG,UAAOC,MAAG,UAAOA,KAAE,QAAM,KAAK,KAAKA,KAAE,SAAM,OAAM,GAAG,KAAG,UAAOC,MAAG,UAAOA,KAAE,QAAM,KAAK,KAAKA,KAAE,SAAM,OAAM,GAAG;AAAA,QAAE;AAAjN,eAAAJ,IAAA;AAAkN,QAAAD,GAAE,oBAAkB,SAASA,IAAE;AAAC,iBAAOC,GAAED,MAAG,KAAG,KAAIA,MAAG,IAAE,KAAI,MAAIA,EAAC;AAAA,QAAC,GAAEA,GAAE,qBAAmBC;AAAA,MAAC,EAAE,MAAIA,GAAE,MAAI,IAAE,CAAA,EAAG,GAAE,SAASD,IAAE;AAAC,iBAASC,GAAED,IAAEC,IAAEC,IAAE;AAAC,gBAAMC,KAAEH,MAAG,KAAG,KAAII,KAAEJ,MAAG,KAAG,KAAIK,KAAEL,MAAG,IAAE;AAAI,cAAIM,KAAEL,MAAG,KAAG,KAAIM,KAAEN,MAAG,KAAG,KAAIO,KAAEP,MAAG,IAAE,KAAIS,KAAE,EAAE,EAAE,mBAAmBJ,IAAEC,IAAEC,EAAC,GAAE,EAAE,mBAAmBL,IAAEC,IAAEC,EAAC,CAAC;AAAE,iBAAKK,KAAER,OAAII,KAAE,KAAGC,KAAE,KAAGC,KAAE,KAAI,CAAAF,MAAG,KAAK,IAAI,GAAE,KAAK,KAAK,MAAGA,EAAC,CAAC,GAAEC,MAAG,KAAK,IAAI,GAAE,KAAK,KAAK,MAAGA,EAAC,CAAC,GAAEC,MAAG,KAAK,IAAI,GAAE,KAAK,KAAK,MAAGA,EAAC,CAAC,GAAEE,KAAE,EAAE,EAAE,mBAAmBJ,IAAEC,IAAEC,EAAC,GAAE,EAAE,mBAAmBL,IAAEC,IAAEC,EAAC,CAAC;AAAE,kBAAOC,MAAG,KAAGC,MAAG,KAAGC,MAAG,IAAE,SAAO;AAAA,QAAC;AAA3W,eAAAP,IAAA;AAA4W,iBAASM,GAAEP,IAAEC,IAAEC,IAAE;AAAC,gBAAMC,KAAEH,MAAG,KAAG,KAAII,KAAEJ,MAAG,KAAG,KAAIK,KAAEL,MAAG,IAAE;AAAI,cAAIM,KAAEL,MAAG,KAAG,KAAIM,KAAEN,MAAG,KAAG,KAAIO,KAAEP,MAAG,IAAE,KAAIS,KAAE,EAAE,EAAE,mBAAmBJ,IAAEC,IAAEC,EAAC,GAAE,EAAE,mBAAmBL,IAAEC,IAAEC,EAAC,CAAC;AAAE,iBAAKK,KAAER,OAAII,KAAE,OAAKC,KAAE,OAAKC,KAAE,OAAM,CAAAF,KAAE,KAAK,IAAI,KAAIA,KAAE,KAAK,KAAK,OAAI,MAAIA,GAAE,CAAC,GAAEC,KAAE,KAAK,IAAI,KAAIA,KAAE,KAAK,KAAK,OAAI,MAAIA,GAAE,CAAC,GAAEC,KAAE,KAAK,IAAI,KAAIA,KAAE,KAAK,KAAK,OAAI,MAAIA,GAAE,CAAC,GAAEE,KAAE,EAAE,EAAE,mBAAmBJ,IAAEC,IAAEC,EAAC,GAAE,EAAE,mBAAmBL,IAAEC,IAAEC,EAAC,CAAC;AAAE,kBAAOC,MAAG,KAAGC,MAAG,KAAGC,MAAG,IAAE,SAAO;AAAA,QAAC;AAA5Y,eAAAD,IAAA;AAA6Y,QAAAP,GAAE,QAAM,SAASA,IAAEC,IAAE;AAAC,cAAG,KAAG,MAAIA,MAAG,KAAI,MAAI,EAAE,QAAOA;AAAE,gBAAMM,KAAEN,MAAG,KAAG,KAAIO,KAAEP,MAAG,KAAG,KAAIQ,KAAER,MAAG,IAAE,KAAIS,KAAEV,MAAG,KAAG,KAAIW,KAAEX,MAAG,KAAG,KAAIY,KAAEZ,MAAG,IAAE;AAAI,iBAAOE,KAAEQ,KAAE,KAAK,OAAOH,KAAEG,MAAG,CAAC,GAAEP,KAAEQ,KAAE,KAAK,OAAOH,KAAEG,MAAG,CAAC,GAAE,IAAEC,KAAE,KAAK,OAAOH,KAAEG,MAAG,CAAC,GAAE,EAAE,OAAOV,IAAEC,IAAE,CAAC;AAAA,QAAC,GAAEH,GAAE,sBAAoB,SAASA,IAAEE,IAAEC,IAAE;AAAC,gBAAMC,KAAE,EAAE,kBAAkBJ,MAAG,CAAC,GAAEK,KAAE,EAAE,kBAAkBH,MAAG,CAAC;AAAE,cAAG,EAAEE,IAAEC,EAAC,IAAEF,IAAE;AAAC,gBAAGE,KAAED,IAAE;AAAC,oBAAMC,KAAEJ,GAAED,IAAEE,IAAEC,EAAC,GAAEG,KAAE,EAAEF,IAAE,EAAE,kBAAkBC,MAAG,CAAC,CAAC;AAAE,kBAAGC,KAAEH,IAAE;AAAC,sBAAMF,KAAEM,GAAEP,IAAEE,IAAEC,EAAC;AAAE,uBAAOG,KAAE,EAAEF,IAAE,EAAE,kBAAkBH,MAAG,CAAC,CAAC,IAAEI,KAAEJ;AAAA,cAAC;AAAC,qBAAOI;AAAA,YAAC;AAAC,kBAAMC,KAAEC,GAAEP,IAAEE,IAAEC,EAAC,GAAEK,KAAE,EAAEJ,IAAE,EAAE,kBAAkBE,MAAG,CAAC,CAAC;AAAE,gBAAGE,KAAEL,IAAE;AAAC,oBAAME,KAAEJ,GAAED,IAAEE,IAAEC,EAAC;AAAE,qBAAOK,KAAE,EAAEJ,IAAE,EAAE,kBAAkBC,MAAG,CAAC,CAAC,IAAEC,KAAED;AAAA,YAAC;AAAC,mBAAOC;AAAA,UAAC;AAAA,QAAC,GAAEN,GAAE,kBAAgBC,IAAED,GAAE,oBAAkBO,IAAEP,GAAE,aAAW,SAASA,IAAE;AAAC,iBAAM,CAACA,MAAG,KAAG,KAAIA,MAAG,KAAG,KAAIA,MAAG,IAAE,KAAI,MAAIA,EAAC;AAAA,QAAC;AAAA,MAAC,EAAE,MAAIC,GAAE,OAAK,IAAE,GAAG,GAAEA,GAAE,cAAY,GAAEA,GAAE,gBAAc;AAAA,IAAC,GAAE,KAAI,CAACD,IAAEC,OAAI;AAAC,aAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,kBAAgBA,GAAE,eAAaA,GAAE,eAAa,QAAOA,GAAE,eAAa,MAAK;AAAA,QAAC,cAAa;AAAC,eAAK,aAAW,CAAA,GAAG,KAAK,YAAU;AAAA,QAAE;AAAA,QAAC,IAAI,QAAO;AAAC,iBAAO,KAAK,WAAS,KAAK,SAAO,CAAAD,QAAI,KAAK,WAAW,KAAKA,EAAC,GAAE,EAAC,SAAQ,6BAAI;AAAC,gBAAG,CAAC,KAAK;AAAU,uBAAQC,KAAE,GAAEA,KAAE,KAAK,WAAW,QAAOA,KAAI,KAAG,KAAK,WAAWA,EAAC,MAAID,GAAE,QAAO,KAAK,KAAK,WAAW,OAAOC,IAAE,CAAC;AAAA;AAAA,UAAC,GAAlI,WAAmI,KAAI,KAAK;AAAA,QAAM;AAAA,QAAC,KAAKD,IAAEC,IAAE;AAAC,gBAAMC,KAAE;AAAG,mBAAQF,KAAE,GAAEA,KAAE,KAAK,WAAW,QAAOA,KAAI,CAAAE,GAAE,KAAK,KAAK,WAAWF,EAAC,CAAC;AAAE,mBAAQG,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAD,GAAEC,EAAC,EAAE,KAAK,QAAOH,IAAEC,EAAC;AAAA,QAAC;AAAA,QAAC,UAAS;AAAC,eAAK,eAAgB,GAAC,KAAK,YAAU;AAAA,QAAE;AAAA,QAAC,iBAAgB;AAAC,eAAK,eAAa,KAAK,WAAW,SAAO;AAAA,QAAE;AAAA,MAAC,GAAEA,GAAE,eAAa,SAASD,IAAEC,IAAE;AAAC,eAAOD,GAAG,CAAAA,OAAGC,GAAE,KAAKD,EAAC,CAAC;AAAA,MAAE,GAAEC,GAAE,kBAAgB,SAASD,IAAEC,IAAE;AAAC,eAAOA,GAAE,MAAM,GAAED,GAAG,CAAAA,OAAGC,GAAED,EAAC;MAAG;AAAA,IAAC,GAAE,KAAI,CAACA,IAAEC,OAAI;AAAC,eAASC,GAAEF,IAAE;AAAC,mBAAUC,MAAKD,GAAE,CAAAC,GAAE,QAAO;AAAG,QAAAD,GAAE,SAAO;AAAA,MAAC;AAA5C,aAAAE,IAAA;AAA6C,aAAO,eAAeD,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,4BAA0BA,GAAE,eAAaA,GAAE,eAAaA,GAAE,oBAAkBA,GAAE,aAAW,QAAOA,GAAE,aAAW,MAAK;AAAA,QAAC,cAAa;AAAC,eAAK,eAAa,CAAA,GAAG,KAAK,cAAY;AAAA,QAAE;AAAA,QAAC,UAAS;AAAC,eAAK,cAAY;AAAG,qBAAUD,MAAK,KAAK,aAAa,CAAAA,GAAE,QAAO;AAAG,eAAK,aAAa,SAAO;AAAA,QAAC;AAAA,QAAC,SAASA,IAAE;AAAC,iBAAO,KAAK,aAAa,KAAKA,EAAC,GAAEA;AAAA,QAAC;AAAA,QAAC,WAAWA,IAAE;AAAC,gBAAMC,KAAE,KAAK,aAAa,QAAQD,EAAC;AAAE,iBAAKC,MAAG,KAAK,aAAa,OAAOA,IAAE,CAAC;AAAA,QAAC;AAAA,MAAC,GAAEA,GAAE,oBAAkB,MAAK;AAAA,QAAC,cAAa;AAAC,eAAK,cAAY;AAAA,QAAE;AAAA,QAAC,IAAI,QAAO;AAAC,iBAAO,KAAK,cAAY,SAAO,KAAK;AAAA,QAAM;AAAA,QAAC,IAAI,MAAMD,IAAE;AAAC,eAAK,eAAaA,OAAI,KAAK,WAAS,KAAK,QAAQ,QAAS,GAAC,KAAK,SAAOA;AAAA,QAAE;AAAA,QAAC,QAAO;AAAC,eAAK,QAAM;AAAA,QAAM;AAAA,QAAC,UAAS;AAAC,eAAK,cAAY,MAAG,KAAK,QAAQ,QAAS,GAAC,KAAK,SAAO;AAAA,QAAM;AAAA,MAAC,GAAEC,GAAE,eAAa,SAASD,IAAE;AAAC,eAAM,EAAC,SAAQA,GAAC;AAAA,MAAC,GAAEC,GAAE,eAAaC,IAAED,GAAE,4BAA0B,SAASD,IAAE;AAAC,eAAM,EAAC,SAAQ,6BAAIE,GAAEF,EAAC,GAAP,WAAQ;AAAA,MAAC;AAAA,IAAC,GAAE,KAAI,CAACA,IAAEC,OAAI;AAAC,aAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,aAAWA,GAAE,YAAU;AAAA,MAAO,MAAMC,GAAC;AAAA,eAAA;AAAA;AAAA;AAAA,QAAC,cAAa;AAAC,eAAK,QAAM,CAAA;AAAA,QAAE;AAAA,QAAC,IAAIF,IAAEC,IAAEC,IAAE;AAAC,eAAK,MAAMF,EAAC,MAAI,KAAK,MAAMA,EAAC,IAAE,CAAE,IAAE,KAAK,MAAMA,EAAC,EAAEC,EAAC,IAAEC;AAAA,QAAC;AAAA,QAAC,IAAIF,IAAEC,IAAE;AAAC,iBAAO,KAAK,MAAMD,EAAC,IAAE,KAAK,MAAMA,EAAC,EAAEC,EAAC,IAAE;AAAA,QAAM;AAAA,QAAC,QAAO;AAAC,eAAK,QAAM,CAAE;AAAA,QAAA;AAAA,MAAC;AAAC,MAAAA,GAAE,YAAUC,IAAED,GAAE,aAAW,MAAK;AAAA,QAAC,cAAa;AAAC,eAAK,QAAM,IAAIC;AAAA,QAAC;AAAA,QAAC,IAAIF,IAAEC,IAAEE,IAAE,GAAE,GAAE;AAAC,eAAK,MAAM,IAAIH,IAAEC,EAAC,KAAG,KAAK,MAAM,IAAID,IAAEC,IAAE,IAAIC,IAAC,GAAE,KAAK,MAAM,IAAIF,IAAEC,EAAC,EAAE,IAAIE,IAAE,GAAE,CAAC;AAAA,QAAC;AAAA,QAAC,IAAIH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,iBAAO,KAAK,MAAM,IAAIH,IAAEC,EAAC,GAAG,IAAIC,IAAEC,EAAC;AAAA,QAAC;AAAA,QAAC,QAAO;AAAC,eAAK,MAAM,MAAO;AAAA,QAAA;AAAA,MAAC;AAAA,IAAC,GAAE,KAAI,CAACH,IAAEC,OAAI;AAAC,aAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,kBAAgBA,GAAE,yBAAuBA,GAAE,kBAAgB;AAAO,YAAMC,KAAE,aAAYC,KAAE;AAAkB,MAAAF,GAAE,kBAAgB,oBAAI,OAAIA,GAAE,yBAAuB,SAASD,IAAE;AAAC,eAAOA,GAAEG,EAAC,KAAG,CAAA;AAAA,MAAE,GAAEF,GAAE,kBAAgB,SAASD,IAAE;AAAC,YAAGC,GAAE,gBAAgB,IAAID,EAAC,EAAE,QAAOC,GAAE,gBAAgB,IAAID,EAAC;AAAE,cAAM,IAAE,gCAASA,IAAEC,IAAE,GAAE;AAAC,cAAG,MAAI,UAAU,OAAO,OAAM,IAAI,MAAM,kEAAkE;AAAE,WAAC,SAASD,IAAEC,IAAEG,IAAE;AAAC,YAAAH,GAAEC,EAAC,MAAID,KAAEA,GAAEE,EAAC,EAAE,KAAK,EAAC,IAAGH,IAAE,OAAMI,GAAC,CAAC,KAAGH,GAAEE,EAAC,IAAE,CAAC,EAAC,IAAGH,IAAE,OAAMI,GAAC,CAAC,GAAEH,GAAEC,EAAC,IAAED;AAAA,UAAE,EAAE,GAAED,IAAE,CAAC;AAAA,QAAC,GAAtN;AAAwN,eAAO,EAAE,WAAS,MAAIA,IAAEC,GAAE,gBAAgB,IAAID,IAAE,CAAC,GAAE;AAAA,MAAC;AAAA,IAAC,GAAE,IAAG,CAACA,IAAEC,IAAEC,OAAI;AAAC,aAAO,eAAeD,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,qBAAmBA,GAAE,kBAAgBA,GAAE,kBAAgBA,GAAE,kBAAgBA,GAAE,cAAYA,GAAE,eAAaA,GAAE,wBAAsBA,GAAE,kBAAgBA,GAAE,eAAaA,GAAE,oBAAkBA,GAAE,iBAAe;AAAO,YAAME,KAAED,GAAE,GAAG;AAAE,UAAI;AAAE,MAAAD,GAAE,kBAAgB,GAAEE,GAAE,iBAAiB,eAAe,GAAEF,GAAE,qBAAmB,GAAEE,GAAE,iBAAiB,kBAAkB,GAAEF,GAAE,gBAAc,GAAEE,GAAE,iBAAiB,aAAa,GAAEF,GAAE,mBAAiB,GAAEE,GAAE,iBAAiB,gBAAgB,GAAEF,GAAE,yBAAuB,GAAEE,GAAE,iBAAiB,sBAAsB,GAAE,SAASH,IAAE;AAAC,QAAAA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,MAAI,CAAC,IAAE;AAAA,MAAK,EAAE,MAAIC,GAAE,eAAa,IAAE,GAAG,GAAEA,GAAE,eAAa,GAAEE,GAAE,iBAAiB,YAAY,GAAEF,GAAE,mBAAiB,GAAEE,GAAE,iBAAiB,gBAAgB,GAAEF,GAAE,mBAAiB,GAAEE,GAAE,iBAAiB,gBAAgB,GAAEF,GAAE,mBAAiB,GAAEE,GAAE,iBAAiB,gBAAgB,GAAEF,GAAE,sBAAoB,GAAEE,GAAE,iBAAiB,mBAAmB;AAAA,IAAC,EAAC,GAAE,IAAE,CAAA;AAAG,aAAS,EAAEA,IAAE;AAAC,UAAI,IAAE,EAAEA,EAAC;AAAE,UAAG,WAAS,EAAE,QAAO,EAAE;AAAQ,UAAI,IAAE,EAAEA,EAAC,IAAE,EAAC,SAAQ,CAAE,EAAA;AAAE,aAAO,EAAEA,EAAC,EAAE,KAAK,EAAE,SAAQ,GAAE,EAAE,SAAQ,CAAC,GAAE,EAAE;AAAA,IAAO;AAA1H;AAA2H,QAAI,IAAE,CAAA;AAAG,YAAO,MAAI;AAAC,UAAIH,KAAE;AAAE,aAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,uBAAqBA,GAAE,iBAAe;AAAO,YAAMC,KAAE,EAAE,GAAG;AAAE,eAAS,EAAED,IAAEC,IAAEC,IAAE;AAAC,eAAO,KAAK,IAAID,IAAE,KAAK,IAAID,IAAEE,EAAC,CAAC;AAAA,MAAC;AAAzC;AAAA,MAA0C,MAAM,EAAC;AAAA,eAAA;AAAA;AAAA;AAAA,QAAC,YAAYF,IAAE;AAAC,eAAK,UAAQA;AAAA,QAAC;AAAA,QAAC,UAAUA,IAAEC,IAAE;AAAC,gBAAMC,KAAE,KAAK,QAAQ,eAAcC,KAAE,KAAK,QAAQ;AAAc,cAAIC,KAAEF;AAAE,gBAAMG,KAAEL,GAAE,MAAM,GAAEM,KAAEN,GAAE,IAAI,GAAEO,KAAEP,GAAE,MAAM,GAAEQ,KAAER,GAAE,IAAI;AAAE,eAAK,iBAAiBM,KAAED,IAAEA,IAAEC,EAAC;AAAE,mBAAQL,KAAEI,IAAEJ,MAAGK,IAAEL,MAAI;AAAC,kBAAMI,KAAE,KAAK,QAAQ,QAAQJ,EAAC;AAAE,gBAAGI,IAAE;AAAC,oBAAMC,KAAEL,OAAID,GAAE,MAAM,IAAEO,KAAE,GAAEE,KAAER,OAAID,GAAE,IAAI,IAAEQ,KAAEH,GAAE;AAAO,uBAAQL,KAAEM,IAAEN,KAAES,IAAET,MAAI;AAAC,sBAAMM,KAAED,GAAE,QAAQL,IAAEI,OAAIF,KAAEC,KAAED,EAAC;AAAE,gBAAAI,MAAG,KAAK,UAAUA,IAAEF,IAAEH,IAAED,EAAC,GAAEI,KAAEE,MAAG,QAAQ,KAAK,yBAAyBL,EAAC,SAASD,EAAC,EAAE;AAAA,cAAC;AAAA,YAAC;AAAC,iBAAK,QAAQC,IAAEA,OAAIK,EAAC;AAAA,UAAC;AAAC,iBAAO,KAAK,gBAAiB,GAAC,KAAK,iBAAiBL,EAAC;AAAA,QAAC;AAAA,QAAC,UAAUD,IAAEC,IAAEC,IAAEC,IAAE;AAAA,QAAE;AAAA,QAAA,QAAQH,IAAEC,IAAE;AAAA,QAAE;AAAA,QAAA,iBAAiBD,IAAEC,IAAEC,IAAE;AAAA,QAAE;AAAA,QAAA,kBAAiB;AAAA,QAAA;AAAA,QAAE,iBAAiBF,IAAE;AAAC,iBAAM;AAAA,QAAE;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAEC,IAAE;AAAC,eAAOD,GAAE,eAAc,MAAKC,GAAE,eAAc,KAAID,GAAE,iBAAeC,GAAE,WAAY;AAAA,MAAA;AAAtF;AAAuF,eAAS,EAAED,IAAEC,IAAE;AAAC,eAAOD,GAAE,qBAAmBC,GAAE,eAAgB,KAAED,GAAE,WAAY,MAAGC,GAAE,WAAU;AAAA,MAAE;AAAtF;AAAuF,eAAS,EAAED,IAAEC,IAAE;AAAC,eAAOD,GAAE,UAAS,MAAKC,GAAE,UAAS,KAAID,GAAE,OAAM,MAAKC,GAAE,OAAQ,KAAED,GAAE,YAAa,MAAGC,GAAE,YAAW,KAAID,GAAE,WAAU,MAAKC,GAAE,gBAAcD,GAAE,cAAYC,GAAE,aAAWD,GAAE,YAAa,MAAGC,GAAE,YAAa,KAAED,GAAE,SAAQ,MAAKC,GAAE,SAAQ,KAAID,GAAE,MAAO,MAAGC,GAAE,MAAO,KAAED,GAAE,gBAAiB,MAAGC,GAAE,gBAAiB;AAAA,MAAA;AAArS;AAAA,MAAsS,MAAM,UAAU,EAAC;AAAA,eAAA;AAAA;AAAA;AAAA,QAAC,YAAYD,IAAEC,IAAE;AAAC,gBAAMD,EAAC,GAAE,KAAK,YAAUC,IAAE,KAAK,YAAU,GAAE,KAAK,WAAS,IAAI,SAAM,KAAK,oBAAkB,IAAI,SAAM,KAAK,cAAY,IAAG,KAAK,iBAAe,GAAE,KAAK,eAAa,KAAK,QAAQ,eAAc,KAAK,kBAAgB,GAAE,KAAK,kBAAgB,GAAE,KAAK,kBAAgB,KAAK,QAAQ,eAAc,KAAK,YAAU,GAAE,KAAK,iBAAe,GAAE,KAAK,iBAAe,GAAE,KAAK,wBAAsB,GAAE,KAAK,wBAAsB,GAAE,KAAK,mBAAiB,KAAK,QAAQ,YAAW,GAAG,KAAK,yBAAuB,KAAK,QAAQ,YAAW,GAAG,KAAK,oBAAkB,KAAK,QAAQ,YAAW;AAAA,QAAE;AAAA,QAAC,iBAAiBD,IAAEC,IAAEC,IAAE;AAAC,eAAK,WAAS,IAAI,MAAMF,EAAC,GAAE,KAAK,wBAAsBC,IAAE,KAAK,iBAAeA,IAAE,KAAK,YAAUA;AAAA,QAAC;AAAA,QAAC,QAAQD,IAAEC,IAAE;AAAC,eAAK,iBAAe,KAAG,CAAC,EAAE,KAAK,cAAa,KAAK,eAAe,MAAI,KAAK,eAAa,QAAK,KAAK,cAAc;AAAK,cAAIC,KAAE;AAAG,cAAG,CAACD,IAAE;AAAC,YAAAD,KAAE,KAAK,aAAW,KAAK,UAAU,QAAM,KAAK,QAAQ,QAAQ,KAAK,eAAe,GAAG,QAAQ,KAAK,iBAAgB,KAAK,eAAe;AAAE,kBAAMC,KAAE,KAAK,QAAQ,QAAQD,EAAC,GAAEG,KAAE,KAAK,QAAQ,QAAQH,KAAE,CAAC;AAAE,gBAAGG,GAAE,WAAU;AAAC,cAAAD,KAAE;AAAG,oBAAME,KAAEH,GAAE,QAAQA,GAAE,SAAO,GAAE,KAAK,gBAAgB,GAAEI,KAAEJ,GAAE,QAAQA,GAAE,SAAO,GAAE,KAAK,sBAAsB,GAAEK,KAAEH,GAAE,QAAQ,GAAE,KAAK,iBAAiB,GAAEK,KAAEF,GAAE,SAAQ,IAAG;AAAE,kBAAIG,KAAE;AAAG,eAACH,GAAE,SAAQ,KAAIE,KAAE,KAAK,kBAAgB,IAAE,KAAK,kBAAgB,QAAMJ,GAAE,SAAQ,KAAI,MAAIA,GAAE,SAAU,MAAG,EAAEA,IAAEE,EAAC,MAAIG,KAAE,OAAID,OAAIH,GAAE,SAAU,KAAE,MAAIA,GAAE,SAAU,MAAG,EAAED,IAAEE,EAAC,KAAG,EAAED,IAAEC,EAAC,MAAIG,KAAE,QAAKA,OAAIP,KAAE,IAAI,OAAO,KAAK,iBAAe,CAAC,GAAEA,MAAG,kBAAW,KAAK,iBAAe,MAAIA,MAAG,UAAMA,MAAG,QAAKD,GAAE,SAAO,KAAK,cAAc,KAAIC,MAAG,QAAK,KAAK,cAAc,KAAIA,MAAG,QAAKD,GAAE,SAAO,KAAK,cAAc,KAAIC,MAAG,WAAO,KAAK,wBAAsBF,KAAE,GAAE,KAAK,wBAAsB,GAAE,KAAK,iBAAeA,KAAE,GAAE,KAAK,iBAAe;AAAA,YAAE,MAAM,CAAAE,KAAE,QAAO,KAAK,iBAAeF,KAAE,GAAE,KAAK,iBAAe;AAAA,UAAC;AAAC,eAAK,SAAS,KAAK,SAAS,IAAE,KAAK,aAAY,KAAK,kBAAkB,KAAK,WAAW,IAAEE,IAAE,KAAK,cAAY,IAAG,KAAK,iBAAe;AAAA,QAAC;AAAA,QAAC,WAAWF,IAAEC,IAAE;AAAC,gBAAMC,KAAE,CAAE,GAACC,KAAE,CAAC,EAAEH,IAAEC,EAAC,GAAEG,KAAE,CAAC,EAAEJ,IAAEC,EAAC,GAAEI,KAAE,CAAC,EAAEL,IAAEC,EAAC;AAAE,cAAGE,MAAGC,MAAGC,GAAE,KAAGL,GAAE,qBAAqB,CAAAC,GAAE,mBAAoB,KAAEC,GAAE,KAAK,CAAC;AAAA,eAAM;AAAC,gBAAGC,IAAE;AAAC,oBAAMF,KAAED,GAAE,WAAY;AAAC,cAAAA,GAAE,QAAS,IAACE,GAAE,KAAK,IAAG,GAAED,OAAI,KAAG,KAAIA,OAAI,IAAE,KAAI,MAAIA,EAAC,IAAED,GAAE,YAAW,IAAGC,MAAG,KAAGC,GAAE,KAAK,IAAG,GAAED,EAAC,IAAEC,GAAE,KAAK,IAAED,KAAE,MAAI,IAAEA,MAAG,MAAI,IAAEA,GAAE,IAAEC,GAAE,KAAK,EAAE;AAAA,YAAC;AAAC,gBAAGE,IAAE;AAAC,oBAAMH,KAAED,GAAE,WAAU;AAAG,cAAAA,GAAE,QAAO,IAAGE,GAAE,KAAK,IAAG,GAAED,OAAI,KAAG,KAAIA,OAAI,IAAE,KAAI,MAAIA,EAAC,IAAED,GAAE,YAAW,IAAGC,MAAG,KAAGC,GAAE,KAAK,IAAG,GAAED,EAAC,IAAEC,GAAE,KAAK,IAAED,KAAE,OAAK,IAAEA,MAAG,MAAI,IAAEA,GAAE,IAAEC,GAAE,KAAK,EAAE;AAAA,YAAC;AAAC,YAAAG,OAAIL,GAAE,UAAW,MAAGC,GAAE,UAAW,KAAEC,GAAE,KAAKF,GAAE,UAAW,IAAC,IAAE,EAAE,GAAEA,GAAE,OAAM,MAAKC,GAAE,OAAM,KAAIC,GAAE,KAAKF,GAAE,OAAM,IAAG,IAAE,EAAE,GAAEA,GAAE,YAAW,MAAKC,GAAE,iBAAeC,GAAE,KAAKF,GAAE,gBAAc,IAAE,EAAE,GAAEA,GAAE,WAAY,MAAGC,GAAE,WAAY,KAAEC,GAAE,KAAKF,GAAE,WAAY,IAAC,KAAG,EAAE,GAAEA,GAAE,QAAO,MAAKC,GAAE,QAAO,KAAIC,GAAE,KAAKF,GAAE,QAAO,IAAG,IAAE,EAAE,GAAEA,GAAE,YAAW,MAAKC,GAAE,YAAa,KAAEC,GAAE,KAAKF,GAAE,YAAa,IAAC,IAAE,EAAE,GAAEA,GAAE,SAAQ,MAAKC,GAAE,SAAQ,KAAIC,GAAE,KAAKF,GAAE,SAAQ,IAAG,IAAE,EAAE,GAAEA,GAAE,MAAO,MAAGC,GAAE,MAAO,KAAEC,GAAE,KAAKF,GAAE,MAAO,IAAC,IAAE,EAAE,GAAEA,GAAE,gBAAiB,MAAGC,GAAE,gBAAe,KAAIC,GAAE,KAAKF,GAAE,gBAAe,IAAG,IAAE,EAAE;AAAA,UAAE;AAAC,iBAAOE;AAAA,QAAC;AAAA,QAAC,UAAUF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAG,MAAIH,GAAE,SAAQ,EAAG;AAAO,gBAAMI,KAAE,OAAKJ,GAAE,SAAQ,GAAGK,KAAE,KAAK,WAAWL,IAAE,KAAK,YAAY;AAAE,cAAGI,KAAE,CAAC,EAAE,KAAK,cAAaJ,EAAC,IAAEK,GAAE,SAAO,GAAE;AAAC,iBAAK,iBAAe,MAAI,EAAE,KAAK,cAAa,KAAK,eAAe,MAAI,KAAK,eAAa,QAAK,KAAK,cAAc,MAAK,KAAK,eAAa,QAAK,KAAK,cAAc,KAAI,KAAK,iBAAe,IAAG,KAAK,wBAAsB,KAAK,iBAAeH,IAAE,KAAK,wBAAsB,KAAK,iBAAeC,IAAE,KAAK,eAAa,QAAKE,GAAE,KAAK,GAAG,CAAC;AAAI,kBAAML,KAAE,KAAK,QAAQ,QAAQE,EAAC;AAAE,uBAASF,OAAIA,GAAE,QAAQG,IAAE,KAAK,YAAY,GAAE,KAAK,kBAAgBD,IAAE,KAAK,kBAAgBC;AAAA,UAAE;AAAC,UAAAC,KAAE,KAAK,kBAAgBJ,GAAE,SAAU,KAAE,KAAK,iBAAe,MAAI,EAAE,KAAK,cAAa,KAAK,eAAe,MAAI,KAAK,eAAa,QAAK,KAAK,cAAc,MAAK,KAAK,eAAa,QAAK,KAAK,cAAc,KAAI,KAAK,iBAAe,IAAG,KAAK,eAAaA,GAAE,SAAU,GAAC,KAAK,wBAAsB,KAAK,iBAAeE,IAAE,KAAK,wBAAsB,KAAK,iBAAeC,KAAEH,GAAE,SAAU;AAAA,QAAC;AAAA,QAAC,iBAAiBA,IAAE;AAAC,cAAIC,KAAE,KAAK,SAAS;AAAO,eAAK,QAAQ,SAAO,KAAK,aAAW,KAAK,UAAU,SAAOA,KAAE,KAAK,wBAAsB,IAAE,KAAK,WAAU,KAAK,iBAAe,KAAK,uBAAsB,KAAK,iBAAe,KAAK;AAAuB,cAAIC,KAAE;AAAG,mBAAQF,KAAE,GAAEA,KAAEC,IAAED,KAAI,CAAAE,MAAG,KAAK,SAASF,EAAC,GAAEA,KAAE,IAAEC,OAAIC,MAAG,KAAK,kBAAkBF,EAAC;AAAG,cAAG,CAACA,IAAE;AAAC,kBAAMA,KAAE,KAAK,QAAQ,QAAM,KAAK,QAAQ,SAAQC,KAAE,KAAK,QAAQ,SAAQG,KAAE,wBAAAJ,OAAG;AAAC,cAAAA,KAAE,IAAEE,MAAG,QAAKF,EAAC,MAAIA,KAAE,MAAIE,MAAG,QAAK,CAACF,EAAC;AAAA,YAAI,GAAzC;AAA2C,aAACA,OAAI,KAAK,kBAAgBC,OAAI,KAAK,qBAAmBE,KAAEH,KAAE,KAAK,kBAAgB,IAAEE,MAAG,QAAKC,EAAC,MAAIA,KAAE,MAAID,MAAG,QAAK,CAACC,EAAC,MAAKC,GAAEH,KAAE,KAAK,cAAc;AAAA,UAAE;AAAC,cAAIE;AAAE,gBAAMC,KAAE,KAAK,UAAU,MAAM,cAAc,cAAaC,KAAE,KAAK,WAAWD,IAAE,KAAK,YAAY;AAAE,iBAAOC,GAAE,SAAO,MAAIH,MAAG,QAAKG,GAAE,KAAK,GAAG,CAAC,MAAKH;AAAA,QAAC;AAAA,MAAC;AAAC,MAAAF,GAAE,iBAAe,MAAK;AAAA,QAAC,SAASA,IAAE;AAAC,eAAK,YAAUA;AAAA,QAAC;AAAA,QAAC,6BAA6BA,IAAEC,IAAEC,IAAE;AAAC,gBAAMC,KAAEF,GAAE,QAAOI,KAAE,WAASH,KAAEC,KAAE,EAAED,KAAEF,GAAE,MAAK,GAAEG,EAAC;AAAE,iBAAO,KAAK,wBAAwBH,IAAEC,IAAE,EAAC,OAAME,KAAEE,IAAE,KAAIF,KAAE,EAAC,GAAE,KAAE;AAAA,QAAC;AAAA,QAAC,wBAAwBH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,iBAAO,IAAI,EAAEF,IAAED,EAAC,EAAE,UAAU,EAAC,OAAM,EAAC,GAAE,GAAE,GAAE,YAAU,OAAOE,GAAE,QAAMA,GAAE,QAAMA,GAAE,MAAM,KAAI,GAAE,KAAI,EAAC,GAAEF,GAAE,MAAK,GAAE,YAAU,OAAOE,GAAE,MAAIA,GAAE,MAAIA,GAAE,IAAI,KAAI,EAAC,GAAEC,EAAC;AAAA,QAAC;AAAA,QAAC,uBAAuBH,IAAEC,IAAE;AAAC,gBAAMC,KAAEF,GAAE,OAAO,QAAOG,KAAE,IAAI,EAAED,IAAEF,IAAEC,EAAC;AAAE,cAAG,CAACA,GAAE,eAAc;AAAC,kBAAMI,KAAEH,GAAE,QAAOI,KAAEL,GAAE,YAAWM,KAAE,WAASD,KAAED,KAAE,EAAEC,KAAEN,GAAE,MAAK,GAAEK,EAAC;AAAE,mBAAOF,GAAE,UAAU,EAAC,OAAM,EAAC,GAAE,GAAE,GAAEE,KAAEE,GAAC,GAAE,KAAI,EAAC,GAAEP,GAAE,MAAK,GAAEK,KAAE,EAAC,EAAC,CAAC;AAAA,UAAC;AAAC,gBAAMA,KAAE,KAAK,WAAW;AAAuB,iBAAO,WAASA,KAAEF,GAAE,UAAU,EAAC,OAAM,EAAC,GAAEE,GAAE,MAAM,GAAE,GAAEA,GAAE,MAAM,EAAC,GAAE,KAAI,EAAC,GAAEA,GAAE,IAAI,GAAE,GAAEA,GAAE,IAAI,EAAC,EAAC,CAAC,IAAE;AAAA,QAAE;AAAA,QAAC,gBAAgBL,IAAE;AAAC,cAAIC,KAAE;AAAG,gBAAMC,KAAEF,GAAE;AAAM,cAAGE,GAAE,8BAA4BD,MAAG,aAASC,GAAE,0BAAwBD,MAAG,cAAUC,GAAE,uBAAqBD,MAAG,gBAAYC,GAAE,eAAaD,MAAG,YAAQC,GAAE,eAAaD,MAAG,aAASC,GAAE,0BAAwBD,MAAG,cAAUC,GAAE,kBAAgBD,MAAG,gBAAY,UAAKC,GAAE,mBAAiBD,MAAG,aAAS,WAASC,GAAE,kBAAkB,SAAOA,GAAE,mBAAmB;AAAA,YAAA,KAAI;AAAM,cAAAD,MAAG;AAAQ;AAAA,YAAM,KAAI;AAAQ,cAAAA,MAAG;AAAW;AAAA,YAAM,KAAI;AAAO,cAAAA,MAAG;AAAW;AAAA,YAAM,KAAI;AAAM,cAAAA,MAAG;AAAA,UAAU;AAAC,iBAAOA;AAAA,QAAC;AAAA,QAAC,UAAUD,IAAE;AAAC,cAAG,CAAC,KAAK,UAAU,OAAM,IAAI,MAAM,2CAA2C;AAAE,cAAIC,KAAED,IAAG,QAAM,KAAK,wBAAwB,KAAK,WAAU,KAAK,UAAU,OAAO,QAAOA,GAAE,OAAM,IAAE,IAAE,KAAK,6BAA6B,KAAK,WAAU,KAAK,UAAU,OAAO,QAAOA,IAAG,UAAU;AAAE,iBAAOA,IAAG,oBAAkB,gBAAc,KAAK,UAAU,OAAO,OAAO,SAAOC,MAAG,oBAAc,KAAK,6BAA6B,KAAK,WAAU,KAAK,UAAU,OAAO,WAAU,MAAM,CAAC,KAAID,IAAG,iBAAeC,MAAG,KAAK,gBAAgB,KAAK,SAAS,IAAGA;AAAA,QAAC;AAAA,QAAC,gBAAgBD,IAAE;AAAC,cAAG,CAAC,KAAK,UAAU,OAAM,IAAI,MAAM,2CAA2C;AAAE,iBAAO,KAAK,uBAAuB,KAAK,WAAUA,MAAG,CAAE,CAAA;AAAA,QAAC;AAAA,QAAC,UAAS;AAAA,QAAA;AAAA,MAAE;AAAA,MAAE,MAAM,UAAU,EAAC;AAAA,eAAA;AAAA;AAAA;AAAA,QAAC,YAAYA,IAAEE,IAAEC,IAAE;AAAC,gBAAMH,EAAC,GAAE,KAAK,YAAUE,IAAE,KAAK,WAASC,IAAE,KAAK,cAAY,IAAG,KAAK,eAAa,IAAGD,GAAE,MAAM,gBAAc,KAAK,cAAYA,GAAE,MAAM,cAAc,OAAO,OAAK,KAAK,cAAYD,GAAE;AAAA,QAAmB;AAAA,QAAC,UAAUD,IAAEC,IAAEC,IAAE;AAAC,iBAAOD,OAAI,GAAEC,KAAEA,MAAG,KAAIF,GAAE,SAAOC,KAAED,OAAIC,MAAGD,GAAE,UAAQE,GAAE,WAASA,MAAGA,GAAE,OAAOD,KAAEC,GAAE,MAAM,IAAGA,GAAE,MAAM,GAAED,EAAC,IAAED;AAAA,QAAE;AAAA,QAAC,iBAAiBA,IAAEC,IAAEC,IAAE;AAAC,eAAK,gBAAc;AAA8C,cAAIC,KAAE,WAAUC,KAAE;AAAU,eAAK,SAAS,4BAA0BD,KAAE,KAAK,UAAU,QAAQ,OAAO,cAAY,WAAUC,KAAE,KAAK,UAAU,QAAQ,OAAO,cAAY;AAAW,gBAAMC,KAAE,CAAA;AAAG,UAAAA,GAAE,KAAK,YAAUF,KAAE,GAAG,GAAEE,GAAE,KAAK,uBAAqBD,KAAE,GAAG,GAAEC,GAAE,KAAK,kBAAgB,KAAK,UAAU,QAAQ,aAAW,GAAG,GAAEA,GAAE,KAAK,gBAAc,KAAK,UAAU,QAAQ,WAAS,KAAK,GAAE,KAAK,gBAAc,iBAAeA,GAAE,KAAK,GAAG,IAAE;AAAA,QAAI;AAAA,QAAC,kBAAiB;AAAC,eAAK,gBAAc,UAAS,KAAK,gBAAc;AAAA,QAA8C;AAAA,QAAC,QAAQL,IAAEC,IAAE;AAAC,eAAK,gBAAc,gBAAc,KAAK,cAAY,iBAAgB,KAAK,cAAY;AAAA,QAAE;AAAA,QAAC,aAAaD,IAAEC,IAAE;AAAC,gBAAMC,KAAED,KAAED,GAAE,WAAY,IAACA,GAAE,WAAU;AAAG,kBAAOC,KAAED,GAAE,QAAS,IAACA,GAAE,QAAS,KAAE,MAAI,CAACE,MAAG,KAAG,KAAIA,MAAG,IAAE,KAAI,MAAIA,EAAC,EAAE,IAAK,CAAAF,OAAG,KAAK,UAAUA,GAAE,SAAS,EAAE,GAAE,GAAE,GAAG,CAAG,EAAC,KAAK,EAAE,KAAGC,KAAED,GAAE,YAAW,IAAGA,GAAE,YAAa,KAAE,KAAK,YAAYE,EAAC,EAAE,MAAI;AAAA,QAAM;AAAA,QAAC,WAAWF,IAAEC,IAAE;AAAC,gBAAMC,KAAE,CAAE,GAACC,KAAE,CAAC,EAAEH,IAAEC,EAAC,GAAEG,KAAE,CAAC,EAAEJ,IAAEC,EAAC,GAAEI,KAAE,CAAC,EAAEL,IAAEC,EAAC;AAAE,cAAGE,MAAGC,MAAGC,IAAE;AAAC,kBAAMJ,KAAE,KAAK,aAAaD,IAAE,IAAE;AAAE,YAAAC,MAAGC,GAAE,KAAK,YAAUD,KAAE,GAAG;AAAE,kBAAME,KAAE,KAAK,aAAaH,IAAE,KAAE;AAAE,mBAAOG,MAAGD,GAAE,KAAK,uBAAqBC,KAAE,GAAG,GAAEH,GAAE,UAAS,KAAIE,GAAE,KAAK,4CAA4C,GAAEF,GAAE,YAAUE,GAAE,KAAK,oBAAoB,GAAEF,GAAE,YAAa,KAAEA,GAAE,WAAU,IAAGE,GAAE,KAAK,sCAAsC,IAAEF,GAAE,YAAa,IAACE,GAAE,KAAK,6BAA6B,IAAEF,GAAE,WAAU,KAAIE,GAAE,KAAK,4BAA4B,GAAEF,GAAE,QAAS,KAAEE,GAAE,KAAK,yBAAyB,GAAEF,GAAE,iBAAeE,GAAE,KAAK,qBAAqB,GAAEF,GAAE,SAAU,KAAEE,GAAE,KAAK,qBAAqB,GAAEF,GAAE,MAAK,KAAIE,GAAE,KAAK,eAAe,GAAEF,GAAE,gBAAe,KAAIE,GAAE,KAAK,gCAAgC,GAAEA;AAAA,UAAC;AAAA,QAAC;AAAA,QAAC,UAAUF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAG,MAAIH,GAAE,SAAU,EAAC;AAAO,gBAAMI,KAAE,OAAKJ,GAAE,SAAQ,GAAGK,KAAE,KAAK,WAAWL,IAAEC,EAAC;AAAE,UAAAI,OAAI,KAAK,eAAa,MAAIA,GAAE,SAAO,kBAAgB,yBAAuBA,GAAE,KAAK,GAAG,IAAE,OAAM,KAAK,eAAaD,KAAE,MAAIJ,GAAE,SAAQ;AAAA,QAAE;AAAA,QAAC,mBAAkB;AAAC,iBAAO,KAAK;AAAA,QAAY;AAAA,MAAC;AAAC,MAAAA,GAAE,uBAAqB;AAAA,IAAC,MAAK;AAAA,EAAC,GAAC;;;;ACIjkxB,SAAS,oBAAoB;AAC5B,QAAA,iBAAiB,IAAIa,sBAAAA;AACrB,QAAA,WAAW,QAAQ,IAAIC,aAAAA,SAAS,EAAE,YAAY,KAAM,CAAA,CAAC;AAErD,QAAA,SAAS,wBAAC,wBAAkC;AAC5B,wBAAA,MAAM,eAAe,UAAW,CAAA;AAAA,EAAA,GADvC;AAIf,QAAM,QAAQ,wBAAC,YAAoB,SAAS,MAAM,OAAO,GAA3C;AAER,QAAA,YAAY,6BAAM,eAAe,aAArB;AAElB,YAAU,MAAM;AACd,aAAS,UAAU,cAAc;AAAA,EAAA,CAClC;AAED,cAAY,MAAM;AAChB,aAAS,QAAQ;AAAA,EAAA,CAClB;AAEM,SAAA;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ;AAzBgB;;;;;;;;;;;;ACkBV,UAAA,kBAAkBC,SAAuC,SAAA,YAAA;AAC/D,UAAM,QAAQ;AAKd,UAAM,WAAW;AAGjB,UAAM,SAAS;AACf,QAAI,QAAyB;AAG7B,UAAM,kBAAkB,wBACtB,EAAE,UAAU,YAAA,GACZ,SACG;AACK,cAAA;AACR,kBAAY,EAAE,MAAM,UAAU,MAAM,UAAU,MAAM;AAC3C,eAAA,MAAM,MAAM,cAAc;AACnC,aAAO,OAAO,QAAQ;AAEtB,eAAS,QAAQ,cAAc;AAC/B,eAAS,QAAQ,cAAc;AAC/B,eAAS,QAAQ,sBAAsB;AACvC,eAAS,QAAQ,eAAe;AAAA,IAAA,GAZV;AAexB,UAAM,oBAAoB,6BAAM;AACtB,cAAA;AAAA,IAAA,GADgB;AAI1B,cAAU,YAAY;AACX,eAAA,aAAa,CAAC,YAAoB;AACzC,eAAO,MAAM,OAAO;AACpB,eAAO,MAAM,OAAO;AAAA,MAAA,CACrB;AAAA,IAAA,CACF;;;;;;;;;;;;;;;;;;;;", "x_google_ignoreList": [0]}