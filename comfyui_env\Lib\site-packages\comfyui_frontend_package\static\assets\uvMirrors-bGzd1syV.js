const PYTHON_MIRROR = {
  settingId: "Comfy-Desktop.UV.PythonInstallMirror",
  mirror: "https://github.com/astral-sh/python-build-standalone/releases/download",
  fallbackMirror: "https://python-standalone.org/mirror/astral-sh/python-build-standalone",
  validationPathSuffix: "/20250115/cpython-3.10.16+20250115-aarch64-apple-darwin-debug-full.tar.zst.sha256"
};
const PYPI_MIRROR = {
  settingId: "Comfy-Desktop.UV.PypiInstallMirror",
  mirror: "https://pypi.org/simple/",
  fallbackMirror: "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"
};
export {
  PYPI_MIRROR,
  PYTHON_MIRROR
};
//# sourceMappingURL=uvMirrors-bGzd1syV.js.map
