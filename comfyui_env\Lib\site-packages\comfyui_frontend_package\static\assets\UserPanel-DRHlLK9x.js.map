{"version": 3, "file": "UserPanel-DRHlLK9x.js", "sources": ["../../src/components/dialog/content/setting/UserPanel.vue"], "sourcesContent": ["<template>\n  <TabPanel value=\"User\" class=\"user-settings-container h-full\">\n    <div class=\"flex flex-col h-full\">\n      <h2 class=\"text-2xl font-bold mb-2\">{{ $t('userSettings.title') }}</h2>\n      <Divider class=\"mb-3\" />\n\n      <!-- Normal User Panel -->\n      <div v-if=\"isLoggedIn\" class=\"flex flex-col gap-2\">\n        <UserAvatar\n          v-if=\"userPhotoUrl\"\n          :photo-url=\"userPhotoUrl\"\n          shape=\"circle\"\n          size=\"large\"\n        />\n\n        <div class=\"flex flex-col gap-0.5\">\n          <h3 class=\"font-medium\">\n            {{ $t('userSettings.name') }}\n          </h3>\n          <div class=\"text-muted\">\n            {{ userDisplayName || $t('userSettings.notSet') }}\n          </div>\n        </div>\n\n        <div class=\"flex flex-col gap-0.5\">\n          <h3 class=\"font-medium\">\n            {{ $t('userSettings.email') }}\n          </h3>\n          <span class=\"text-muted\">\n            {{ userEmail }}\n          </span>\n        </div>\n\n        <div class=\"flex flex-col gap-0.5\">\n          <h3 class=\"font-medium\">\n            {{ $t('userSettings.provider') }}\n          </h3>\n          <div class=\"text-muted flex items-center gap-1\">\n            <i :class=\"providerIcon\" />\n            {{ providerName }}\n            <Button\n              v-if=\"isEmailProvider\"\n              v-tooltip=\"{\n                value: $t('userSettings.updatePassword'),\n                showDelay: 300\n              }\"\n              icon=\"pi pi-pen-to-square\"\n              severity=\"secondary\"\n              text\n              @click=\"dialogService.showUpdatePasswordDialog()\"\n            />\n          </div>\n        </div>\n\n        <ProgressSpinner\n          v-if=\"loading\"\n          class=\"w-8 h-8 mt-4\"\n          style=\"--pc-spinner-color: #000\"\n        />\n        <Button\n          v-else\n          class=\"mt-4 w-32\"\n          severity=\"secondary\"\n          :label=\"$t('auth.signOut.signOut')\"\n          icon=\"pi pi-sign-out\"\n          @click=\"handleSignOut\"\n        />\n      </div>\n\n      <!-- Login Section -->\n      <div v-else class=\"flex flex-col gap-4\">\n        <p class=\"text-gray-600\">\n          {{ $t('auth.login.title') }}\n        </p>\n\n        <Button\n          class=\"w-52\"\n          severity=\"primary\"\n          :loading=\"loading\"\n          :label=\"$t('auth.login.signInOrSignUp')\"\n          icon=\"pi pi-user\"\n          @click=\"handleSignIn\"\n        />\n      </div>\n    </div>\n  </TabPanel>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport Divider from 'primevue/divider'\nimport ProgressSpinner from 'primevue/progressspinner'\nimport TabPanel from 'primevue/tabpanel'\n\nimport UserAvatar from '@/components/common/UserAvatar.vue'\nimport { useCurrentUser } from '@/composables/auth/useCurrentUser'\nimport { useDialogService } from '@/services/dialogService'\n\nconst dialogService = useDialogService()\nconst {\n  loading,\n  isLoggedIn,\n  isEmailProvider,\n  userDisplayName,\n  userEmail,\n  userPhotoUrl,\n  providerName,\n  providerIcon,\n  handleSignOut,\n  handleSignIn\n} = useCurrentUser()\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkGA,UAAM,gBAAgB;AAChB,UAAA;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}