# 批量生成问题修复指南

## 🚨 问题解决

您遇到的错误：`Required input is missing: image1, image2`

**原因**: `ImageBatch` 节点的参数名称错误
- ❌ 错误: `images1`, `images2`
- ✅ 正确: `image1`, `image2`

## ✅ 已修复的工作流

我已经修复了问题并创建了更好的批量生成方案：

### 🏆 推荐工作流（按优先级）

#### 1. 简单批量10张 ⭐⭐⭐⭐⭐
**文件**: `workflows/flux_simple_batch_10.json`

**特点**:
- ✅ 一次生成10张图片
- ✅ 使用随机种子（更多样化）
- ✅ 无复杂节点，稳定可靠
- ✅ 适合日常使用

#### 2. 可调节批量 ⭐⭐⭐⭐⭐
**文件**: `workflows/flux_adjustable_batch.json`

**特点**:
- ✅ 默认5张，可调节为任意数量
- ✅ 灵活的批量大小控制
- ✅ 适合不同显存配置

#### 3. 原有批量工作流 ⭐⭐⭐⭐
**文件**: `workflows/flux_batch_generation.json`

**特点**:
- ✅ 稳定的10张批量生成
- ✅ 已验证可用

#### 4. 多种子工作流 ⭐⭐⭐
**文件**: `workflows/flux_multi_seed_batch.json` (已修复)

**特点**:
- ✅ 修复了 ImageBatch 参数错误
- ✅ 使用不同种子生成
- ⚠️ 较复杂，适合高级用户

## 🚀 立即使用（推荐）

### 最佳选择：简单批量10张

1. **导入工作流**:
   ```
   拖拽 workflows/flux_simple_batch_10.json 到 ComfyUI 界面
   ```

2. **参数设置**:
   - 批量大小: 10张
   - 种子: -1 (随机，每张都不同)
   - 分辨率: 1024x1024
   - 步数: 25

3. **开始生成**:
   点击 "Queue Prompt"

## 📊 批量大小建议

### 根据您的 RTX 4070 (8GB) 显存：

| 分辨率 | 推荐批量 | 安全批量 | 极限批量 |
|--------|----------|----------|----------|
| 512x512 | 20张 | 15张 | 30张 |
| 768x768 | 12张 | 10张 | 15张 |
| 1024x1024 | 8张 | 5张 | 10张 |
| 1216x832 | 6张 | 4张 | 8张 |

## 🔧 自定义批量数量

### 修改可调节工作流：

在 `flux_adjustable_batch.json` 中找到 "Empty Latent Image" 节点：

```json
"batch_size": 5  // 改为您想要的数量
```

**推荐设置**:
- 快速测试: 3张
- 日常使用: 5-8张
- 大量生成: 10-15张
- 极限生成: 20+张

## ⚙️ 显存优化

### 如果遇到显存不足：

#### 1. 启动参数优化
```bash
# 低显存模式
python main.py --lowvram --auto-launch

# 极低显存模式
python main.py --lowvram --cpu-vae --auto-launch
```

#### 2. 减少批量大小
```
10张 → 5张
5张 → 3张
```

#### 3. 降低分辨率
```
1024x1024 → 768x768
768x768 → 512x512
```

#### 4. 减少步数
```
25步 → 20步
20步 → 15步
```

## 🎯 使用技巧

### 1. 种子策略

#### 随机种子（推荐）:
```json
"seed": -1  // 每张图片都不同
```

#### 固定种子:
```json
"seed": 42  // 所有图片相似但有细微差别
```

### 2. 提示词优化

#### 适合批量的提示词:
```
"a beautiful portrait, natural lighting, photorealistic, masterpiece"
```

#### 避免过于具体:
```
❌ "a woman with blue eyes, wearing red dress, sitting on chair"
✅ "a woman portrait, natural lighting, professional photography"
```

### 3. 文件管理

生成的文件会自动命名：
```
Flux_Batch10_00001.png
Flux_Batch10_00002.png
...
Flux_Batch10_00010.png
```

## 🔍 故障排除

### 1. 显存不足 (CUDA OOM)
```bash
# 解决方案
减少批量大小: 10 → 5
降低分辨率: 1024 → 768
使用 --lowvram 参数
```

### 2. 生成时间过长
```bash
# 优化方案
减少步数: 25 → 20
降低分辨率: 1024 → 768
减少批量: 10 → 5
```

### 3. 结果过于相似
```bash
# 解决方案
使用随机种子: seed: -1
调整提示词
增加变化元素
```

## 🎉 开始批量创作

**推荐流程**:

1. ✅ **导入**: `flux_simple_batch_10.json`
2. 🎨 **调整**: 修改提示词和批量大小
3. 🚀 **生成**: 点击 "Queue Prompt"
4. 📁 **查看**: 检查 `output/` 目录
5. 🔄 **优化**: 根据结果调整参数

现在您可以稳定地批量生成10张高质量 Flux 图片了！🎨✨

**最佳实践**: 从 `flux_simple_batch_10.json` 开始，这是最稳定可靠的批量生成方案。
