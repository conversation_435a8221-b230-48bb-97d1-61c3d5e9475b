{"id": "0061ebf7-f396-45ca-9c38-321e3353d419", "revision": 0, "last_node_id": 62, "last_link_id": 121, "nodes": [{"id": 50, "type": "ConditioningZeroOut", "pos": [130, 360], "size": [317.4000244140625, 26], "flags": {"collapsed": true}, "order": 7, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 98}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [116]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 6, "type": "CLIPTextEncode", "pos": [130, 130], "size": [388.7348327636719, 188.959716796875], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 65}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [98, 115]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["crystal butterfly above the sea, white, hyper detailed, with diamonds"]}, {"id": 58, "type": "ControlNetApplyAdvanced", "pos": [589.7810668945312, -118.3919677734375], "size": [270, 186], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 115}, {"name": "negative", "type": "CONDITIONING", "link": 116}, {"name": "control_net", "type": "CONTROL_NET", "link": 117}, {"name": "image", "type": "IMAGE", "link": 119}, {"name": "vae", "shape": 7, "type": "VAE", "link": 118}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [120]}, {"name": "negative", "type": "CONDITIONING", "links": [121]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [1, 0, 1]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [591.5077514648438, 126.07306671142578], "size": [270.849609375, 262], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 14}, {"name": "positive", "type": "CONDITIONING", "link": 120}, {"name": "negative", "type": "CONDITIONING", "link": 121}, {"name": "latent_image", "type": "LATENT", "link": 66}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [63]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [268264726798396, "randomize", 30, 4, "euler", "simple", 1]}, {"id": 8, "type": "VAEDecode", "pos": [597.3875732421875, 437.29803466796875], "size": [263.6221008300781, 46.82275390625], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 63}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [13]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [904.2274169921875, -113.31980895996094], "size": [845.74560546875, 898.2359619140625], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 13}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33"}, "widgets_values": ["ComfyUI"]}, {"id": 46, "type": "ControlNetLoader", "pos": [-265.6670227050781, -146.99842834472656], "size": [362.3617248535156, 60.98270797729492], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [117]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetLoader", "models": [{"name": "sd3.5_large_controlnet_blur.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets/resolve/main/sd3.5_large_controlnet_blur.safetensors?download=true", "directory": "controlnet"}]}, "widgets_values": ["sd3.5_large_controlnet_blur.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-273.1151428222656, -27.83469009399414], "size": [372.6641845703125, 99.02931213378906], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [14]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [65]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [8, 118]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "sd3.5_large_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/stable-diffusion-3.5-fp8/resolve/main/sd3.5_large_fp8_scaled.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["sd3.5_large_fp8_scaled.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 57, "type": "LoadImage", "pos": [-267.654296875, 175.24879455566406], "size": [359.1749267578125, 430.28228759765625], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [119]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_00204_.png", "image"]}, {"id": 33, "type": "EmptySD3LatentImage", "pos": [-264.4872131347656, 702.3841552734375], "size": [348.6626892089844, 115.82335662841797], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [66]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 62, "type": "<PERSON>downNote", "pos": [-650, 140], "size": [338.3355407714844, 162.93470764160156], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Please use the preprocessed images, or install [comfyui_controlnet_aux](https://github.com/Fannovel16/comfyui_controlnet_aux) to preprocess the images.\n\n---\n\n请使用经过预处理的图像，或者安装 [comfyui_controlnet_aux](https://github.com/Fannovel16/comfyui_controlnet_aux) 来对图像进行预处理"], "color": "#432", "bgcolor": "#653"}, {"id": 61, "type": "<PERSON>downNote", "pos": [-643.76513671875, -189.92088317871094], "size": [339.5606689453125, 262.63995361328125], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "title": "Model links", "properties": {}, "widgets_values": ["Please visit [stable-diffusion-3.5-controlnets](https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets) and log in to agree to the protocol to get permission to download.\n\n访问链接并同意对应的仓库协议才能具有下载权限\n\n---\n\nVisit  [stable-diffusion-3.5-controlnets](https://huggingface.co/stabilityai/stable-diffusion-3.5-controlnets) and download **sd3.5_large_controlnet_blur.safetensors**, then save  it in  **ComfyUI/models/controlnet** \n\n\n[sd3.5_large_fp8_scaled.safetensors](https://huggingface.co/Comfy-Org/stable-diffusion-3.5-fp8/resolve/main/sd3.5_large_fp8_scaled.safetensors?download=true)\nSave in **ComfyUI/models/checkpoints** \n"], "color": "#432", "bgcolor": "#653"}], "links": [[8, 4, 2, 8, 1, "VAE"], [13, 8, 0, 9, 0, "IMAGE"], [14, 4, 0, 3, 0, "MODEL"], [63, 3, 0, 8, 0, "LATENT"], [65, 4, 1, 6, 0, "CLIP"], [66, 33, 0, 3, 3, "LATENT"], [98, 6, 0, 50, 0, "CONDITIONING"], [115, 6, 0, 58, 0, "CONDITIONING"], [116, 50, 0, 58, 1, "CONDITIONING"], [117, 46, 0, 58, 2, "CONTROL_NET"], [118, 4, 2, 58, 4, "VAE"], [119, 57, 0, 58, 3, "IMAGE"], [120, 58, 0, 3, 1, "CONDITIONING"], [121, 58, 1, 3, 2, "CONDITIONING"]], "groups": [{"id": 1, "title": "Load models here", "bounding": [-283.1151428222656, -220.5984344482422, 392.6641845703125, 301.7930603027344], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Need preprocessed image", "bounding": [-277.654296875, 101.64891052246094, 383.9927978515625, 513.8822631835938], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Size", "bounding": [-274.4872131347656, 628.7841796875, 377.4058532714844, 201.889892578125], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.555991731349224, "offset": [980.2712530775718, 188.8216386551408]}, "frontendVersion": "1.18.9"}, "version": 0.4}