# ComfyUI 工作流导入和使用指南

## 🎯 什么是工作流？

工作流（Workflow）是 ComfyUI 中的核心概念，它定义了图像生成的完整流程，包括：
- 模型加载
- 文本编码
- 采样参数
- 后处理步骤
- 输出设置

## 📥 导入工作流的方法

### 方法 1: 拖拽导入（推荐）

1. **启动 ComfyUI**
   ```bash
   .\start_comfyui.bat
   ```

2. **访问界面**: http://127.0.0.1:8188

3. **拖拽文件**:
   - 将 `.json` 工作流文件直接拖拽到浏览器窗口
   - 或将包含工作流的 `.png` 图片拖拽到窗口

### 方法 2: 使用加载按钮

1. 点击右侧菜单的 **"Load"** 按钮
2. 选择工作流文件（`.json` 格式）
3. 确认加载

### 方法 3: 从生成的图片中提取

ComfyUI 生成的图片通常包含工作流信息：
1. 将 ComfyUI 生成的 `.png` 图片拖拽到界面
2. 系统自动提取并加载工作流
3. 确认加载对话框

## 📁 工作流文件位置

### 本地示例工作流
我已经为您创建了一个基础工作流：
- **位置**: `workflows/basic_txt2img.json`
- **功能**: 基础文本到图像生成

### 获取更多工作流

1. **官方示例**:
   - ComfyUI GitHub 仓库
   - 官方文档示例

2. **社区资源**:
   - ComfyUI 社区论坛
   - Reddit r/comfyui
   - Discord 服务器

3. **工作流分享网站**:
   - OpenArt.ai
   - CivitAI
   - ComfyWorkflows.com

## 🚀 使用基础工作流

### 步骤 1: 准备模型文件

在使用工作流前，需要下载相应的模型：

```bash
# 创建模型目录（如果不存在）
mkdir -p models/checkpoints
```

**推荐下载的基础模型**:
1. **Stable Diffusion 1.5**:
   - 下载: https://huggingface.co/runwayml/stable-diffusion-v1-5
   - 文件: `v1-5-pruned-emaonly.ckpt` 或 `v1-5-pruned-emaonly.safetensors`
   - 放置: `models/checkpoints/`

### 步骤 2: 导入工作流

1. 启动 ComfyUI
2. 拖拽 `workflows/basic_txt2img.json` 到界面
3. 确认加载

### 步骤 3: 修改参数

工作流加载后，您可以修改以下参数：

1. **文本提示词** (CLIP Text Encode):
   - 正面提示: 描述想要生成的内容
   - 负面提示: 描述不想要的内容

2. **图像尺寸** (Empty Latent Image):
   - 宽度/高度: 建议使用 512x512 或 768x768

3. **采样参数** (KSampler):
   - 种子 (seed): 控制随机性
   - 步数 (steps): 通常 20-50 步
   - CFG: 引导强度，通常 7-12
   - 采样器: euler, dpm++, 等

### 步骤 4: 生成图像

1. 点击右侧的 **"Queue Prompt"** 按钮
2. 等待生成完成
3. 查看 `output/` 目录中的结果

## 🎨 工作流类型示例

### 1. 基础文本到图像 (Text2Image)
- **用途**: 根据文本描述生成图像
- **文件**: `workflows/basic_txt2img.json`

### 2. 图像到图像 (Image2Image)
- **用途**: 基于输入图像生成新图像
- **需要**: 输入图像 + 文本描述

### 3. 图像修复 (Inpainting)
- **用途**: 修复或替换图像的特定区域
- **需要**: 原图 + 遮罩 + 文本描述

### 4. 超分辨率 (Upscaling)
- **用途**: 提高图像分辨率
- **需要**: 超分辨率模型

## 🔧 常见问题解决

### 1. 模型文件缺失
**错误**: "Model not found" 或红色节点
**解决**: 
- 检查模型文件是否在正确目录
- 确认文件名与工作流中的名称匹配
- 下载缺失的模型文件

### 2. 显存不足
**错误**: CUDA out of memory
**解决**:
```bash
# 使用低显存模式启动
python main.py --lowvram

# 或极低显存模式
python main.py --novram
```

### 3. 工作流加载失败
**错误**: 工作流无法加载
**解决**:
- 检查 JSON 文件格式是否正确
- 确认所需的自定义节点已安装
- 查看控制台错误信息

### 4. 生成速度慢
**优化建议**:
- 减少采样步数
- 降低图像分辨率
- 使用更快的采样器（如 euler_a）

## 📚 进阶技巧

### 1. 保存自定义工作流
1. 设置好参数后，点击 **"Save"** 按钮
2. 选择保存位置和文件名
3. 保存为 `.json` 格式

### 2. 批量生成
- 修改 "batch_size" 参数
- 或使用不同的种子值多次运行

### 3. 工作流优化
- 使用 VAE 缓存减少重复计算
- 合理设置采样参数平衡质量和速度
- 使用适合的模型和分辨率

### 4. 自定义节点
- 安装 ComfyUI Manager 管理插件
- 探索社区自定义节点
- 扩展工作流功能

## 🌟 推荐资源

### 学习资源
- **官方文档**: https://docs.comfy.org/
- **视频教程**: YouTube ComfyUI 教程
- **社区论坛**: Reddit r/comfyui

### 模型下载
- **Hugging Face**: https://huggingface.co/
- **CivitAI**: https://civitai.com/
- **官方模型**: Stability AI 官方发布

### 工作流分享
- **OpenArt**: https://openart.ai/
- **ComfyWorkflows**: 社区工作流分享
- **GitHub**: 开源工作流项目

现在您可以开始使用 ComfyUI 创建精美的 AI 图像了！🎨✨
