{"last_node_id": 44, "last_link_id": 123, "nodes": [{"id": 11, "type": "DualCLIPLoader", "pos": [48, 288], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "shape": 3, "links": [10], "slot_index": 0}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 17, "type": "BasicScheduler", "pos": [480, 1008], "size": [315, 106], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 55, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "shape": 3, "links": [20]}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 1]}, {"id": 16, "type": "KSamplerSelect", "pos": [480, 912], "size": [315, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "shape": 3, "links": [19]}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 26, "type": "FluxGuidance", "pos": [480, 144], "size": [317.4, 58], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 41}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "shape": 3, "links": [122], "slot_index": 0}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5], "color": "#233", "bgcolor": "#355"}, {"id": 13, "type": "SamplerCustomAdvanced", "pos": [864, 192], "size": [272.36, 124.54], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 37, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 30, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 19, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 20, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 116, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "shape": 3, "links": [24], "slot_index": 0}, {"name": "denoised_output", "type": "LATENT", "shape": 3, "links": null}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 25, "type": "RandomNoise", "pos": [480, 768], "size": [315, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "shape": 3, "links": [37]}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [958831004022715, "randomize"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 8, "type": "VAEDecode", "pos": [866, 367], "size": [210, 46], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 24}, {"name": "vae", "type": "VAE", "link": 12}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 30, "type": "ModelSamplingFlux", "pos": [480, 1152], "size": [315, 130], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 56, "slot_index": 0}, {"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 115, "slot_index": 1}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 114, "slot_index": 2}], "outputs": [{"name": "MODEL", "type": "MODEL", "shape": 3, "links": [54, 55], "slot_index": 0}], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, 1024, 1024]}, {"id": 27, "type": "EmptySD3LatentImage", "pos": [480, 624], "size": [315, 106], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 112}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 113}], "outputs": [{"name": "LATENT", "type": "LATENT", "shape": 3, "links": [116], "slot_index": 0}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 34, "type": "PrimitiveNode", "pos": [432, 480], "size": [210, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "width"}, "links": [112, 115], "slot_index": 0}], "title": "width", "properties": {"Run widget replace on values": false}, "widgets_values": [1024, "fixed"], "color": "#323", "bgcolor": "#535"}, {"id": 35, "type": "PrimitiveNode", "pos": [672, 480], "size": [210, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "height"}, "links": [113, 114], "slot_index": 0}], "title": "height", "properties": {"Run widget replace on values": false}, "widgets_values": [1024, "fixed"], "color": "#323", "bgcolor": "#535"}, {"id": 12, "type": "UNETLoader", "pos": [48, 144], "size": [315, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "shape": 3, "links": [56], "slot_index": 0}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 9, "type": "SaveImage", "pos": [1155, 196], "size": [985.3, 1060.38], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 37, "type": "Note", "pos": [480, 1344], "size": [315.0, 117.98], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["The reference sampling implementation auto adjusts the shift value based on the resolution, if you don't want this you can just bypass (CTRL-B) this ModelSamplingFlux node.\n"], "color": "#432", "bgcolor": "#653"}, {"id": 10, "type": "VAELoader", "pos": [48, 432], "size": [311.82, 60.43], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "shape": 3, "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 28, "type": "Note", "pos": [48, 576], "size": [336, 288], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["If you get an error in any of the nodes above make sure the files are in the correct directories.\n\nSee the top of the examples page for the links : https://comfyanonymous.github.io/ComfyUI_examples/flux/\n\nflux1-dev.safetensors goes in: ComfyUI/models/diffusion_models/\n\nt5xxl_fp16.safetensors and clip_l.safetensors go in: ComfyUI/models/text_encoders/\n\nae.safetensors goes in: ComfyUI/models/vae/\n\n\nTip: You can set the weight_dtype above to one of the fp8 types if you have memory issues."], "color": "#432", "bgcolor": "#653"}, {"id": 39, "type": "CLIPVisionEncode", "pos": [420, -300], "size": [290, 78], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 117}, {"name": "image", "type": "IMAGE", "link": 118}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [120], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"]}, {"id": 40, "type": "LoadImage", "pos": [60, -300], "size": [315, 314], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [118]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["sd3_controlnet_example.png", "image"]}, {"id": 42, "type": "StyleModelLoader", "pos": [400, -180], "size": [340, 60], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "STYLE_MODEL", "type": "STYLE_MODEL", "links": [119]}], "properties": {"Node name for S&R": "StyleModelLoader"}, "widgets_values": ["flux1-redux-dev.safetensors"]}, {"id": 38, "type": "CLIPVisionLoader", "pos": [60, -410], "size": [370, 60], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [117], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"]}, {"id": 41, "type": "StyleModelApply", "pos": [760, -300], "size": [320, 122], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 122}, {"name": "style_model", "type": "STYLE_MODEL", "link": 119}, {"name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "shape": 7, "link": 120}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [123], "slot_index": 0}], "properties": {"Node name for S&R": "StyleModelApply"}, "widgets_values": [1, "multiply"]}, {"id": 22, "type": "BasicGuider", "pos": [960, 66], "size": [222.35, 46], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 54, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 123, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "shape": 3, "links": [30], "slot_index": 0}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 43, "type": "Note", "pos": [1130, -440], "size": [345.9, 182.31], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["The redux model lets you prompt with images. It can be used with any Flux1 dev or schnell model workflow.\n\nYou can chain multiple \"Apply Style Model\" nodes if you want to mix multiple images together."], "color": "#432", "bgcolor": "#653"}, {"id": 6, "type": "CLIPTextEncode", "pos": [384, 240], "size": [422.85, 164.31], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 10}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [41], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["cute anime girl with massive fluffy fennec ears"], "color": "#232", "bgcolor": "#353"}, {"id": 44, "type": "<PERSON>downNote", "pos": [60, 915], "size": [225, 60], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/flux/#redux)"], "color": "#432", "bgcolor": "#653"}], "links": [[9, 8, 0, 9, 0, "IMAGE"], [10, 11, 0, 6, 0, "CLIP"], [12, 10, 0, 8, 1, "VAE"], [19, 16, 0, 13, 2, "SAMPLER"], [20, 17, 0, 13, 3, "SIGMAS"], [24, 13, 0, 8, 0, "LATENT"], [30, 22, 0, 13, 1, "GUIDER"], [37, 25, 0, 13, 0, "NOISE"], [41, 6, 0, 26, 0, "CONDITIONING"], [54, 30, 0, 22, 0, "MODEL"], [55, 30, 0, 17, 0, "MODEL"], [56, 12, 0, 30, 0, "MODEL"], [112, 34, 0, 27, 0, "INT"], [113, 35, 0, 27, 1, "INT"], [114, 35, 0, 30, 2, "INT"], [115, 34, 0, 30, 1, "INT"], [116, 27, 0, 13, 4, "LATENT"], [117, 38, 0, 39, 0, "CLIP_VISION"], [118, 40, 0, 39, 1, "IMAGE"], [119, 42, 0, 41, 1, "STYLE_MODEL"], [120, 39, 0, 41, 2, "CLIP_VISION_OUTPUT"], [122, 26, 0, 41, 0, "CONDITIONING"], [123, 41, 0, 22, 1, "CONDITIONING"]], "groups": [{"id": 1, "title": "Redux Model", "bounding": [45, -480, 1040, 507.6], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.9, "offset": [139.8, 57.78]}, "groupNodes": {"EmptyLatentImage": {"nodes": [{"type": "PrimitiveNode", "pos": [432, 480], "size": {"0": 210, "1": 82}, "flags": {}, "order": 6, "mode": 0, "outputs": [{"name": "INT", "type": "INT", "links": [], "widget": {"name": "height"}, "slot_index": 0}], "title": "height", "properties": {"Run widget replace on values": false}, "color": "#323", "bgcolor": "#535", "index": 0}, {"type": "PrimitiveNode", "pos": [672, 480], "size": {"0": 210, "1": 82}, "flags": {}, "order": 7, "mode": 0, "outputs": [{"name": "INT", "type": "INT", "links": [], "slot_index": 0, "widget": {"name": "width"}}], "title": "width", "properties": {"Run widget replace on values": false}, "color": "#323", "bgcolor": "#535", "index": 1}, {"type": "EmptySD3LatentImage", "pos": [480, 624], "size": {"0": 315, "1": 106}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": null, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": null, "widget": {"name": "height"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1], "index": 2}], "links": [[1, 0, 2, 0, 34, "INT"], [0, 0, 2, 1, 35, "INT"]], "external": [[0, 0, "INT"], [1, 0, "INT"], [2, 0, "LATENT"]], "config": {"0": {"output": {"0": {"name": "height"}}, "input": {"value": {"visible": true}}}, "1": {"output": {"0": {"name": "width"}}, "input": {"value": {"visible": true}}}, "2": {"input": {"width": {"visible": false}, "height": {"visible": false}}}}}}}, "version": 0.4, "models": [{"name": "t5xxl_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors?download=true", "directory": "text_encoders"}, {"name": "flux1-dev.safetensors", "url": "https://huggingface.co/black-forest-labs/FLUX.1-dev/resolve/main/flux1-dev.safetensors?download=true", "directory": "diffusion_models"}, {"name": "sigclip_vision_patch14_384.safetensors", "url": "https://huggingface.co/Comfy-Org/sigclip_vision_384/resolve/main/sigclip_vision_patch14_384.safetensors?download=true", "directory": "clip_vision"}, {"name": "ae.safetensors", "url": "https://huggingface.co/black-forest-labs/FLUX.1-schnell/resolve/main/ae.safetensors?download=true", "directory": "vae"}, {"name": "flux1-redux-dev.safetensors", "url": "https://huggingface.co/black-forest-labs/FLUX.1-Redux-dev/resolve/main/flux1-redux-dev.safetensors?download=true", "directory": "style_models"}, {"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors?download=true", "directory": "text_encoders"}]}