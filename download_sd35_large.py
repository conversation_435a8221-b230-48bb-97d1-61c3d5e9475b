#!/usr/bin/env python3
"""
下载 Stable Diffusion 3.5 Large 模型
"""

import os
import requests
from pathlib import Path
import sys

def download_file_with_progress(url, filename, description=""):
    """下载文件并显示进度"""
    print(f"正在下载 {description}: {filename}")
    print(f"下载地址: {url}")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        gb_downloaded = downloaded / (1024 * 1024 * 1024)
                        gb_total = total_size / (1024 * 1024 * 1024)
                        print(f"\r进度: {percent:.1f}% ({gb_downloaded:.2f}GB/{gb_total:.2f}GB)", end='')
        
        print(f"\n✅ 下载完成: {filename}")
        return True
        
    except Exception as e:
        print(f"\n❌ 下载失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Stable Diffusion 3.5 Large 下载器")
    print("=" * 60)
    
    # 确保目录存在
    checkpoints_dir = Path("models/checkpoints")
    clip_dir = Path("models/clip")
    checkpoints_dir.mkdir(parents=True, exist_ok=True)
    clip_dir.mkdir(parents=True, exist_ok=True)
    
    # SD3.5 Large 模型文件
    models = [
        {
            "name": "SD3.5 Large 主模型",
            "filename": "sd3.5_large.safetensors",
            "url": "https://huggingface.co/stabilityai/stable-diffusion-3.5-large/resolve/main/sd3.5_large.safetensors",
            "dir": checkpoints_dir,
            "size": "约 8.9GB",
            "priority": 1
        },
        {
            "name": "CLIP-G 文本编码器",
            "filename": "clip_g.safetensors", 
            "url": "https://huggingface.co/stabilityai/stable-diffusion-3.5-large/resolve/main/text_encoders/clip_g.safetensors",
            "dir": clip_dir,
            "size": "约 1.4GB",
            "priority": 2
        }
    ]
    
    print("可下载的模型文件:")
    for i, model in enumerate(models, 1):
        print(f"{i}. {model['name']} ({model['size']})")
    
    print("\n注意: SD3.5 Large 需要较大显存 (建议 12GB+)")
    print("您的 RTX 4070 (8GB) 可能需要使用量化版本")
    print()
    
    choice = input("选择下载 (1-主模型, 2-CLIP-G, all-全部, q-退出): ").strip().lower()
    
    if choice == 'q':
        print("退出下载")
        return
    elif choice == 'all':
        selected_models = models
    else:
        try:
            index = int(choice) - 1
            if 0 <= index < len(models):
                selected_models = [models[index]]
            else:
                print("❌ 无效选择")
                return
        except ValueError:
            print("❌ 无效输入")
            return
    
    # 下载选中的模型
    for model in selected_models:
        filepath = model["dir"] / model["filename"]
        
        if filepath.exists():
            print(f"⚠️  文件已存在: {model['filename']}")
            file_size = filepath.stat().st_size / (1024 * 1024 * 1024)
            print(f"当前文件大小: {file_size:.2f}GB")
            
            overwrite = input("是否重新下载? (y/N): ").strip().lower()
            if overwrite != 'y':
                continue
        
        print(f"\n开始下载: {model['name']}")
        success = download_file_with_progress(model["url"], filepath, model["name"])
        
        if not success:
            print(f"❌ {model['name']} 下载失败")
            print("可能需要 Hugging Face 账户登录")
            print("请尝试手动下载或使用浏览器下载")
        else:
            print(f"✅ {model['name']} 下载成功")
    
    print("\n🎉 下载完成!")
    print("\n使用说明:")
    print("1. 下载完成后，使用 SD3.5 工作流")
    print("2. 如果显存不足，考虑使用量化版本")
    print("3. SD3.5 支持更复杂的提示词和更好的文字渲染")

if __name__ == "__main__":
    main()
