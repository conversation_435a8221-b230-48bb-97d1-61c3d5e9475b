{"last_node_id": 43, "last_link_id": 111, "nodes": [{"id": 13, "type": "CLIPVisionEncode", "pos": [135, -63], "size": [253.6, 78], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 101}, {"name": "image", "type": "IMAGE", "link": 95}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [24], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"]}, {"id": 36, "type": "CLIPVisionEncode", "pos": [137, 24], "size": [253.6, 78], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 102}, {"name": "image", "type": "IMAGE", "link": 98}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [100], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"]}, {"id": 8, "type": "VAEDecode", "pos": [1277, -210], "size": [210, 46], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 106}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 34, "type": "LoadImage", "pos": [-352, -29], "size": [435.35, 377.59], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [95], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["mountains.png", "image"]}, {"id": 38, "type": "LoadImage", "pos": [-341, 412], "size": [435.35, 377.59], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [98], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["sunset.png", "image"]}, {"id": 40, "type": "CheckpointLoaderSimple", "pos": [-761, -275], "size": [315, 98], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "shape": 3, "links": [103], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "shape": 3, "links": [104], "slot_index": 1}, {"name": "VAE", "type": "VAE", "shape": 3, "links": [106], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["sd_xl_base_1.0.safetensors"]}, {"id": 39, "type": "CLIPVisionLoader", "pos": [-760, -120], "size": [315, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "shape": 3, "links": [101, 102], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["clip_vision_g.safetensors"]}, {"id": 9, "type": "SaveImage", "pos": [1542, -209], "size": [635.19, 692.82], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [915, -218], "size": [315, 262], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 103}, {"name": "positive", "type": "CONDITIONING", "link": 97}, {"name": "negative", "type": "CONDITIONING", "link": 111}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [133632471276133, "randomize", 26, 8, "dpmpp_3m_sde_gpu", "exponential", 1]}, {"id": 37, "type": "unCLIPConditioning", "pos": [626, -205], "size": [262, 102], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 96}, {"name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 100}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [97], "slot_index": 0}], "properties": {"Node name for S&R": "unCLIPConditioning"}, "widgets_values": [0.75, 0]}, {"id": 5, "type": "EmptyLatentImage", "pos": [534, 214], "size": [315, 106], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 19, "type": "unCLIPConditioning", "pos": [347, -207], "size": [262, 102], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 110}, {"name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 24}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [96], "slot_index": 0}], "properties": {"Node name for S&R": "unCLIPConditioning"}, "widgets_values": [0.75, 0]}, {"id": 42, "type": "ConditioningZeroOut", "pos": [60, -211], "size": [211.6, 26], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 109, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "shape": 3, "links": [110], "slot_index": 0}], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 6, "type": "CLIPTextEncode", "pos": [-182, -184], "size": [422.85, 164.31], "flags": {"collapsed": true}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 104}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [109, 111], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 43, "type": "<PERSON>downNote", "pos": [-750, -15], "size": [225, 60], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/sdxl/#revision)"], "color": "#432", "bgcolor": "#653"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [7, 3, 0, 8, 0, "LATENT"], [9, 8, 0, 9, 0, "IMAGE"], [24, 13, 0, 19, 1, "CLIP_VISION_OUTPUT"], [95, 34, 0, 13, 1, "IMAGE"], [96, 19, 0, 37, 0, "CONDITIONING"], [97, 37, 0, 3, 1, "CONDITIONING"], [98, 38, 0, 36, 1, "IMAGE"], [100, 36, 0, 37, 1, "CLIP_VISION_OUTPUT"], [101, 39, 0, 13, 0, "CLIP_VISION"], [102, 39, 0, 36, 0, "CLIP_VISION"], [103, 40, 0, 3, 0, "MODEL"], [104, 40, 1, 6, 0, "CLIP"], [106, 40, 2, 8, 1, "VAE"], [109, 6, 0, 42, 0, "CONDITIONING"], [110, 42, 0, 19, 0, "CONDITIONING"], [111, 6, 0, 3, 2, "CONDITIONING"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.49, "offset": [1046.06, 311.39]}}, "version": 0.4, "models": [{"name": "sd_xl_base_1.0.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0.safetensors?download=true", "directory": "checkpoints"}, {"name": "clip_vision_g.safetensors", "url": "https://huggingface.co/comfyanonymous/clip_vision_g/resolve/main/clip_vision_g.safetensors?download=true", "directory": "clip_vision"}]}