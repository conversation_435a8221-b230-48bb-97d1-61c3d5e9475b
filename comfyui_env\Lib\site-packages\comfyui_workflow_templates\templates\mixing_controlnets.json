{"last_node_id": 37, "last_link_id": 57, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [170, 90], "size": [315, 474], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 41}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 53}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 54}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [1021287240057524, "randomize", 20, 6, "dpmpp_sde", "karras", 1]}, {"id": 5, "type": "EmptyLatentImage", "pos": [-720, 360], "size": [315, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 7, "type": "CLIPTextEncode", "pos": [-352, 296], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 42}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [46]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(hands), text, error, cropped, (worst quality:1.2), (low quality:1.2), normal quality, (jpeg artifacts:1.3), signature, watermark, username, blurry, artist name, monochrome, sketch, censorship, censor, (copyright:1.2), extra legs, (forehead mark) (depth of field) (emotionless) (penis)"]}, {"id": 8, "type": "VAEDecode", "pos": [510, 90], "size": [210, 46], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 7}, {"label": "vae", "name": "vae", "type": "VAE", "link": 28}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [512, 184], "size": [404.7805480957031, 370.7200927734375], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}, {"id": 15, "type": "VAELoader", "pos": [-720, 240], "size": [315, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [28]}], "properties": {"Node name for S&R": "VAELoader", "models": [{"name": "vae-ft-mse-840000-ema-pruned.safetensors", "url": "https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.safetensors?download=true", "directory": "vae"}]}, "widgets_values": ["vae-ft-mse-840000-ema-pruned.safetensors"]}, {"id": 20, "type": "LoadImage", "pos": [163.35365295410156, -366.2963562011719], "size": [328, 360], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [55]}, {"label": "MASK", "name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["image (72).png", "image"]}, {"id": 21, "type": "LoadImage", "pos": [-640, -368], "size": [344, 368], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [57]}, {"label": "MASK", "name": "MASK", "type": "MASK", "slot_index": 1, "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["image (70).png", "image"]}, {"id": 24, "type": "CLIPTextEncode", "pos": [-352, 88], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 43}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [47]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Masterpiece, Top Quality, Best Quality, Official Art, Aesthetics: 1.2, Raytrace 1.4), <PERSON>, Shut up, Stand Up, full_body, 1girl, <PERSON>, Animal, Blush sticker, Cat, animal_focus, White Cat, Hover EV,"]}, {"id": 26, "type": "ControlNetLoader", "pos": [161.00196838378906, -474.3419494628906], "size": [328, 64], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "CONTROL_NET", "name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [51]}], "properties": {"Node name for S&R": "ControlNetLoader", "models": [{"name": "control_v11p_sd15_scribble_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_scribble_fp16.safetensors?download=true", "directory": "controlnet"}]}, "widgets_values": ["control_v11p_sd15_scribble_fp16.safetensors"]}, {"id": 31, "type": "CheckpointLoaderSimple", "pos": [-720, 90], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [41]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [42, 43]}, {"label": "VAE", "name": "VAE", "type": "VAE", "links": null}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "models": [{"directory": "checkpoints", "name": "awpainting_v14.safetensors", "url": "https://civitai.com/api/download/models/624939?type=Model&format=SafeTensor&size=full&fp=fp16"}]}, "widgets_values": ["awpainting_v14.safetensors"]}, {"id": 34, "type": "ControlNetApplyAdvanced", "pos": [-256, -472], "size": [315, 186], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 47}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 46}, {"label": "control_net", "name": "control_net", "type": "CONTROL_NET", "link": 56}, {"label": "image", "name": "image", "type": "IMAGE", "link": 57}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [49]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [50]}], "properties": {"Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [1, 0, 1]}, {"id": 35, "type": "ControlNetLoader", "pos": [-640, -472], "size": [340, 60], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "CONTROL_NET", "name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [56]}], "properties": {"Node name for S&R": "ControlNetLoader", "models": [{"name": "control_v11p_sd15_openpose_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_openpose_fp16.safetensors", "directory": "controlnet"}]}, "widgets_values": ["control_v11p_sd15_openpose_fp16.safetensors"]}, {"id": 36, "type": "ControlNetApplyAdvanced", "pos": [509.81451416015625, -337.0327453613281], "size": [315, 186], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 49}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 50}, {"label": "control_net", "name": "control_net", "type": "CONTROL_NET", "link": 51}, {"label": "image", "name": "image", "type": "IMAGE", "link": 55}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [53]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [54]}], "properties": {"Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [1, 0, 1]}, {"id": 37, "type": "<PERSON>downNote", "pos": [-720, 528], "size": [336, 152], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n> \n> \n> [ControlNets - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/controlnet/#mixing-controlnets) — Overview\n\n> [Mixing ControlNets - docs.comfy.org](https://docs.comfy.org/tutorials/controlnet/mixing-controlnets) — Explanation of concepts and step-by-step tutorial "], "color": "#432", "bgcolor": "#653"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [7, 3, 0, 8, 0, "LATENT"], [9, 8, 0, 9, 0, "IMAGE"], [28, 15, 0, 8, 1, "VAE"], [41, 31, 0, 3, 0, "MODEL"], [42, 31, 1, 7, 0, "CLIP"], [43, 31, 1, 24, 0, "CLIP"], [46, 7, 0, 34, 1, "CONDITIONING"], [47, 24, 0, 34, 0, "CONDITIONING"], [49, 34, 0, 36, 0, "CONDITIONING"], [50, 34, 1, 36, 1, "CONDITIONING"], [51, 26, 0, 36, 2, "CONTROL_NET"], [53, 36, 0, 3, 1, "CONDITIONING"], [54, 36, 1, 3, 2, "CONDITIONING"], [55, 20, 0, 36, 3, "IMAGE"], [56, 35, 0, 34, 2, "CONTROL_NET"], [57, 21, 0, 34, 3, "IMAGE"]], "groups": [{"id": 1, "title": "Apply Pose ControlNet", "bounding": [-664, -552, 736.0491943359375, 576.887939453125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Apply Scribble ControlNet", "bounding": [136, -552, 728.1638793945312, 582.2390747070312], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"node_versions": {"comfy-core": "0.3.14"}}, "version": 0.4}