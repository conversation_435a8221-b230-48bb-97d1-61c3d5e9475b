{"id": "7cb6261d-3b03-4171-bbd1-a4b256b42404", "revision": 0, "last_node_id": 16, "last_link_id": 17, "nodes": [{"id": 7, "type": "CLIPTextEncode", "pos": [413, 389], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 15}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["watermark, text\n"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [415, 186], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 14}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["photograph of victorian woman with wings, sky clouds, meadow grass\n"]}, {"id": 8, "type": "VAEDecode", "pos": [1209, 188], "size": [210, 46], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 17}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1451, 189], "size": [210, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [863, 186], "size": [315, 262], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 13}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 11}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [280823642470253, "randomize", 20, 8, "dpmpp_2m", "normal", 0.8700000000000001]}, {"id": 14, "type": "CheckpointLoaderSimple", "pos": [53.31755065917969, 191.36024475097656], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [13]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [14, 15]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [16, 17]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "v1-5-pruned-emaonly-fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/stable-diffusion-v1-5-archive/resolve/main/v1-5-pruned-emaonly-fp16.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["v1-5-pruned-emaonly-fp16.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 10, "type": "LoadImage", "pos": [166.07955932617188, 678.5671997070312], "size": [315, 314.0000305175781], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [10]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 12, "type": "VAEEncode", "pos": [585.1956787109375, 685.038818359375], "size": [210, 46], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 10}, {"name": "vae", "type": "VAE", "link": 16}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [11]}], "properties": {"Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 16, "type": "<PERSON>downNote", "pos": [-290.15606689453125, 191.5925750732422], "size": [314.95745849609375, 133.0336456298828], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tutorials](https://docs.comfy.org/tutorials/basic/text-to-image)|[教程](https://docs.comfy.org/zh-CN/tutorials/basic/text-to-image)\n\nDownload  [v1-5-pruned-emaonly-fp16.safetensors](https://huggingface.co/Comfy-Org/stable-diffusion-v1-5-archive/resolve/main/v1-5-pruned-emaonly-fp16.safetensors?download=true) and save it in  **ComfyUI/models/checkpoints** \n\n---\n\n下载 [v1-5-pruned-emaonly-fp16.safetensors](https://huggingface.co/Comfy-Org/stable-diffusion-v1-5-archive/resolve/main/v1-5-pruned-emaonly-fp16.safetensors?download=true) 并保存到 **ComfyUI/models/checkpoints** 文件夹下"], "color": "#432", "bgcolor": "#653"}], "links": [[4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [9, 8, 0, 9, 0, "IMAGE"], [10, 10, 0, 12, 0, "IMAGE"], [11, 12, 0, 3, 3, "LATENT"], [13, 14, 0, 3, 0, "MODEL"], [14, 14, 1, 6, 0, "CLIP"], [15, 14, 1, 7, 0, "CLIP"], [16, 14, 2, 12, 1, "VAE"], [17, 14, 2, 8, 1, "VAE"]], "groups": [{"id": 1, "title": "Loading images", "bounding": [124.6132583618164, 595.794921875, 700.1656494140625, 414.9020080566406], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"frontendVersion": "1.19.4"}, "version": 0.4}