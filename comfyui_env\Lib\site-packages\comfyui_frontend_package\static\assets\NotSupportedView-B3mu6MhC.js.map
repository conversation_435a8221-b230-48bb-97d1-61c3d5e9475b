{"version": 3, "file": "NotSupportedView-B3mu6MhC.js", "sources": ["../../../../../../../assets/images/sad_girl.png", "../../src/views/NotSupportedView.vue"], "sourcesContent": ["export default \"__VITE_PUBLIC_ASSET__b82952e7__\"", "<template>\n  <BaseViewTemplate>\n    <div class=\"sad-container\">\n      <!-- Right side image -->\n      <img\n        class=\"sad-girl\"\n        src=\"/assets/images/sad_girl.png\"\n        alt=\"Sad girl illustration\"\n      />\n\n      <div class=\"no-drag sad-text flex items-center\">\n        <div class=\"flex flex-col gap-8 p-8 min-w-110\">\n          <!-- Header -->\n          <h1 class=\"text-4xl font-bold text-red-500\">\n            {{ $t('notSupported.title') }}\n          </h1>\n\n          <!-- Message -->\n          <div class=\"space-y-4\">\n            <p class=\"text-xl\">\n              {{ $t('notSupported.message') }}\n            </p>\n            <ul class=\"list-disc list-inside space-y-1 text-neutral-800\">\n              <li>{{ $t('notSupported.supportedDevices.macos') }}</li>\n              <li>{{ $t('notSupported.supportedDevices.windows') }}</li>\n            </ul>\n          </div>\n\n          <!-- Actions -->\n          <div class=\"flex gap-4\">\n            <Button\n              :label=\"$t('notSupported.learnMore')\"\n              icon=\"pi pi-github\"\n              severity=\"secondary\"\n              @click=\"openDocs\"\n            />\n            <Button\n              :label=\"$t('notSupported.reportIssue')\"\n              icon=\"pi pi-flag\"\n              severity=\"secondary\"\n              @click=\"reportIssue\"\n            />\n            <Button\n              v-tooltip=\"$t('notSupported.continueTooltip')\"\n              :label=\"$t('notSupported.continue')\"\n              icon=\"pi pi-arrow-right\"\n              icon-pos=\"right\"\n              severity=\"danger\"\n              @click=\"continueToInstall\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  </BaseViewTemplate>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport { useRouter } from 'vue-router'\n\nimport BaseViewTemplate from '@/views/templates/BaseViewTemplate.vue'\n\nconst openDocs = () => {\n  window.open(\n    'https://github.com/Comfy-Org/desktop#currently-supported-platforms',\n    '_blank'\n  )\n}\n\nconst reportIssue = () => {\n  window.open('https://forum.comfy.org/c/v1-feedback/', '_blank')\n}\n\nconst router = useRouter()\nconst continueToInstall = async () => {\n  await router.push('/install')\n}\n</script>\n\n<style scoped>\n.sad-container {\n  @apply grid items-center justify-evenly;\n  grid-template-columns: 25rem 1fr;\n\n  & > * {\n    grid-row: 1;\n  }\n}\n\n.sad-text {\n  grid-column: 1/3;\n}\n\n.sad-girl {\n  grid-column: 2/3;\n  width: min(75vw, 100vh);\n}\n</style>\n"], "names": [], "mappings": ";;;;;;;AAAA,MAAe,aAAA,KAAA,IAAA,IAAA,uBAAA,YAAA,GAAA,EAAA;;;;;;;;;;;;AC+Df,UAAM,WAAW,6BAAM;AACd,aAAA;AAAA,QACL;AAAA,QACA;AAAA,MAAA;AAAA,IACF,GAJe;AAOjB,UAAM,cAAc,6BAAM;AACjB,aAAA,KAAK,0CAA0C,QAAQ;AAAA,IAAA,GAD5C;AAIpB,UAAM,SAAS;AACf,UAAM,oBAAoB,mCAAY;AAC9B,YAAA,OAAO,KAAK,UAAU;AAAA,IAAA,GADJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}