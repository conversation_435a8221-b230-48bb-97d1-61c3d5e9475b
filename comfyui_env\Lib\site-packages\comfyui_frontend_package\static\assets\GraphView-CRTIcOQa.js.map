{"version": 3, "file": "GraphView-CRTIcOQa.js", "sources": ["../../src/components/MenuHamburger.vue", "../../src/components/dialog/UnloadWindowConfirmDialog.vue", "../../src/components/LiteGraphCanvasSplitterOverlay.vue", "../../src/components/common/ExtensionSlot.vue", "../../src/components/bottomPanel/BottomPanel.vue", "../../src/components/breadcrumb/SubgraphBreadcrumb.vue", "../../src/composables/element/useAbsolutePosition.ts", "../../src/composables/element/useDomClipping.ts", "../../src/components/graph/widgets/DomWidget.vue", "../../src/components/graph/DomWidgets.vue", "../../src/components/graph/GraphCanvasMenu.vue", "../../src/components/graph/NodeTooltip.vue", "../../src/components/graph/SelectionOverlay.vue", "../../src/components/graph/selectionToolbox/BypassButton.vue", "../../src/components/graph/selectionToolbox/ColorPickerButton.vue", "../../src/components/graph/selectionToolbox/DeleteButton.vue", "../../src/components/graph/selectionToolbox/ExecuteButton.vue", "../../src/components/graph/selectionToolbox/ExtensionCommandButton.vue", "../../src/components/graph/selectionToolbox/MaskEditorButton.vue", "../../src/components/graph/selectionToolbox/PinButton.vue", "../../src/composables/useRefreshableSelection.ts", "../../src/components/graph/selectionToolbox/RefreshButton.vue", "../../src/components/graph/SelectionToolbox.vue", "../../src/components/graph/TitleEditor.vue", "../../src/stores/workspace/searchBoxStore.ts", "../../src/types/searchBoxTypes.ts", "../../src/components/primevueOverride/AutoCompletePlus.vue", "../../src/components/searchbox/NodeSearchItem.vue", "../../src/components/searchbox/NodeSearchBox.vue", "../../src/components/searchbox/NodeSearchBoxPopover.vue", "../../src/components/sidebar/SidebarIcon.vue", "../../src/components/sidebar/SidebarLogoutIcon.vue", "../../src/components/sidebar/SidebarSettingsToggleIcon.vue", "../../src/components/sidebar/SidebarThemeToggleIcon.vue", "../../src/components/sidebar/SideToolbar.vue", "../../src/components/topbar/WorkflowTab.vue", "../../src/components/topbar/WorkflowTabs.vue", "../../src/composables/node/useNodePricing.ts", "../../src/composables/node/useNodeBadge.ts", "../../src/composables/useCanvasDrop.ts", "../../src/composables/useContextMenuTranslation.ts", "../../src/composables/useCopy.ts", "../../src/composables/useGlobalLitegraph.ts", "../../src/composables/useLitegraphSettings.ts", "../../src/composables/usePaste.ts", "../../src/composables/useWorkflowAutoSave.ts", "../../src/composables/useWorkflowPersistence.ts", "../../src/constants/coreSettings.ts", "../../src/components/graph/GraphCanvas.vue", "../../src/components/toast/GlobalToast.vue", "../../src/components/toast/RerouteMigrationToast.vue", "../../src/components/actionbar/BatchCountEdit.vue", "../../src/components/actionbar/ComfyQueueButton.vue", "../../src/components/actionbar/ComfyActionbar.vue", "../../src/components/topbar/BottomPanelToggleButton.vue", "../../src/components/topbar/CommandMenubar.vue", "../../src/components/topbar/CurrentUserPopover.vue", "../../src/components/topbar/CurrentUserButton.vue", "../../src/components/topbar/TopMenubar.vue", "../../src/composables/useBrowserTabTitle.ts", "../../src/composables/useCoreCommands.ts", "../../src/composables/useProgressFavicon.ts", "../../src/types/serverArgs.ts", "../../src/constants/serverConfig.ts", "../../src/services/autoQueueService.ts", "../../src/views/GraphView.vue"], "sourcesContent": ["<template>\n  <div\n    v-show=\"workspaceState.focusMode\"\n    class=\"comfy-menu-hamburger no-drag\"\n    :style=\"positionCSS\"\n  >\n    <Button\n      v-tooltip=\"{ value: $t('menu.showMenu'), showDelay: 300 }\"\n      icon=\"pi pi-bars\"\n      severity=\"secondary\"\n      text\n      size=\"large\"\n      :aria-label=\"$t('menu.showMenu')\"\n      aria-live=\"assertive\"\n      @click=\"exitFocusMode\"\n      @contextmenu=\"showNativeSystemMenu\"\n    />\n    <div v-show=\"menuSetting !== 'Bottom'\" class=\"window-actions-spacer\" />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport { CSSProperties, computed, watchEffect } from 'vue'\n\nimport { app } from '@/scripts/app'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\nimport { showNativeSystemMenu } from '@/utils/envUtil'\n\nconst workspaceState = useWorkspaceStore()\nconst settingStore = useSettingStore()\nconst exitFocusMode = () => {\n  workspaceState.focusMode = false\n}\n\nwatchEffect(() => {\n  if (settingStore.get('Comfy.UseNewMenu') !== 'Disabled') {\n    return\n  }\n  if (workspaceState.focusMode) {\n    app.ui.menuContainer.style.display = 'none'\n  } else {\n    app.ui.menuContainer.style.display = 'block'\n  }\n})\n\nconst menuSetting = computed(() => settingStore.get('Comfy.UseNewMenu'))\nconst positionCSS = computed<CSSProperties>(() =>\n  // 'Bottom' menuSetting shows the hamburger button in the bottom right corner\n  // 'Disabled', 'Top' menuSetting shows the hamburger button in the top right corner\n  menuSetting.value === 'Bottom'\n    ? { bottom: '0px', right: '0px' }\n    : { top: '0px', right: '0px' }\n)\n</script>\n\n<style scoped>\n.comfy-menu-hamburger {\n  @apply fixed z-[9999] flex flex-row;\n}\n</style>\n", "<template>\n  <div>\n    <!--\n    UnloadWindowConfirmDialog: This component does not render\n    anything visible. It is used to confirm the user wants to\n    close the window, and if they do, it will call the\n    beforeunload event.\n    -->\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { onBeforeUnmount, onMounted } from 'vue'\n\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useWorkflowStore } from '@/stores/workflowStore'\n\nconst settingStore = useSettingStore()\nconst workflowStore = useWorkflowStore()\n\nconst handleBeforeUnload = (event: BeforeUnloadEvent) => {\n  if (\n    settingStore.get('Comfy.Window.UnloadConfirmation') &&\n    workflowStore.modifiedWorkflows.length > 0\n  ) {\n    event.preventDefault()\n    return true\n  }\n  return undefined\n}\n\nonMounted(() => {\n  window.addEventListener('beforeunload', handleBeforeUnload)\n})\n\nonBeforeUnmount(() => {\n  window.removeEventListener('beforeunload', handleBeforeUnload)\n})\n</script>\n", "<template>\n  <Splitter\n    :key=\"sidebarStateKey\"\n    class=\"splitter-overlay-root splitter-overlay\"\n    :pt:gutter=\"sidebarPanelVisible ? '' : 'hidden'\"\n    :state-key=\"sidebarStateKey\"\n    state-storage=\"local\"\n  >\n    <SplitterPanel\n      v-show=\"sidebarPanelVisible\"\n      v-if=\"sidebarLocation === 'left'\"\n      class=\"side-bar-panel\"\n      :min-size=\"10\"\n      :size=\"20\"\n    >\n      <slot name=\"side-bar-panel\" />\n    </SplitterPanel>\n\n    <SplitterPanel :size=\"100\">\n      <Splitter\n        class=\"splitter-overlay max-w-full\"\n        layout=\"vertical\"\n        :pt:gutter=\"bottomPanelVisible ? '' : 'hidden'\"\n        state-key=\"bottom-panel-splitter\"\n        state-storage=\"local\"\n      >\n        <SplitterPanel class=\"graph-canvas-panel relative\">\n          <slot name=\"graph-canvas-panel\" />\n        </SplitterPanel>\n        <SplitterPanel v-show=\"bottomPanelVisible\" class=\"bottom-panel\">\n          <slot name=\"bottom-panel\" />\n        </SplitterPanel>\n      </Splitter>\n    </SplitterPanel>\n\n    <SplitterPanel\n      v-show=\"sidebarPanelVisible\"\n      v-if=\"sidebarLocation === 'right'\"\n      class=\"side-bar-panel\"\n      :min-size=\"10\"\n      :size=\"20\"\n    >\n      <slot name=\"side-bar-panel\" />\n    </SplitterPanel>\n  </Splitter>\n</template>\n\n<script setup lang=\"ts\">\nimport Splitter from 'primevue/splitter'\nimport SplitterPanel from 'primevue/splitterpanel'\nimport { computed } from 'vue'\n\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useBottomPanelStore } from '@/stores/workspace/bottomPanelStore'\nimport { useSidebarTabStore } from '@/stores/workspace/sidebarTabStore'\n\nconst settingStore = useSettingStore()\nconst sidebarLocation = computed<'left' | 'right'>(() =>\n  settingStore.get('Comfy.Sidebar.Location')\n)\n\nconst unifiedWidth = computed(() =>\n  settingStore.get('Comfy.Sidebar.UnifiedWidth')\n)\n\nconst sidebarPanelVisible = computed(\n  () => useSidebarTabStore().activeSidebarTab !== null\n)\nconst bottomPanelVisible = computed(\n  () => useBottomPanelStore().bottomPanelVisible\n)\nconst activeSidebarTabId = computed(\n  () => useSidebarTabStore().activeSidebarTabId\n)\n\nconst sidebarStateKey = computed(() => {\n  return unifiedWidth.value ? 'unified-sidebar' : activeSidebarTabId.value ?? ''\n})\n</script>\n\n<style scoped>\n:deep(.p-splitter-gutter) {\n  pointer-events: auto;\n}\n\n:deep(.p-splitter-gutter:hover),\n:deep(.p-splitter-gutter[data-p-gutter-resizing='true']) {\n  transition: background-color 0.2s ease 300ms;\n  background-color: var(--p-primary-color);\n}\n\n.side-bar-panel {\n  background-color: var(--bg-color);\n  pointer-events: auto;\n}\n\n.bottom-panel {\n  background-color: var(--bg-color);\n  pointer-events: auto;\n}\n\n.splitter-overlay {\n  @apply bg-transparent pointer-events-none border-none;\n}\n\n.splitter-overlay-root {\n  @apply w-full h-full absolute top-0 left-0;\n\n  /* Set it the same as the ComfyUI menu */\n  /* Note: Lite-graph DOM widgets have the same z-index as the node id, so\n  999 should be sufficient to make sure splitter overlays on node's DOM\n  widgets */\n  z-index: 999;\n}\n</style>\n", "<template>\n  <component :is=\"extension.component\" v-if=\"extension.type === 'vue'\" />\n  <div\n    v-else\n    :ref=\"\n      (el) => {\n        if (el)\n          mountCustomExtension(\n            props.extension as CustomExtension,\n            el as HTMLElement\n          )\n      }\n    \"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport { onBeforeUnmount } from 'vue'\n\nimport { CustomExtension, VueExtension } from '@/types/extensionTypes'\n\nconst props = defineProps<{\n  extension: VueExtension | CustomExtension\n}>()\n\nconst mountCustomExtension = (extension: CustomExtension, el: HTMLElement) => {\n  extension.render(el)\n}\n\nonBeforeUnmount(() => {\n  if (props.extension.type === 'custom' && props.extension.destroy) {\n    props.extension.destroy()\n  }\n})\n</script>\n", "<template>\n  <div class=\"flex flex-col h-full\">\n    <Tabs v-model:value=\"bottomPanelStore.activeBottomPanelTabId\">\n      <TabList pt:tab-list=\"border-none\">\n        <div class=\"w-full flex justify-between\">\n          <div class=\"tabs-container\">\n            <Tab\n              v-for=\"tab in bottomPanelStore.bottomPanelTabs\"\n              :key=\"tab.id\"\n              :value=\"tab.id\"\n              class=\"p-3 border-none\"\n            >\n              <span class=\"font-bold\">\n                {{ tab.title.toUpperCase() }}\n              </span>\n            </Tab>\n          </div>\n          <Button\n            class=\"justify-self-end\"\n            icon=\"pi pi-times\"\n            severity=\"secondary\"\n            size=\"small\"\n            text\n            @click=\"bottomPanelStore.bottomPanelVisible = false\"\n          />\n        </div>\n      </TabList>\n    </Tabs>\n    <!-- h-0 to force the div to flex-grow -->\n    <div class=\"flex-grow h-0\">\n      <ExtensionSlot\n        v-if=\"\n          bottomPanelStore.bottomPanelVisible &&\n          bottomPanelStore.activeBottomPanelTab\n        \"\n        :extension=\"bottomPanelStore.activeBottomPanelTab\"\n      />\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport Tab from 'primevue/tab'\nimport TabList from 'primevue/tablist'\nimport Tabs from 'primevue/tabs'\n\nimport ExtensionSlot from '@/components/common/ExtensionSlot.vue'\nimport { useBottomPanelStore } from '@/stores/workspace/bottomPanelStore'\n\nconst bottomPanelStore = useBottomPanelStore()\n</script>\n", "<template>\n  <div\n    v-if=\"workflowStore.isSubgraphActive\"\n    class=\"fixed top-[var(--comfy-topbar-height)] left-[var(--sidebar-width)] p-2 subgraph-breadcrumb\"\n  >\n    <Breadcrumb\n      class=\"bg-transparent\"\n      :home=\"home\"\n      :model=\"items\"\n      aria-label=\"Graph navigation\"\n      @item-click=\"handleItemClick\"\n    />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { useEventListener, whenever } from '@vueuse/core'\nimport Breadcrumb from 'primevue/breadcrumb'\nimport type { MenuItem, MenuItemCommandEvent } from 'primevue/menuitem'\nimport { computed } from 'vue'\n\nimport { useWorkflowService } from '@/services/workflowService'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { useWorkflowStore } from '@/stores/workflowStore'\n\nconst workflowService = useWorkflowService()\nconst workflowStore = useWorkflowStore()\n\nconst workflowName = computed(() => workflowStore.activeWorkflow?.filename)\n\nconst items = computed(() => {\n  if (!workflowStore.subgraphNamePath.length) return []\n\n  return workflowStore.subgraphNamePath.map<MenuItem>((name) => ({\n    label: name,\n    command: async () => {\n      const workflow = workflowStore.getWorkflowByPath(name)\n      if (workflow) await workflowService.openWorkflow(workflow)\n    }\n  }))\n})\n\nconst home = computed(() => ({\n  label: workflowName.value,\n  icon: 'pi pi-home',\n  command: async () => {\n    const canvas = useCanvasStore().getCanvas()\n    if (!canvas.graph) throw new TypeError('Canvas has no graph')\n\n    canvas.setGraph(canvas.graph.rootGraph)\n  }\n}))\n\nconst handleItemClick = (event: MenuItemCommandEvent) => {\n  event.item.command?.(event)\n}\n\nwhenever(\n  () => useCanvasStore().canvas,\n  (canvas) => {\n    useEventListener(canvas.canvas, 'litegraph:set-graph', () => {\n      useWorkflowStore().updateActiveGraph()\n    })\n  }\n)\n</script>\n\n<style>\n.subgraph-breadcrumb {\n  .p-breadcrumb-item-link,\n  .p-breadcrumb-item-icon {\n    color: #d26565;\n    user-select: none;\n  }\n}\n</style>\n", "import type { Size, Vector2 } from '@comfyorg/litegraph'\nimport { CSSProperties, ref } from 'vue'\n\nimport { useCanvasPositionConversion } from '@/composables/element/useCanvasPositionConversion'\nimport { useCanvasStore } from '@/stores/graphStore'\n\nexport interface PositionConfig {\n  /* The position of the element on litegraph canvas */\n  pos: Vector2\n  /* The size of the element on litegraph canvas */\n  size: Size\n  /* The scale factor of the canvas */\n  scale?: number\n}\n\nexport function useAbsolutePosition(options: { useTransform?: boolean } = {}) {\n  const { useTransform = false } = options\n\n  const canvasStore = useCanvasStore()\n  const lgCanvas = canvasStore.getCanvas()\n  const { canvasPosToClientPos } = useCanvasPositionConversion(\n    lgCanvas.canvas,\n    lgCanvas\n  )\n\n  /**\n   * @note Do NOT convert style to a computed value, as it will cause lag when\n   * updating the style on different animation frames. Vue's computed value is\n   * evaluated asynchronously.\n   */\n  const style = ref<CSSProperties>({})\n\n  /**\n   * Compute the style of the element based on the position and size.\n   *\n   * @param position\n   */\n  const computeStyle = (position: PositionConfig): CSSProperties => {\n    const { pos, size, scale = lgCanvas.ds.scale } = position\n    const [left, top] = canvasPosToClientPos(pos)\n    const [width, height] = size\n\n    return useTransform\n      ? {\n          position: 'fixed',\n          transformOrigin: '0 0',\n          transform: `scale(${scale})`,\n          left: `${left}px`,\n          top: `${top}px`,\n          width: `${width}px`,\n          height: `${height}px`\n        }\n      : {\n          position: 'fixed',\n          left: `${left}px`,\n          top: `${top}px`,\n          width: `${width * scale}px`,\n          height: `${height * scale}px`\n        }\n  }\n\n  /**\n   * Update the position of the element on the litegraph canvas.\n   *\n   * @param config\n   */\n  const updatePosition = (config: PositionConfig) => {\n    style.value = computeStyle(config)\n  }\n\n  return {\n    style,\n    updatePosition\n  }\n}\n", "import { CSSProperties, ref } from 'vue'\n\ninterface Rect {\n  x: number\n  y: number\n  width: number\n  height: number\n}\n\n/**\n * Finds the intersection between two rectangles\n */\nfunction intersect(a: Rect, b: Rect): [number, number, number, number] | null {\n  const x1 = Math.max(a.x, b.x)\n  const y1 = Math.max(a.y, b.y)\n  const x2 = Math.min(a.x + a.width, b.x + b.width)\n  const y2 = Math.min(a.y + a.height, b.y + b.height)\n\n  if (x1 >= x2 || y1 >= y2) {\n    return null\n  }\n\n  return [x1, y1, x2 - x1, y2 - y1]\n}\n\nexport interface ClippingOptions {\n  margin?: number\n}\n\nexport const useDomClipping = (options: ClippingOptions = {}) => {\n  const style = ref<CSSProperties>({})\n  const { margin = 4 } = options\n\n  /**\n   * Calculates a clip path for an element based on its intersection with a selected area\n   */\n  const calculateClipPath = (\n    elementRect: DOMRect,\n    canvasRect: DOMRect,\n    isSelected: boolean,\n    selectedArea?: {\n      x: number\n      y: number\n      width: number\n      height: number\n      scale: number\n      offset: [number, number]\n    }\n  ): string => {\n    if (!isSelected && selectedArea) {\n      const { scale, offset } = selectedArea\n\n      // Get intersection in browser space\n      const intersection = intersect(\n        {\n          x: elementRect.left - canvasRect.left,\n          y: elementRect.top - canvasRect.top,\n          width: elementRect.width,\n          height: elementRect.height\n        },\n        {\n          x: (selectedArea.x + offset[0] - margin) * scale,\n          y: (selectedArea.y + offset[1] - margin) * scale,\n          width: (selectedArea.width + 2 * margin) * scale,\n          height: (selectedArea.height + 2 * margin) * scale\n        }\n      )\n\n      if (!intersection) {\n        return ''\n      }\n\n      // Convert intersection to canvas scale (element has scale transform)\n      const clipX =\n        (intersection[0] - elementRect.left + canvasRect.left) / scale + 'px'\n      const clipY =\n        (intersection[1] - elementRect.top + canvasRect.top) / scale + 'px'\n      const clipWidth = intersection[2] / scale + 'px'\n      const clipHeight = intersection[3] / scale + 'px'\n\n      return `polygon(0% 0%, 0% 100%, ${clipX} 100%, ${clipX} ${clipY}, calc(${clipX} + ${clipWidth}) ${clipY}, calc(${clipX} + ${clipWidth}) calc(${clipY} + ${clipHeight}), ${clipX} calc(${clipY} + ${clipHeight}), ${clipX} 100%, 100% 100%, 100% 0%)`\n    }\n\n    return ''\n  }\n\n  /**\n   * Updates the clip-path style based on element and selection information\n   */\n  const updateClipPath = (\n    element: HTMLElement,\n    canvasElement: HTMLCanvasElement,\n    isSelected: boolean,\n    selectedArea?: {\n      x: number\n      y: number\n      width: number\n      height: number\n      scale: number\n      offset: [number, number]\n    }\n  ) => {\n    const elementRect = element.getBoundingClientRect()\n    const canvasRect = canvasElement.getBoundingClientRect()\n\n    const clipPath = calculateClipPath(\n      elementRect,\n      canvasRect,\n      isSelected,\n      selectedArea\n    )\n\n    style.value = {\n      clipPath: clipPath || 'none',\n      willChange: 'clip-path'\n    }\n  }\n\n  return {\n    style,\n    updateClipPath\n  }\n}\n", "<template>\n  <div\n    v-show=\"widgetState.visible\"\n    ref=\"widgetElement\"\n    class=\"dom-widget\"\n    :title=\"tooltip\"\n    :style=\"style\"\n  >\n    <component\n      :is=\"widget.component\"\n      v-if=\"isComponentWidget(widget)\"\n      :model-value=\"widget.value\"\n      :widget=\"widget\"\n      v-bind=\"widget.props\"\n      @update:model-value=\"emit('update:widgetValue', $event)\"\n    />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { useElementBounding, useEventListener } from '@vueuse/core'\nimport { CSSProperties, computed, onMounted, ref, watch } from 'vue'\n\nimport { useAbsolutePosition } from '@/composables/element/useAbsolutePosition'\nimport { useDomClipping } from '@/composables/element/useDomClipping'\nimport { isComponentWidget, isDOMWidget } from '@/scripts/domWidget'\nimport { DomWidgetState } from '@/stores/domWidgetStore'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { useSettingStore } from '@/stores/settingStore'\n\nconst { widgetState } = defineProps<{\n  widgetState: DomWidgetState\n}>()\nconst widget = widgetState.widget\n\nconst emit = defineEmits<{\n  'update:widgetValue': [value: string | object]\n}>()\n\nconst widgetElement = ref<HTMLElement | undefined>()\n\n/**\n * @note Do NOT convert style to a computed value, as it will cause lag when\n * updating the style on different animation frames. Vue's computed value is\n * evaluated asynchronously.\n */\nconst style = ref<CSSProperties>({})\nconst { style: positionStyle, updatePosition } = useAbsolutePosition({\n  useTransform: true\n})\nconst { style: clippingStyle, updateClipPath } = useDomClipping()\n\nconst canvasStore = useCanvasStore()\nconst settingStore = useSettingStore()\nconst enableDomClipping = computed(() =>\n  settingStore.get('Comfy.DOMClippingEnabled')\n)\n\nconst updateDomClipping = () => {\n  const lgCanvas = canvasStore.canvas\n  if (!lgCanvas || !widgetElement.value) return\n\n  const selectedNode = Object.values(lgCanvas.selected_nodes ?? {})[0]\n  if (!selectedNode) return\n\n  const node = widget.node\n  const isSelected = selectedNode === node\n  const renderArea = selectedNode?.renderArea\n  const offset = lgCanvas.ds.offset\n  const scale = lgCanvas.ds.scale\n  const selectedAreaConfig = renderArea\n    ? {\n        x: renderArea[0],\n        y: renderArea[1],\n        width: renderArea[2],\n        height: renderArea[3],\n        scale,\n        offset: [offset[0], offset[1]] as [number, number]\n      }\n    : undefined\n\n  updateClipPath(\n    widgetElement.value,\n    lgCanvas.canvas,\n    isSelected,\n    selectedAreaConfig\n  )\n}\n\n/**\n * @note mapping between canvas position and client position depends on the\n * canvas element's position, so we need to watch the canvas element's position\n * and update the position of the widget accordingly.\n */\nconst { left, top } = useElementBounding(canvasStore.getCanvas().canvas)\nwatch(\n  [() => widgetState, left, top],\n  ([widgetState, _, __]) => {\n    updatePosition(widgetState)\n    if (enableDomClipping.value) {\n      updateDomClipping()\n    }\n\n    style.value = {\n      ...positionStyle.value,\n      ...(enableDomClipping.value ? clippingStyle.value : {}),\n      zIndex: widgetState.zIndex,\n      pointerEvents:\n        widgetState.readonly || widget.computedDisabled ? 'none' : 'auto',\n      opacity: widget.computedDisabled ? 0.5 : 1\n    }\n  },\n  { deep: true }\n)\n\nwatch(\n  () => widgetState.visible,\n  (newVisible, oldVisible) => {\n    if (!newVisible && oldVisible) {\n      widget.options.onHide?.(widget)\n    }\n  }\n)\n\nif (isDOMWidget(widget)) {\n  if (widget.element.blur) {\n    useEventListener(document, 'mousedown', (event) => {\n      if (!widget.element.contains(event.target as HTMLElement)) {\n        widget.element.blur()\n      }\n    })\n  }\n\n  for (const evt of widget.options.selectOn ?? ['focus', 'click']) {\n    useEventListener(widget.element, evt, () => {\n      const lgCanvas = canvasStore.canvas\n      lgCanvas?.selectNode(widget.node)\n      lgCanvas?.bringToFront(widget.node)\n    })\n  }\n}\n\nconst inputSpec = widget.node.constructor.nodeData\nconst tooltip = inputSpec?.inputs?.[widget.name]?.tooltip\n\nonMounted(() => {\n  if (isDOMWidget(widget) && widgetElement.value) {\n    widgetElement.value.appendChild(widget.element)\n  }\n})\n</script>\n\n<style scoped>\n.dom-widget > * {\n  @apply h-full w-full;\n}\n</style>\n", "<template>\n  <!-- Create a new stacking context for widgets to avoid z-index issues -->\n  <div class=\"isolate\">\n    <DomWidget\n      v-for=\"widgetState in widgetStates\"\n      :key=\"widgetState.widget.id\"\n      :widget-state=\"widgetState\"\n      @update:widget-value=\"widgetState.widget.value = $event\"\n    />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport type { LGraphNode } from '@comfyorg/litegraph'\nimport { whenever } from '@vueuse/core'\nimport { computed } from 'vue'\n\nimport DomWidget from '@/components/graph/widgets/DomWidget.vue'\nimport { useChainCallback } from '@/composables/functional/useChainCallback'\nimport { useDomWidgetStore } from '@/stores/domWidgetStore'\nimport { useCanvasStore } from '@/stores/graphStore'\n\nconst domWidgetStore = useDomWidgetStore()\nconst widgetStates = computed(() =>\n  Array.from(domWidgetStore.widgetStates.values())\n)\n\nconst updateWidgets = () => {\n  const lgCanvas = canvasStore.canvas\n  if (!lgCanvas) return\n\n  const lowQuality = lgCanvas.low_quality\n  for (const widgetState of domWidgetStore.widgetStates.values()) {\n    const widget = widgetState.widget\n    const node = widget.node as LGraphNode\n\n    const visible =\n      lgCanvas.isNodeVisible(node) &&\n      !(widget.options.hideOnZoom && lowQuality) &&\n      widget.isVisible()\n\n    widgetState.visible = visible\n    if (visible) {\n      const margin = widget.margin\n      widgetState.pos = [node.pos[0] + margin, node.pos[1] + margin + widget.y]\n      widgetState.size = [\n        (widget.width ?? node.width) - margin * 2,\n        (widget.computedHeight ?? 50) - margin * 2\n      ]\n      // TODO: optimize this logic as it's O(n), where n is the number of nodes\n      widgetState.zIndex = lgCanvas.graph?.nodes.indexOf(node) ?? -1\n      widgetState.readonly = lgCanvas.read_only\n    }\n  }\n}\n\nconst canvasStore = useCanvasStore()\nwhenever(\n  () => canvasStore.canvas,\n  (canvas) =>\n    (canvas.onDrawForeground = useChainCallback(\n      canvas.onDrawForeground,\n      updateWidgets\n    )),\n  { immediate: true }\n)\n</script>\n", "<template>\n  <ButtonGroup\n    class=\"p-buttongroup-vertical absolute bottom-[10px] right-[10px] z-[1000]\"\n  >\n    <Button\n      v-tooltip.left=\"t('graphCanvasMenu.zoomIn')\"\n      severity=\"secondary\"\n      icon=\"pi pi-plus\"\n      :aria-label=\"$t('graphCanvasMenu.zoomIn')\"\n      @mousedown=\"repeat('Comfy.Canvas.ZoomIn')\"\n      @mouseup=\"stopRepeat\"\n    />\n    <Button\n      v-tooltip.left=\"t('graphCanvasMenu.zoomOut')\"\n      severity=\"secondary\"\n      icon=\"pi pi-minus\"\n      :aria-label=\"$t('graphCanvasMenu.zoomOut')\"\n      @mousedown=\"repeat('Comfy.Canvas.ZoomOut')\"\n      @mouseup=\"stopRepeat\"\n    />\n    <Button\n      v-tooltip.left=\"t('graphCanvasMenu.fitView')\"\n      severity=\"secondary\"\n      icon=\"pi pi-expand\"\n      :aria-label=\"$t('graphCanvasMenu.fitView')\"\n      @click=\"() => commandStore.execute('Comfy.Canvas.FitView')\"\n    />\n    <Button\n      v-tooltip.left=\"\n        t(\n          'graphCanvasMenu.' +\n            (canvasStore.canvas?.read_only ? 'panMode' : 'selectMode')\n        ) + ' (Space)'\n      \"\n      severity=\"secondary\"\n      :aria-label=\"\n        t(\n          'graphCanvasMenu.' +\n            (canvasStore.canvas?.read_only ? 'panMode' : 'selectMode')\n        )\n      \"\n      @click=\"() => commandStore.execute('Comfy.Canvas.ToggleLock')\"\n    >\n      <template #icon>\n        <i-material-symbols:pan-tool-outline\n          v-if=\"canvasStore.canvas?.read_only\"\n        />\n        <i-simple-line-icons:cursor v-else />\n      </template>\n    </Button>\n    <Button\n      v-tooltip.left=\"t('graphCanvasMenu.toggleLinkVisibility')\"\n      severity=\"secondary\"\n      :icon=\"linkHidden ? 'pi pi-eye-slash' : 'pi pi-eye'\"\n      :aria-label=\"$t('graphCanvasMenu.toggleLinkVisibility')\"\n      data-testid=\"toggle-link-visibility-button\"\n      @click=\"() => commandStore.execute('Comfy.Canvas.ToggleLinkVisibility')\"\n    />\n  </ButtonGroup>\n</template>\n\n<script setup lang=\"ts\">\nimport { LiteGraph } from '@comfyorg/litegraph'\nimport Button from 'primevue/button'\nimport ButtonGroup from 'primevue/buttongroup'\nimport { computed } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { useSettingStore } from '@/stores/settingStore'\n\nconst { t } = useI18n()\nconst commandStore = useCommandStore()\nconst canvasStore = useCanvasStore()\nconst settingStore = useSettingStore()\n\nconst linkHidden = computed(\n  () => settingStore.get('Comfy.LinkRenderMode') === LiteGraph.HIDDEN_LINK\n)\n\nlet interval: number | null = null\nconst repeat = async (command: string) => {\n  if (interval) return\n  const cmd = () => commandStore.execute(command)\n  await cmd()\n  interval = window.setInterval(cmd, 100)\n}\nconst stopRepeat = () => {\n  if (interval) {\n    clearInterval(interval)\n    interval = null\n  }\n}\n</script>\n\n<style scoped>\n.p-buttongroup-vertical {\n  display: flex;\n  flex-direction: column;\n  border-radius: var(--p-button-border-radius);\n  overflow: hidden;\n  border: 1px solid var(--p-panel-border-color);\n}\n\n.p-buttongroup-vertical .p-button {\n  margin: 0;\n  border-radius: 0;\n}\n</style>\n", "<template>\n  <div\n    v-if=\"tooltipText\"\n    ref=\"tooltipRef\"\n    class=\"node-tooltip\"\n    :style=\"{ left, top }\"\n  >\n    {{ tooltipText }}\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport {\n  LiteGraph,\n  isOverNodeInput,\n  isOverNodeOutput\n} from '@comfyorg/litegraph'\nimport { useEventListener } from '@vueuse/core'\nimport { nextTick, ref } from 'vue'\n\nimport { st } from '@/i18n'\nimport { app as comfyApp } from '@/scripts/app'\nimport { isDOMWidget } from '@/scripts/domWidget'\nimport { useNodeDefStore } from '@/stores/nodeDefStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { normalizeI18nKey } from '@/utils/formatUtil'\n\nlet idleTimeout: number\nconst nodeDefStore = useNodeDefStore()\nconst settingStore = useSettingStore()\nconst tooltipRef = ref<HTMLDivElement | undefined>()\nconst tooltipText = ref('')\nconst left = ref<string>()\nconst top = ref<string>()\n\nconst hideTooltip = () => (tooltipText.value = '')\n\nconst showTooltip = async (tooltip: string | null | undefined) => {\n  if (!tooltip) return\n\n  left.value = comfyApp.canvas.mouse[0] + 'px'\n  top.value = comfyApp.canvas.mouse[1] + 'px'\n  tooltipText.value = tooltip\n\n  await nextTick()\n\n  const rect = tooltipRef.value?.getBoundingClientRect()\n  if (!rect) return\n\n  if (rect.right > window.innerWidth) {\n    left.value = comfyApp.canvas.mouse[0] - rect.width + 'px'\n  }\n\n  if (rect.top < 0) {\n    top.value = comfyApp.canvas.mouse[1] + rect.height + 'px'\n  }\n}\n\nconst onIdle = () => {\n  const { canvas } = comfyApp\n  const node = canvas.node_over\n  if (!node) return\n\n  const ctor = node.constructor as { title_mode?: 0 | 1 | 2 | 3 }\n  const nodeDef = nodeDefStore.nodeDefsByName[node.type ?? '']\n\n  if (\n    ctor.title_mode !== LiteGraph.NO_TITLE &&\n    canvas.graph_mouse[1] < node.pos[1] // If we are over a node, but not within the node then we are on its title\n  ) {\n    return showTooltip(nodeDef.description)\n  }\n\n  if (node.flags?.collapsed) return\n\n  const inputSlot = isOverNodeInput(\n    node,\n    canvas.graph_mouse[0],\n    canvas.graph_mouse[1],\n    [0, 0]\n  )\n  if (inputSlot !== -1) {\n    const inputName = node.inputs[inputSlot].name\n    const translatedTooltip = st(\n      `nodeDefs.${normalizeI18nKey(node.type ?? '')}.inputs.${normalizeI18nKey(inputName)}.tooltip`,\n      nodeDef.inputs[inputName]?.tooltip ?? ''\n    )\n    return showTooltip(translatedTooltip)\n  }\n\n  const outputSlot = isOverNodeOutput(\n    node,\n    canvas.graph_mouse[0],\n    canvas.graph_mouse[1],\n    [0, 0]\n  )\n  if (outputSlot !== -1) {\n    const translatedTooltip = st(\n      `nodeDefs.${normalizeI18nKey(node.type ?? '')}.outputs.${outputSlot}.tooltip`,\n      nodeDef.outputs[outputSlot]?.tooltip ?? ''\n    )\n    return showTooltip(translatedTooltip)\n  }\n\n  const widget = comfyApp.canvas.getWidgetAtCursor()\n  // Dont show for DOM widgets, these use native browser tooltips as we dont get proper mouse events on these\n  if (widget && !isDOMWidget(widget)) {\n    const translatedTooltip = st(\n      `nodeDefs.${normalizeI18nKey(node.type ?? '')}.inputs.${normalizeI18nKey(widget.name)}.tooltip`,\n      nodeDef.inputs[widget.name]?.tooltip ?? ''\n    )\n    // Widget tooltip can be set dynamically, current translation collection does not support this.\n    return showTooltip(widget.tooltip ?? translatedTooltip)\n  }\n}\n\nconst onMouseMove = (e: MouseEvent) => {\n  hideTooltip()\n  clearTimeout(idleTimeout)\n\n  if ((e.target as Node).nodeName !== 'CANVAS') return\n  idleTimeout = window.setTimeout(\n    onIdle,\n    settingStore.get('LiteGraph.Node.TooltipDelay')\n  )\n}\n\nuseEventListener(window, 'mousemove', onMouseMove)\nuseEventListener(window, 'click', hideTooltip)\n</script>\n\n<style lang=\"css\" scoped>\n.node-tooltip {\n  pointer-events: none;\n  background: var(--comfy-input-bg);\n  border-radius: 5px;\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);\n  color: var(--input-text);\n  font-family: sans-serif;\n  left: 0;\n  max-width: 30vw;\n  padding: 4px 8px;\n  position: absolute;\n  top: 0;\n  transform: translate(5px, calc(-100% - 5px));\n  white-space: pre-wrap;\n  z-index: 99999;\n}\n</style>\n", "<!-- This component is used to bound the selected items on the canvas. -->\n<template>\n  <div\n    v-show=\"visible\"\n    class=\"selection-overlay-container pointer-events-none z-40\"\n    :class=\"{\n      'show-border': showBorder\n    }\"\n    :style=\"style\"\n  >\n    <slot />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { createBounds } from '@comfyorg/litegraph'\nimport { whenever } from '@vueuse/core'\nimport { ref, watch } from 'vue'\n\nimport { useAbsolutePosition } from '@/composables/element/useAbsolutePosition'\nimport { useCanvasStore } from '@/stores/graphStore'\n\nconst canvasStore = useCanvasStore()\nconst { style, updatePosition } = useAbsolutePosition()\n\nconst visible = ref(false)\nconst showBorder = ref(false)\n\nconst positionSelectionOverlay = () => {\n  const { selectedItems } = canvasStore.getCanvas()\n  showBorder.value = selectedItems.size > 1\n\n  if (!selectedItems.size) {\n    visible.value = false\n    return\n  }\n\n  visible.value = true\n  const bounds = createBounds(selectedItems)\n  if (bounds) {\n    updatePosition({\n      pos: [bounds[0], bounds[1]],\n      size: [bounds[2], bounds[3]]\n    })\n  }\n}\n\n// Register listener on canvas creation.\nwhenever(\n  () => canvasStore.getCanvas().state.selectionChanged,\n  () => {\n    requestAnimationFrame(() => {\n      positionSelectionOverlay()\n      canvasStore.getCanvas().state.selectionChanged = false\n    })\n  },\n  { immediate: true }\n)\n\ncanvasStore.getCanvas().ds.onChanged = positionSelectionOverlay\n\nwatch(\n  () => canvasStore.canvas?.state?.draggingItems,\n  (draggingItems) => {\n    // Litegraph draggingItems state can end early before the bounding boxes of\n    // the selected items are updated. Delay to make sure we put the overlay in\n    // the correct position.\n    // https://github.com/Comfy-Org/ComfyUI_frontend/issues/2656\n    if (draggingItems === false) {\n      requestAnimationFrame(() => {\n        visible.value = true\n        positionSelectionOverlay()\n      })\n    } else {\n      // Selection change update to visible state is delayed by a frame. Here\n      // we also delay a frame so that the order of events is correct when\n      // the initial selection and dragging happens at the same time.\n      requestAnimationFrame(() => {\n        visible.value = false\n      })\n    }\n  }\n)\n</script>\n\n<style scoped>\n.selection-overlay-container > * {\n  pointer-events: auto;\n}\n\n.show-border {\n  @apply border-dashed rounded-md border-2 border-[var(--border-color)];\n}\n</style>\n", "<template>\n  <Button\n    v-show=\"canvasStore.nodeSelected\"\n    v-tooltip.top=\"{\n      value: t('commands.Comfy_Canvas_ToggleSelectedNodes_Bypass.label'),\n      showDelay: 1000\n    }\"\n    severity=\"secondary\"\n    text\n    data-testid=\"bypass-button\"\n    @click=\"\n      () => commandStore.execute('Comfy.Canvas.ToggleSelectedNodes.Bypass')\n    \"\n  >\n    <template #icon>\n      <i-game-icons:detour />\n    </template>\n  </Button>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport { useI18n } from 'vue-i18n'\n\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useCanvasStore } from '@/stores/graphStore'\n\nconst { t } = useI18n()\nconst commandStore = useCommandStore()\nconst canvasStore = useCanvasStore()\n</script>\n", "<template>\n  <div class=\"relative\">\n    <Button\n      v-show=\"canvasStore.nodeSelected || canvasStore.groupSelected\"\n      severity=\"secondary\"\n      text\n      @click=\"() => (showColorPicker = !showColorPicker)\"\n    >\n      <template #icon>\n        <div class=\"flex items-center gap-1\">\n          <i class=\"pi pi-circle-fill\" :style=\"{ color: currentColor ?? '' }\" />\n          <i class=\"pi pi-chevron-down\" :style=\"{ fontSize: '0.5rem' }\" />\n        </div>\n      </template>\n    </Button>\n    <div\n      v-if=\"showColorPicker\"\n      class=\"color-picker-container absolute -top-10 left-1/2\"\n    >\n      <SelectButton\n        :model-value=\"selectedColorOption\"\n        :options=\"colorOptions\"\n        option-label=\"name\"\n        data-key=\"value\"\n        @update:model-value=\"applyColor\"\n      >\n        <template #option=\"{ option }\">\n          <i\n            v-tooltip.top=\"option.localizedName\"\n            class=\"pi pi-circle-fill\"\n            :style=\"{\n              color: isLightTheme ? option.value.light : option.value.dark\n            }\"\n            :data-testid=\"option.name\"\n          />\n        </template>\n      </SelectButton>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport type { ColorOption as CanvasColorOption } from '@comfyorg/litegraph'\nimport { LGraphCanvas, LiteGraph, isColorable } from '@comfyorg/litegraph'\nimport Button from 'primevue/button'\nimport SelectButton from 'primevue/selectbutton'\nimport { computed, ref, watch } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { useWorkflowStore } from '@/stores/workflowStore'\nimport { useColorPaletteStore } from '@/stores/workspace/colorPaletteStore'\nimport { adjustColor } from '@/utils/colorUtil'\nimport { getItemsColorOption } from '@/utils/litegraphUtil'\n\nconst { t } = useI18n()\nconst canvasStore = useCanvasStore()\nconst colorPaletteStore = useColorPaletteStore()\nconst workflowStore = useWorkflowStore()\nconst isLightTheme = computed(\n  () => colorPaletteStore.completedActivePalette.light_theme\n)\nconst toLightThemeColor = (color: string) =>\n  adjustColor(color, { lightness: 0.5 })\n\nconst showColorPicker = ref(false)\n\ntype ColorOption = {\n  name: string\n  localizedName: string\n  value: {\n    dark: string\n    light: string\n  }\n}\n\nconst NO_COLOR_OPTION: ColorOption = {\n  name: 'noColor',\n  localizedName: t('color.noColor'),\n  value: {\n    dark: LiteGraph.NODE_DEFAULT_BGCOLOR,\n    light: toLightThemeColor(LiteGraph.NODE_DEFAULT_BGCOLOR)\n  }\n}\nconst colorOptions: ColorOption[] = [\n  NO_COLOR_OPTION,\n  ...Object.entries(LGraphCanvas.node_colors).map(([name, color]) => ({\n    name,\n    localizedName: t(`color.${name}`),\n    value: {\n      dark: color.bgcolor,\n      light: toLightThemeColor(color.bgcolor)\n    }\n  }))\n]\n\nconst selectedColorOption = ref<ColorOption | null>(null)\nconst applyColor = (colorOption: ColorOption | null) => {\n  const colorName = colorOption?.name ?? NO_COLOR_OPTION.name\n  const canvasColorOption =\n    colorName === NO_COLOR_OPTION.name\n      ? null\n      : LGraphCanvas.node_colors[colorName]\n\n  for (const item of canvasStore.selectedItems) {\n    if (isColorable(item)) {\n      item.setColorOption(canvasColorOption)\n    }\n  }\n\n  canvasStore.canvas?.setDirty(true, true)\n  currentColorOption.value = canvasColorOption\n  showColorPicker.value = false\n  workflowStore.activeWorkflow?.changeTracker.checkState()\n}\n\nconst currentColorOption = ref<CanvasColorOption | null>(null)\nconst currentColor = computed(() =>\n  currentColorOption.value\n    ? isLightTheme.value\n      ? toLightThemeColor(currentColorOption.value?.bgcolor)\n      : currentColorOption.value?.bgcolor\n    : null\n)\n\nwatch(\n  () => canvasStore.selectedItems,\n  (newSelectedItems) => {\n    showColorPicker.value = false\n    selectedColorOption.value = null\n    currentColorOption.value = getItemsColorOption(newSelectedItems)\n  }\n)\n</script>\n\n<style scoped>\n.color-picker-container {\n  transform: translateX(-50%);\n}\n\n:deep(.p-togglebutton) {\n  @apply py-2 px-1;\n}\n</style>\n", "<template>\n  <Button\n    v-tooltip.top=\"{\n      value: t('commands.Comfy_Canvas_DeleteSelectedItems.label'),\n      showDelay: 1000\n    }\"\n    severity=\"danger\"\n    text\n    icon=\"pi pi-trash\"\n    @click=\"() => commandStore.execute('Comfy.Canvas.DeleteSelectedItems')\"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport { useI18n } from 'vue-i18n'\n\nimport { useCommandStore } from '@/stores/commandStore'\n\nconst { t } = useI18n()\nconst commandStore = useCommandStore()\n</script>\n", "<template>\n  <Button\n    v-show=\"canvasStore.nodeSelected\"\n    v-tooltip.top=\"{\n      value: isDisabled\n        ? t('selectionToolbox.executeButton.disabledTooltip')\n        : t('selectionToolbox.executeButton.tooltip'),\n      showDelay: 1000\n    }\"\n    :severity=\"isDisabled ? 'secondary' : 'success'\"\n    text\n    :disabled=\"isDisabled\"\n    @mouseenter=\"() => handleMouseEnter()\"\n    @mouseleave=\"() => handleMouseLeave()\"\n    @click=\"handleClick\"\n  >\n    <i-lucide:play />\n  </Button>\n</template>\n\n<script setup lang=\"ts\">\nimport type { LGraphNode } from '@comfyorg/litegraph'\nimport Button from 'primevue/button'\nimport { computed, ref } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { isLGraphNode } from '@/utils/litegraphUtil'\n\nconst { t } = useI18n()\nconst canvasStore = useCanvasStore()\nconst commandStore = useCommandStore()\n\nconst canvas = canvasStore.getCanvas()\nconst buttonHovered = ref(false)\nconst selectedOutputNodes = computed(\n  () =>\n    canvasStore.selectedItems.filter(\n      (item) => isLGraphNode(item) && item.constructor.nodeData?.output_node\n    ) as LGraphNode[]\n)\n\nconst isDisabled = computed(() => selectedOutputNodes.value.length === 0)\n\nfunction outputNodeStokeStyle(this: LGraphNode) {\n  if (\n    this.selected &&\n    this.constructor.nodeData?.output_node &&\n    buttonHovered.value\n  ) {\n    return { color: 'orange', lineWidth: 2, padding: 10 }\n  }\n}\n\nconst handleMouseEnter = () => {\n  buttonHovered.value = true\n  for (const node of selectedOutputNodes.value) {\n    node.strokeStyles['outputNode'] = outputNodeStokeStyle\n  }\n  canvas.setDirty(true)\n}\n\nconst handleMouseLeave = () => {\n  buttonHovered.value = false\n  canvas.setDirty(true)\n}\n\nconst handleClick = async () => {\n  await commandStore.execute('Comfy.QueueSelectedOutputNodes')\n}\n</script>\n", "<template>\n  <Button\n    v-tooltip.top=\"{\n      value:\n        st(`commands.${normalizeI18nKey(command.id)}.label`, '') || undefined,\n      showDelay: 1000\n    }\"\n    severity=\"secondary\"\n    text\n    :icon=\"typeof command.icon === 'function' ? command.icon() : command.icon\"\n    @click=\"() => commandStore.execute(command.id)\"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\n\nimport { st } from '@/i18n'\nimport { ComfyCommand, useCommandStore } from '@/stores/commandStore'\nimport { normalizeI18nKey } from '@/utils/formatUtil'\n\ndefineProps<{\n  command: ComfyCommand\n}>()\n\nconst commandStore = useCommandStore()\n</script>\n", "<template>\n  <Button\n    v-show=\"isSingleImageNode\"\n    v-tooltip.top=\"{\n      value: t('commands.Comfy_MaskEditor_OpenMaskEditor.label'),\n      showDelay: 1000\n    }\"\n    severity=\"secondary\"\n    text\n    icon=\"pi pi-pencil\"\n    @click=\"openMaskEditor\"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport { computed } from 'vue'\n\nimport { t } from '@/i18n'\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { isImageNode, isLGraphNode } from '@/utils/litegraphUtil'\n\nconst commandStore = useCommandStore()\nconst canvasStore = useCanvasStore()\n\nconst isSingleImageNode = computed(() => {\n  const nodes = canvasStore.selectedItems.filter(isLGraphNode)\n  return nodes.length === 1 && nodes.some(isImageNode)\n})\n\nconst openMaskEditor = () => {\n  void commandStore.execute('Comfy.MaskEditor.OpenMaskEditor')\n}\n</script>\n", "<template>\n  <Button\n    v-show=\"canvasStore.nodeSelected || canvasStore.groupSelected\"\n    v-tooltip.top=\"{\n      value: t('commands.Comfy_Canvas_ToggleSelectedNodes_Pin.label'),\n      showDelay: 1000\n    }\"\n    severity=\"secondary\"\n    text\n    icon=\"pi pi-thumbtack\"\n    @click=\"() => commandStore.execute('Comfy.Canvas.ToggleSelected.Pin')\"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport { useI18n } from 'vue-i18n'\n\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useCanvasStore } from '@/stores/graphStore'\n\nconst { t } = useI18n()\nconst commandStore = useCommandStore()\nconst canvasStore = useCanvasStore()\n</script>\n", "import type { LGraphNode } from '@comfyorg/litegraph'\nimport type { IBaseWidget } from '@comfyorg/litegraph/dist/types/widgets'\nimport { computed, ref, watchEffect } from 'vue'\n\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { isLGraphNode } from '@/utils/litegraphUtil'\n\ninterface RefreshableItem {\n  refresh: () => Promise<void> | void\n}\n\ntype RefreshableWidget = IBaseWidget & RefreshableItem\n\nconst isRefreshableWidget = (\n  widget: IBaseWidget\n): widget is RefreshableWidget =>\n  'refresh' in widget && typeof widget.refresh === 'function'\n\n/**\n * Tracks selected nodes and their refreshable widgets\n */\nexport const useRefreshableSelection = () => {\n  const graphStore = useCanvasStore()\n  const selectedNodes = ref<LGraphNode[]>([])\n\n  watchEffect(() => {\n    selectedNodes.value = graphStore.selectedItems.filter(isLGraphNode)\n  })\n\n  const refreshableWidgets = computed(() =>\n    selectedNodes.value.flatMap(\n      (node) => node.widgets?.filter(isRefreshableWidget) ?? []\n    )\n  )\n\n  const isRefreshable = computed(() => refreshableWidgets.value.length > 0)\n\n  async function refreshSelected() {\n    if (!isRefreshable.value) return\n\n    await Promise.all(refreshableWidgets.value.map((item) => item.refresh()))\n  }\n\n  return {\n    isRefreshable,\n    refreshSelected\n  }\n}\n", "<template>\n  <Button\n    v-show=\"isRefreshable\"\n    severity=\"info\"\n    text\n    icon=\"pi pi-refresh\"\n    @click=\"refreshSelected\"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\n\nimport { useRefreshableSelection } from '@/composables/useRefreshableSelection'\n\nconst { isRefreshable, refreshSelected } = useRefreshableSelection()\n</script>\n", "<template>\n  <Panel\n    class=\"selection-toolbox absolute left-1/2 rounded-lg\"\n    :pt=\"{\n      header: 'hidden',\n      content: 'p-0 flex flex-row'\n    }\"\n  >\n    <ExecuteButton />\n    <ColorPickerButton />\n    <BypassButton />\n    <PinButton />\n    <MaskEditorButton />\n    <DeleteButton />\n    <RefreshButton />\n    <ExtensionCommandButton\n      v-for=\"command in extensionToolboxCommands\"\n      :key=\"command.id\"\n      :command=\"command\"\n    />\n  </Panel>\n</template>\n\n<script setup lang=\"ts\">\nimport Panel from 'primevue/panel'\nimport { computed } from 'vue'\n\nimport BypassButton from '@/components/graph/selectionToolbox/BypassButton.vue'\nimport ColorPickerButton from '@/components/graph/selectionToolbox/ColorPickerButton.vue'\nimport DeleteButton from '@/components/graph/selectionToolbox/DeleteButton.vue'\nimport ExecuteButton from '@/components/graph/selectionToolbox/ExecuteButton.vue'\nimport ExtensionCommandButton from '@/components/graph/selectionToolbox/ExtensionCommandButton.vue'\nimport MaskEditorButton from '@/components/graph/selectionToolbox/MaskEditorButton.vue'\nimport PinButton from '@/components/graph/selectionToolbox/PinButton.vue'\nimport RefreshButton from '@/components/graph/selectionToolbox/RefreshButton.vue'\nimport { useExtensionService } from '@/services/extensionService'\nimport { type ComfyCommandImpl, useCommandStore } from '@/stores/commandStore'\nimport { useCanvasStore } from '@/stores/graphStore'\n\nconst commandStore = useCommandStore()\nconst canvasStore = useCanvasStore()\nconst extensionService = useExtensionService()\n\nconst extensionToolboxCommands = computed<ComfyCommandImpl[]>(() => {\n  const commandIds = new Set<string>(\n    canvasStore.selectedItems\n      .map(\n        (item) =>\n          extensionService\n            .invokeExtensions('getSelectionToolboxCommands', item)\n            .flat() as string[]\n      )\n      .flat()\n  )\n  return Array.from(commandIds)\n    .map((commandId) => commandStore.getCommand(commandId))\n    .filter((command): command is ComfyCommandImpl => command !== undefined)\n})\n</script>\n\n<style scoped>\n.selection-toolbox {\n  transform: translateX(-50%) translateY(-120%);\n}\n</style>\n", "<template>\n  <div\n    v-if=\"showInput\"\n    class=\"group-title-editor node-title-editor\"\n    :style=\"inputStyle\"\n  >\n    <EditableText\n      :is-editing=\"showInput\"\n      :model-value=\"editedTitle\"\n      @edit=\"onEdit\"\n    />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { LGraphGroup, LGraphNode, LiteGraph } from '@comfyorg/litegraph'\nimport type { LiteGraphCanvasEvent } from '@comfyorg/litegraph'\nimport { useEventListener } from '@vueuse/core'\nimport { type CSSProperties, computed, ref, watch } from 'vue'\n\nimport EditableText from '@/components/common/EditableText.vue'\nimport { useAbsolutePosition } from '@/composables/element/useAbsolutePosition'\nimport { app } from '@/scripts/app'\nimport { useCanvasStore, useTitleEditorStore } from '@/stores/graphStore'\nimport { useSettingStore } from '@/stores/settingStore'\n\nconst settingStore = useSettingStore()\n\nconst showInput = ref(false)\nconst editedTitle = ref('')\nconst { style: inputPositionStyle, updatePosition } = useAbsolutePosition()\nconst inputFontStyle = ref<CSSProperties>({})\nconst inputStyle = computed<CSSProperties>(() => ({\n  ...inputPositionStyle.value,\n  ...inputFontStyle.value\n}))\n\nconst titleEditorStore = useTitleEditorStore()\nconst canvasStore = useCanvasStore()\nconst previousCanvasDraggable = ref(true)\n\nconst onEdit = (newValue: string) => {\n  if (titleEditorStore.titleEditorTarget && newValue.trim() !== '') {\n    titleEditorStore.titleEditorTarget.title = newValue.trim()\n    app.graph.setDirtyCanvas(true, true)\n  }\n  showInput.value = false\n  titleEditorStore.titleEditorTarget = null\n  canvasStore.canvas!.allow_dragcanvas = previousCanvasDraggable.value\n}\n\nwatch(\n  () => titleEditorStore.titleEditorTarget,\n  (target) => {\n    if (target === null) {\n      return\n    }\n    editedTitle.value = target.title\n    showInput.value = true\n    const canvas = canvasStore.canvas!\n    previousCanvasDraggable.value = canvas.allow_dragcanvas\n    canvas.allow_dragcanvas = false\n    const scale = canvas.ds.scale\n\n    if (target instanceof LGraphGroup) {\n      const group = target\n      updatePosition({\n        pos: group.pos,\n        size: [group.size[0], group.titleHeight]\n      })\n      inputFontStyle.value = { fontSize: `${group.font_size * scale}px` }\n    } else if (target instanceof LGraphNode) {\n      const node = target\n      const [x, y] = node.getBounding()\n      updatePosition({\n        pos: [x, y],\n        size: [node.width, LiteGraph.NODE_TITLE_HEIGHT]\n      })\n      inputFontStyle.value = { fontSize: `${12 * scale}px` }\n    }\n  }\n)\n\nconst canvasEventHandler = (event: LiteGraphCanvasEvent) => {\n  if (event.detail.subType === 'group-double-click') {\n    if (!settingStore.get('Comfy.Group.DoubleClickTitleToEdit')) {\n      return\n    }\n\n    const group: LGraphGroup = event.detail.group\n    const [_, y] = group.pos\n\n    const e = event.detail.originalEvent\n    const relativeY = e.canvasY - y\n    // Only allow editing if the click is on the title bar\n    if (relativeY <= group.titleHeight) {\n      titleEditorStore.titleEditorTarget = group\n    }\n  } else if (event.detail.subType === 'node-double-click') {\n    if (!settingStore.get('Comfy.Node.DoubleClickTitleToEdit')) {\n      return\n    }\n\n    const node: LGraphNode = event.detail.node\n    const [_, y] = node.pos\n\n    const e = event.detail.originalEvent\n    const relativeY = e.canvasY - y\n    // Only allow editing if the click is on the title bar\n    if (relativeY <= 0) {\n      titleEditorStore.titleEditorTarget = node\n    }\n  }\n}\n\nuseEventListener(document, 'litegraph:canvas', canvasEventHandler)\n</script>\n\n<style scoped>\n.group-title-editor.node-title-editor {\n  z-index: 9999;\n  padding: 0.25rem;\n}\n\n:deep(.editable-text) {\n  width: 100%;\n  height: 100%;\n}\n\n:deep(.editable-text input) {\n  width: 100%;\n  height: 100%;\n  /* Override the default font size */\n  font-size: inherit;\n}\n</style>\n", "import { defineStore } from 'pinia'\nimport { ref } from 'vue'\n\nexport const useSearchBoxStore = defineStore('searchBox', () => {\n  const visible = ref(false)\n  function toggleVisible() {\n    visible.value = !visible.value\n  }\n\n  return {\n    visible,\n    toggleVisible\n  }\n})\n", "export enum LinkReleaseTriggerMode {\n  ALWAYS = 'always',\n  HOLD_SHIFT = 'hold shift',\n  NOT_HOLD_SHIFT = 'NOT hold shift'\n}\n\nexport enum LinkReleaseTriggerAction {\n  CONTEXT_MENU = 'context menu',\n  SEARCH_BOX = 'search box',\n  NO_ACTION = 'no action'\n}\n", "<!-- Auto complete with extra event \"focused-option-changed\" -->\n<script>\nimport AutoComplete from 'primevue/autocomplete'\n\nexport default {\n  name: 'AutoCompletePlus',\n  extends: AutoComplete,\n  emits: ['focused-option-changed'],\n  data() {\n    return {\n      // Flag to determine if IME is active\n      isComposing: false\n    }\n  },\n  mounted() {\n    if (typeof AutoComplete.mounted === 'function') {\n      AutoComplete.mounted.call(this)\n    }\n\n    // Retrieve the actual <input> element and attach IME events\n    const inputEl = this.$el.querySelector('input')\n    if (inputEl) {\n      inputEl.addEventListener('compositionstart', () => {\n        this.isComposing = true\n      })\n      inputEl.addEventListener('compositionend', () => {\n        this.isComposing = false\n      })\n    }\n    // Add a watcher on the focusedOptionIndex property\n    this.$watch(\n      () => this.focusedOptionIndex,\n      (newVal, oldVal) => {\n        // Emit a custom event when focusedOptionIndex changes\n        this.$emit('focused-option-changed', newVal)\n      }\n    )\n  },\n  methods: {\n    // Override onKeyDown to block <PERSON><PERSON> when IME is active\n    onKeyDown(event) {\n      if (event.key === 'Enter' && this.isComposing) {\n        event.preventDefault()\n        event.stopPropagation()\n        return\n      }\n\n      AutoComplete.methods.onKeyDown.call(this, event)\n    }\n  }\n}\n</script>\n", "<template>\n  <div\n    class=\"option-container flex justify-between items-center px-2 py-0 cursor-pointer overflow-hidden w-full\"\n  >\n    <div class=\"option-display-name font-semibold flex flex-col\">\n      <div>\n        <span v-if=\"isBookmarked\">\n          <i class=\"pi pi-bookmark-fill text-sm mr-1\" />\n        </span>\n        <span v-html=\"highlightQuery(nodeDef.display_name, currentQuery)\" />\n        <span>&nbsp;</span>\n        <Tag v-if=\"showIdName\" severity=\"secondary\">\n          <span v-html=\"highlightQuery(nodeDef.name, currentQuery)\" />\n        </Tag>\n      </div>\n      <div\n        v-if=\"showCategory\"\n        class=\"option-category font-light text-sm text-muted overflow-hidden text-ellipsis whitespace-nowrap\"\n      >\n        {{ nodeDef.category.replaceAll('/', ' > ') }}\n      </div>\n    </div>\n    <div class=\"option-badges\">\n      <Tag\n        v-if=\"nodeDef.experimental\"\n        :value=\"$t('g.experimental')\"\n        severity=\"primary\"\n      />\n      <Tag\n        v-if=\"nodeDef.deprecated\"\n        :value=\"$t('g.deprecated')\"\n        severity=\"danger\"\n      />\n      <Tag\n        v-if=\"showNodeFrequency && nodeFrequency > 0\"\n        :value=\"formatNumberWithSuffix(nodeFrequency, { roundToInt: true })\"\n        severity=\"secondary\"\n      />\n      <Chip\n        v-if=\"nodeDef.nodeSource.type !== NodeSourceType.Unknown\"\n        class=\"text-sm font-light\"\n      >\n        {{ nodeDef.nodeSource.displayText }}\n      </Chip>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Chip from 'primevue/chip'\nimport Tag from 'primevue/tag'\nimport { computed } from 'vue'\n\nimport { useNodeBookmarkStore } from '@/stores/nodeBookmarkStore'\nimport { ComfyNodeDefImpl, useNodeFrequencyStore } from '@/stores/nodeDefStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { NodeSourceType } from '@/types/nodeSource'\nimport { highlightQuery } from '@/utils/formatUtil'\nimport { formatNumberWithSuffix } from '@/utils/formatUtil'\n\nconst settingStore = useSettingStore()\nconst showCategory = computed(() =>\n  settingStore.get('Comfy.NodeSearchBoxImpl.ShowCategory')\n)\nconst showIdName = computed(() =>\n  settingStore.get('Comfy.NodeSearchBoxImpl.ShowIdName')\n)\nconst showNodeFrequency = computed(() =>\n  settingStore.get('Comfy.NodeSearchBoxImpl.ShowNodeFrequency')\n)\nconst nodeFrequencyStore = useNodeFrequencyStore()\nconst nodeFrequency = computed(() =>\n  nodeFrequencyStore.getNodeFrequency(props.nodeDef)\n)\n\nconst nodeBookmarkStore = useNodeBookmarkStore()\nconst isBookmarked = computed(() =>\n  nodeBookmarkStore.isBookmarked(props.nodeDef)\n)\n\nconst props = defineProps<{\n  nodeDef: ComfyNodeDefImpl\n  currentQuery: string\n}>()\n</script>\n\n<style scoped>\n:deep(.highlight) {\n  background-color: var(--p-primary-color);\n  color: var(--p-primary-contrast-color);\n  font-weight: bold;\n  border-radius: 0.25rem;\n  padding: 0 0.125rem;\n  margin: -0.125rem 0.125rem;\n}\n</style>\n", "<template>\n  <div\n    class=\"comfy-vue-node-search-container flex justify-center items-center w-full min-w-96\"\n  >\n    <div\n      v-if=\"enableNodePreview\"\n      class=\"comfy-vue-node-preview-container absolute left-[-350px] top-[50px]\"\n    >\n      <NodePreview\n        v-if=\"hoveredSuggestion\"\n        :key=\"hoveredSuggestion?.name || ''\"\n        :node-def=\"hoveredSuggestion\"\n      />\n    </div>\n\n    <Button\n      icon=\"pi pi-filter\"\n      severity=\"secondary\"\n      class=\"filter-button z-10\"\n      @click=\"nodeSearchFilterVisible = true\"\n    />\n    <Dialog\n      v-model:visible=\"nodeSearchFilterVisible\"\n      class=\"min-w-96\"\n      dismissable-mask\n      modal\n      @hide=\"reFocusInput\"\n    >\n      <template #header>\n        <h3>Add node filter condition</h3>\n      </template>\n      <div class=\"_dialog-body\">\n        <NodeSearchFilter @add-filter=\"onAddFilter\" />\n      </div>\n    </Dialog>\n\n    <AutoCompletePlus\n      :model-value=\"filters\"\n      class=\"comfy-vue-node-search-box z-10 flex-grow\"\n      scroll-height=\"40vh\"\n      :placeholder=\"placeholder\"\n      :input-id=\"inputId\"\n      append-to=\"self\"\n      :suggestions=\"suggestions\"\n      :min-length=\"0\"\n      :delay=\"100\"\n      :loading=\"!nodeFrequencyStore.isLoaded\"\n      complete-on-focus\n      auto-option-focus\n      force-selection\n      multiple\n      :option-label=\"'display_name'\"\n      @complete=\"search($event.query)\"\n      @option-select=\"emit('addNode', $event.value)\"\n      @focused-option-changed=\"setHoverSuggestion($event)\"\n    >\n      <template #option=\"{ option }\">\n        <NodeSearchItem :node-def=\"option\" :current-query=\"currentQuery\" />\n      </template>\n      <!-- FilterAndValue -->\n      <template #chip=\"{ value }\">\n        <SearchFilterChip\n          v-if=\"value.filterDef && value.value\"\n          :key=\"`${value.filterDef.id}-${value.value}`\"\n          :text=\"value.value\"\n          :badge=\"value.filterDef.invokeSequence.toUpperCase()\"\n          :badge-class=\"value.filterDef.invokeSequence + '-badge'\"\n          @remove=\"\n            onRemoveFilter(\n              $event,\n              value as FuseFilterWithValue<ComfyNodeDefImpl, string>\n            )\n          \"\n        />\n      </template>\n    </AutoCompletePlus>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport Dialog from 'primevue/dialog'\nimport { computed, nextTick, onMounted, ref } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport NodePreview from '@/components/node/NodePreview.vue'\nimport AutoCompletePlus from '@/components/primevueOverride/AutoCompletePlus.vue'\nimport NodeSearchFilter from '@/components/searchbox/NodeSearchFilter.vue'\nimport NodeSearchItem from '@/components/searchbox/NodeSearchItem.vue'\nimport {\n  ComfyNodeDefImpl,\n  useNodeDefStore,\n  useNodeFrequencyStore\n} from '@/stores/nodeDefStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport type { FuseFilterWithValue } from '@/utils/fuseUtil'\n\nimport SearchFilterChip from '../common/SearchFilterChip.vue'\n\nconst settingStore = useSettingStore()\nconst { t } = useI18n()\n\nconst enableNodePreview = computed(() =>\n  settingStore.get('Comfy.NodeSearchBoxImpl.NodePreview')\n)\n\nconst { filters, searchLimit = 64 } = defineProps<{\n  filters: FuseFilterWithValue<ComfyNodeDefImpl, string>[]\n  searchLimit?: number\n}>()\n\nconst nodeSearchFilterVisible = ref(false)\nconst inputId = `comfy-vue-node-search-box-input-${Math.random()}`\nconst suggestions = ref<ComfyNodeDefImpl[]>([])\nconst hoveredSuggestion = ref<ComfyNodeDefImpl | null>(null)\nconst currentQuery = ref('')\nconst placeholder = computed(() => {\n  return filters.length === 0 ? t('g.searchNodes') + '...' : ''\n})\n\nconst nodeDefStore = useNodeDefStore()\nconst nodeFrequencyStore = useNodeFrequencyStore()\nconst search = (query: string) => {\n  const queryIsEmpty = query === '' && filters.length === 0\n  currentQuery.value = query\n  suggestions.value = queryIsEmpty\n    ? nodeFrequencyStore.topNodeDefs\n    : [\n        ...nodeDefStore.nodeSearchService.searchNode(query, filters, {\n          limit: searchLimit\n        })\n      ]\n}\n\nconst emit = defineEmits(['addFilter', 'removeFilter', 'addNode'])\n\nlet inputElement: HTMLInputElement | null = null\nconst reFocusInput = async () => {\n  inputElement ??= document.getElementById(inputId) as HTMLInputElement\n  if (inputElement) {\n    inputElement.blur()\n    await nextTick(() => inputElement?.focus())\n  }\n}\n\nonMounted(reFocusInput)\nconst onAddFilter = (\n  filterAndValue: FuseFilterWithValue<ComfyNodeDefImpl, string>\n) => {\n  nodeSearchFilterVisible.value = false\n  emit('addFilter', filterAndValue)\n}\nconst onRemoveFilter = async (\n  event: Event,\n  filterAndValue: FuseFilterWithValue<ComfyNodeDefImpl, string>\n) => {\n  event.stopPropagation()\n  event.preventDefault()\n  emit('removeFilter', filterAndValue)\n  await reFocusInput()\n}\nconst setHoverSuggestion = (index: number) => {\n  if (index === -1) {\n    hoveredSuggestion.value = null\n    return\n  }\n  const value = suggestions.value[index]\n  hoveredSuggestion.value = value\n}\n</script>\n", "<template>\n  <div>\n    <Dialog\n      v-model:visible=\"visible\"\n      modal\n      :dismissable-mask=\"dismissable\"\n      :pt=\"{\n        root: {\n          class: 'invisible-dialog-root',\n          role: 'search'\n        },\n        mask: { class: 'node-search-box-dialog-mask' },\n        transition: {\n          enterFromClass: 'opacity-0 scale-75',\n          // 100ms is the duration of the transition in the dialog component\n          enterActiveClass: 'transition-all duration-100 ease-out',\n          leaveActiveClass: 'transition-all duration-100 ease-in',\n          leaveToClass: 'opacity-0 scale-75'\n        }\n      }\"\n      @hide=\"clearFilters\"\n    >\n      <template #container>\n        <NodeSearchBox\n          :filters=\"nodeFilters\"\n          @add-filter=\"addFilter\"\n          @remove-filter=\"removeFilter\"\n          @add-node=\"addNode\"\n        />\n      </template>\n    </Dialog>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport {\n  LGraphNode,\n  LiteGraph,\n  LiteGraphCanvasEvent\n} from '@comfyorg/litegraph'\nimport { Point } from '@comfyorg/litegraph/dist/interfaces'\nimport type { CanvasPointerEvent } from '@comfyorg/litegraph/dist/types/events'\nimport { useEventListener } from '@vueuse/core'\nimport { storeToRefs } from 'pinia'\nimport Dialog from 'primevue/dialog'\nimport { computed, ref, toRaw, watch, watchEffect } from 'vue'\n\nimport { useLitegraphService } from '@/services/litegraphService'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { ComfyNodeDefImpl, useNodeDefStore } from '@/stores/nodeDefStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useWorkflowStore } from '@/stores/workflowStore'\nimport { useSearchBoxStore } from '@/stores/workspace/searchBoxStore'\nimport { LinkReleaseTriggerAction } from '@/types/searchBoxTypes'\nimport { FuseFilterWithValue } from '@/utils/fuseUtil'\n\nimport NodeSearchBox from './NodeSearchBox.vue'\n\nlet triggerEvent: CanvasPointerEvent | null = null\nlet listenerController: AbortController | null = null\nlet disconnectOnReset = false\n\nconst settingStore = useSettingStore()\nconst litegraphService = useLitegraphService()\n\nconst { visible } = storeToRefs(useSearchBoxStore())\nconst dismissable = ref(true)\nconst getNewNodeLocation = (): Point => {\n  return triggerEvent\n    ? [triggerEvent.canvasX, triggerEvent.canvasY]\n    : litegraphService.getCanvasCenter()\n}\nconst nodeFilters = ref<FuseFilterWithValue<ComfyNodeDefImpl, string>[]>([])\nconst addFilter = (filter: FuseFilterWithValue<ComfyNodeDefImpl, string>) => {\n  nodeFilters.value.push(filter)\n}\nconst removeFilter = (\n  filter: FuseFilterWithValue<ComfyNodeDefImpl, string>\n) => {\n  nodeFilters.value = nodeFilters.value.filter(\n    (f) => toRaw(f) !== toRaw(filter)\n  )\n}\nconst clearFilters = () => {\n  nodeFilters.value = []\n}\nconst closeDialog = () => {\n  visible.value = false\n}\nconst canvasStore = useCanvasStore()\n\nconst addNode = (nodeDef: ComfyNodeDefImpl) => {\n  if (!triggerEvent) {\n    console.warn('The trigger event was undefined when addNode was called.')\n    return\n  }\n\n  disconnectOnReset = false\n  const node = litegraphService.addNodeOnGraph(nodeDef, {\n    pos: getNewNodeLocation()\n  })\n\n  canvasStore.getCanvas().linkConnector.connectToNode(node, triggerEvent)\n\n  // Notify changeTracker - new step should be added\n  useWorkflowStore().activeWorkflow?.changeTracker?.checkState()\n  window.requestAnimationFrame(closeDialog)\n}\n\nconst newSearchBoxEnabled = computed(\n  () => settingStore.get('Comfy.NodeSearchBoxImpl') === 'default'\n)\nconst showSearchBox = (e: CanvasPointerEvent) => {\n  if (newSearchBoxEnabled.value) {\n    if (e.pointerType === 'touch') {\n      setTimeout(() => {\n        showNewSearchBox(e)\n      }, 128)\n    } else {\n      showNewSearchBox(e)\n    }\n  } else {\n    canvasStore.getCanvas().showSearchBox(e)\n  }\n}\n\nconst getFirstLink = () =>\n  canvasStore.getCanvas().linkConnector.renderLinks.at(0)\n\nconst nodeDefStore = useNodeDefStore()\nconst showNewSearchBox = (e: CanvasPointerEvent) => {\n  const firstLink = getFirstLink()\n  if (firstLink) {\n    const filter =\n      firstLink.toType === 'input'\n        ? nodeDefStore.nodeSearchService.inputTypeFilter\n        : nodeDefStore.nodeSearchService.outputTypeFilter\n\n    const dataType = firstLink.fromSlot.type?.toString() ?? ''\n    addFilter({\n      filterDef: filter,\n      value: dataType\n    })\n  }\n\n  visible.value = true\n  triggerEvent = e\n\n  // Prevent the dialog from being dismissed immediately\n  dismissable.value = false\n  setTimeout(() => {\n    dismissable.value = true\n  }, 300)\n}\n\nconst showContextMenu = (e: CanvasPointerEvent) => {\n  const firstLink = getFirstLink()\n  if (!firstLink) return\n\n  const { node, fromSlot, toType } = firstLink\n  const commonOptions = {\n    e,\n    allow_searchbox: true,\n    showSearchBox: () => {\n      cancelResetOnContextClose()\n      showSearchBox(e)\n    }\n  }\n  const connectionOptions =\n    toType === 'input'\n      ? { nodeFrom: node, slotFrom: fromSlot }\n      : { nodeTo: node, slotTo: fromSlot }\n\n  const canvas = canvasStore.getCanvas()\n  const menu = canvas.showConnectionMenu({\n    ...connectionOptions,\n    ...commonOptions\n  })\n\n  if (!menu) {\n    console.warn('No menu was returned from showConnectionMenu')\n    return\n  }\n\n  triggerEvent = e\n  listenerController = new AbortController()\n  const { signal } = listenerController\n  const options = { once: true, signal }\n\n  // Connect the node after it is created via context menu\n  useEventListener(\n    canvas.canvas,\n    'connect-new-default-node',\n    (createEvent) => {\n      if (!(createEvent instanceof CustomEvent))\n        throw new Error('Invalid event')\n\n      const node: unknown = createEvent.detail?.node\n      if (!(node instanceof LGraphNode)) throw new Error('Invalid node')\n\n      disconnectOnReset = false\n      createEvent.preventDefault()\n      canvas.linkConnector.connectToNode(node, e)\n    },\n    options\n  )\n\n  // Reset when the context menu is closed\n  const cancelResetOnContextClose = useEventListener(\n    menu.controller.signal,\n    'abort',\n    reset,\n    options\n  )\n}\n\n// Disable litegraph's default behavior of release link and search box.\nwatchEffect(() => {\n  const { canvas } = canvasStore\n  if (!canvas) return\n\n  LiteGraph.release_link_on_empty_shows_menu = false\n  canvas.allow_searchbox = false\n\n  useEventListener(\n    canvas.linkConnector.events,\n    'dropped-on-canvas',\n    handleDroppedOnCanvas\n  )\n})\n\nconst canvasEventHandler = (e: LiteGraphCanvasEvent) => {\n  if (e.detail.subType === 'empty-double-click') {\n    showSearchBox(e.detail.originalEvent)\n  } else if (e.detail.subType === 'group-double-click') {\n    const group = e.detail.group\n    const [_, y] = group.pos\n    const relativeY = e.detail.originalEvent.canvasY - y\n    // Show search box if the click is NOT on the title bar\n    if (relativeY > group.titleHeight) {\n      showSearchBox(e.detail.originalEvent)\n    }\n  }\n}\n\nconst linkReleaseAction = computed(() =>\n  settingStore.get('Comfy.LinkRelease.Action')\n)\n\nconst linkReleaseActionShift = computed(() =>\n  settingStore.get('Comfy.LinkRelease.ActionShift')\n)\n\n// Prevent normal LinkConnector reset (called by CanvasPointer.finally)\nconst preventDefault = (e: Event) => e.preventDefault()\nconst cancelNextReset = (e: CustomEvent<CanvasPointerEvent>) => {\n  e.preventDefault()\n\n  const canvas = canvasStore.getCanvas()\n  canvas.linkConnector.state.snapLinksPos = [e.detail.canvasX, e.detail.canvasY]\n  useEventListener(canvas.linkConnector.events, 'reset', preventDefault, {\n    once: true\n  })\n}\n\nconst handleDroppedOnCanvas = (e: CustomEvent<CanvasPointerEvent>) => {\n  disconnectOnReset = true\n  const action = e.detail.shiftKey\n    ? linkReleaseActionShift.value\n    : linkReleaseAction.value\n  switch (action) {\n    case LinkReleaseTriggerAction.SEARCH_BOX:\n      cancelNextReset(e)\n      showSearchBox(e.detail)\n      break\n    case LinkReleaseTriggerAction.CONTEXT_MENU:\n      cancelNextReset(e)\n      showContextMenu(e.detail)\n      break\n    case LinkReleaseTriggerAction.NO_ACTION:\n    default:\n      break\n  }\n}\n\n// Resets litegraph state\nconst reset = () => {\n  listenerController?.abort()\n  listenerController = null\n  triggerEvent = null\n\n  const canvas = canvasStore.getCanvas()\n  canvas.linkConnector.events.removeEventListener('reset', preventDefault)\n  if (disconnectOnReset) canvas.linkConnector.disconnectLinks()\n\n  canvas.linkConnector.reset()\n  canvas.setDirty(true, true)\n}\n\n// Reset connecting links when the search box is closed\nwatch(visible, () => {\n  if (!visible.value) reset()\n})\n\nuseEventListener(document, 'litegraph:canvas', canvasEventHandler)\n</script>\n\n<style>\n.invisible-dialog-root {\n  width: 60%;\n  min-width: 24rem;\n  max-width: 48rem;\n  border: 0 !important;\n  background-color: transparent !important;\n  margin-top: 25vh;\n  margin-left: 400px;\n}\n@media all and (max-width: 768px) {\n  .invisible-dialog-root {\n    margin-left: 0;\n  }\n}\n\n.node-search-box-dialog-mask {\n  align-items: flex-start !important;\n}\n</style>\n", "<template>\n  <Button\n    v-tooltip=\"{ value: tooltip, showDelay: 300, hideDelay: 300 }\"\n    text\n    :pt=\"{\n      root: {\n        class: `side-bar-button ${\n          selected\n            ? 'p-button-primary side-bar-button-selected'\n            : 'p-button-secondary'\n        }`,\n        'aria-label': tooltip\n      }\n    }\"\n    @click=\"emit('click', $event)\"\n  >\n    <template #icon>\n      <OverlayBadge v-if=\"shouldShowBadge\" :value=\"overlayValue\">\n        <i :class=\"icon + ' side-bar-button-icon'\" />\n      </OverlayBadge>\n      <i v-else :class=\"icon + ' side-bar-button-icon'\" />\n    </template>\n  </Button>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport OverlayBadge from 'primevue/overlaybadge'\nimport { computed } from 'vue'\n\nconst {\n  icon = '',\n  selected = false,\n  tooltip = '',\n  iconBadge = ''\n} = defineProps<{\n  icon?: string\n  selected?: boolean\n  tooltip?: string\n  iconBadge?: string | (() => string | null)\n}>()\n\nconst emit = defineEmits<{\n  (e: 'click', event: MouseEvent): void\n}>()\nconst overlayValue = computed(() =>\n  typeof iconBadge === 'function' ? iconBadge() ?? '' : iconBadge\n)\nconst shouldShowBadge = computed(() => !!overlayValue.value)\n</script>\n\n<style>\n.side-bar-button-icon {\n  font-size: var(--sidebar-icon-size) !important;\n}\n\n.side-bar-button-selected .side-bar-button-icon {\n  font-size: var(--sidebar-icon-size) !important;\n  font-weight: bold;\n}\n</style>\n\n<style scoped>\n.side-bar-button {\n  width: var(--sidebar-width);\n  height: var(--sidebar-width);\n  border-radius: 0;\n}\n\n.comfyui-body-left .side-bar-button.side-bar-button-selected,\n.comfyui-body-left .side-bar-button.side-bar-button-selected:hover {\n  border-left: 4px solid var(--p-button-text-primary-color);\n}\n\n.comfyui-body-right .side-bar-button.side-bar-button-selected,\n.comfyui-body-right .side-bar-button.side-bar-button-selected:hover {\n  border-right: 4px solid var(--p-button-text-primary-color);\n}\n</style>\n", "<template>\n  <SidebarIcon icon=\"pi pi-sign-out\" :tooltip=\"tooltip\" @click=\"logout\" />\n</template>\n\n<script setup lang=\"ts\">\nimport { computed } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { useUserStore } from '@/stores/userStore'\n\nimport SidebarIcon from './SidebarIcon.vue'\n\nconst { t } = useI18n()\nconst userStore = useUserStore()\n\nconst tooltip = computed(\n  () => `${t('sideToolbar.logout')} (${userStore.currentUser?.username})`\n)\nconst logout = async () => {\n  await userStore.logout()\n  window.location.reload()\n}\n</script>\n", "<template>\n  <SidebarIcon\n    icon=\"pi pi-cog\"\n    class=\"comfy-settings-btn\"\n    :tooltip=\"$t('g.settings')\"\n    @click=\"showSetting\"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport SettingDialogContent from '@/components/dialog/content/SettingDialogContent.vue'\nimport SettingDialogHeader from '@/components/dialog/header/SettingDialogHeader.vue'\nimport { useDialogStore } from '@/stores/dialogStore'\n\nimport SidebarIcon from './SidebarIcon.vue'\n\nconst dialogStore = useDialogStore()\nconst showSetting = () => {\n  dialogStore.showDialog({\n    key: 'global-settings',\n    headerComponent: SettingDialogHeader,\n    component: SettingDialogContent\n  })\n}\n</script>\n", "<template>\n  <SidebarIcon\n    :icon=\"icon\"\n    :tooltip=\"$t('sideToolbar.themeToggle')\"\n    class=\"comfy-vue-theme-toggle\"\n    @click=\"toggleTheme\"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport { computed } from 'vue'\n\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useColorPaletteStore } from '@/stores/workspace/colorPaletteStore'\n\nimport SidebarIcon from './SidebarIcon.vue'\n\nconst colorPaletteStore = useColorPaletteStore()\nconst icon = computed(() =>\n  colorPaletteStore.completedActivePalette.light_theme\n    ? 'pi pi-sun'\n    : 'pi pi-moon'\n)\n\nconst commandStore = useCommandStore()\nconst toggleTheme = async () => {\n  await commandStore.execute('Comfy.ToggleTheme')\n}\n</script>\n", "<template>\n  <teleport :to=\"teleportTarget\">\n    <nav class=\"side-tool-bar-container\" :class=\"{ 'small-sidebar': isSmall }\">\n      <SidebarIcon\n        v-for=\"tab in tabs\"\n        :key=\"tab.id\"\n        :icon=\"tab.icon\"\n        :icon-badge=\"tab.iconBadge\"\n        :tooltip=\"tab.tooltip + getTabTooltipSuffix(tab)\"\n        :selected=\"tab.id === selectedTab?.id\"\n        :class=\"tab.id + '-tab-button'\"\n        @click=\"onTabClick(tab)\"\n      />\n      <div class=\"side-tool-bar-end\">\n        <SidebarLogoutIcon v-if=\"userStore.isMultiUserServer\" />\n        <SidebarThemeToggleIcon />\n        <SidebarSettingsToggleIcon />\n      </div>\n    </nav>\n  </teleport>\n  <div\n    v-if=\"selectedTab\"\n    class=\"sidebar-content-container h-full overflow-y-auto overflow-x-hidden\"\n  >\n    <ExtensionSlot :extension=\"selectedTab\" />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed } from 'vue'\n\nimport ExtensionSlot from '@/components/common/ExtensionSlot.vue'\nimport { useKeybindingStore } from '@/stores/keybindingStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useUserStore } from '@/stores/userStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\nimport type { SidebarTabExtension } from '@/types/extensionTypes'\n\nimport SidebarIcon from './SidebarIcon.vue'\nimport SidebarLogoutIcon from './SidebarLogoutIcon.vue'\nimport SidebarSettingsToggleIcon from './SidebarSettingsToggleIcon.vue'\nimport SidebarThemeToggleIcon from './SidebarThemeToggleIcon.vue'\n\nconst workspaceStore = useWorkspaceStore()\nconst settingStore = useSettingStore()\nconst userStore = useUserStore()\n\nconst teleportTarget = computed(() =>\n  settingStore.get('Comfy.Sidebar.Location') === 'left'\n    ? '.comfyui-body-left'\n    : '.comfyui-body-right'\n)\n\nconst isSmall = computed(\n  () => settingStore.get('Comfy.Sidebar.Size') === 'small'\n)\n\nconst tabs = computed(() => workspaceStore.getSidebarTabs())\nconst selectedTab = computed(() => workspaceStore.sidebarTab.activeSidebarTab)\nconst onTabClick = (item: SidebarTabExtension) => {\n  workspaceStore.sidebarTab.toggleSidebarTab(item.id)\n}\nconst keybindingStore = useKeybindingStore()\nconst getTabTooltipSuffix = (tab: SidebarTabExtension) => {\n  const keybinding = keybindingStore.getKeybindingByCommandId(\n    `Workspace.ToggleSidebarTab.${tab.id}`\n  )\n  return keybinding ? ` (${keybinding.combo.toString()})` : ''\n}\n</script>\n\n<style scoped>\n.side-tool-bar-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n\n  width: var(--sidebar-width);\n  height: 100%;\n\n  background-color: var(--comfy-menu-secondary-bg);\n  color: var(--fg-color);\n  box-shadow: var(--bar-shadow);\n\n  --sidebar-width: 4rem;\n  --sidebar-icon-size: 1.5rem;\n}\n\n.side-tool-bar-container.small-sidebar {\n  --sidebar-width: 2.5rem;\n  --sidebar-icon-size: 1rem;\n}\n\n.side-tool-bar-end {\n  align-self: flex-end;\n  margin-top: auto;\n}\n</style>\n", "<template>\n  <div ref=\"workflowTabRef\" class=\"flex p-2 gap-2 workflow-tab\" v-bind=\"$attrs\">\n    <span\n      v-tooltip.bottom=\"workflowOption.workflow.key\"\n      class=\"workflow-label text-sm max-w-[150px] truncate inline-block\"\n    >\n      {{ workflowOption.workflow.filename }}\n    </span>\n    <div class=\"relative\">\n      <span v-if=\"shouldShowStatusIndicator\" class=\"status-indicator\">•</span>\n      <Button\n        class=\"close-button p-0 w-auto\"\n        icon=\"pi pi-times\"\n        text\n        severity=\"secondary\"\n        size=\"small\"\n        @click.stop=\"onCloseWorkflow(workflowOption)\"\n      />\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport { computed, ref } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport {\n  usePragmaticDraggable,\n  usePragmaticDroppable\n} from '@/composables/usePragmaticDragAndDrop'\nimport { useWorkflowService } from '@/services/workflowService'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { ComfyWorkflow } from '@/stores/workflowStore'\nimport { useWorkflowStore } from '@/stores/workflowStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\n\ninterface WorkflowOption {\n  value: string\n  workflow: ComfyWorkflow\n}\n\nconst props = defineProps<{\n  class?: string\n  workflowOption: WorkflowOption\n}>()\n\nconst { t } = useI18n()\n\nconst workspaceStore = useWorkspaceStore()\nconst workflowStore = useWorkflowStore()\nconst settingStore = useSettingStore()\nconst workflowTabRef = ref<HTMLElement | null>(null)\n\n// Use computed refs to cache autosave settings\nconst autoSaveSetting = computed(() =>\n  settingStore.get('Comfy.Workflow.AutoSave')\n)\nconst autoSaveDelay = computed(() =>\n  settingStore.get('Comfy.Workflow.AutoSaveDelay')\n)\n\nconst shouldShowStatusIndicator = computed(() => {\n  if (workspaceStore.shiftDown) {\n    // Branch 1: Shift key is held down, do not show the status indicator.\n    return false\n  }\n  if (!props.workflowOption.workflow.isPersisted) {\n    // Branch 2: Workflow is not persisted, show the status indicator.\n    return true\n  }\n  if (props.workflowOption.workflow.isModified) {\n    // Branch 3: Workflow is modified.\n    if (autoSaveSetting.value === 'off') {\n      // Sub-branch 3a: Autosave is off, so show the status indicator.\n      return true\n    }\n    if (autoSaveSetting.value === 'after delay' && autoSaveDelay.value > 3000) {\n      // Sub-branch 3b: Autosave delay is too high, so show the status indicator.\n      return true\n    }\n    // Sub-branch 3c: Workflow is modified but no condition applies, do not show the status indicator.\n    return false\n  }\n  // Default: do not show the status indicator. This should not be reachable.\n  return false\n})\n\nconst closeWorkflows = async (options: WorkflowOption[]) => {\n  for (const opt of options) {\n    if (\n      !(await useWorkflowService().closeWorkflow(opt.workflow, {\n        warnIfUnsaved: !workspaceStore.shiftDown,\n        hint: t('sideToolbar.workflowTab.dirtyCloseHint')\n      }))\n    ) {\n      // User clicked cancel\n      break\n    }\n  }\n}\n\nconst onCloseWorkflow = async (option: WorkflowOption) => {\n  await closeWorkflows([option])\n}\nconst tabGetter = () => workflowTabRef.value as HTMLElement\n\nusePragmaticDraggable(tabGetter, {\n  getInitialData: () => {\n    return {\n      workflowKey: props.workflowOption.workflow.key\n    }\n  }\n})\n\nusePragmaticDroppable(tabGetter, {\n  getData: () => {\n    return {\n      workflowKey: props.workflowOption.workflow.key\n    }\n  },\n  onDrop: (e) => {\n    const fromIndex = workflowStore.openWorkflows.findIndex(\n      (wf) => wf.key === e.source.data.workflowKey\n    )\n    const toIndex = workflowStore.openWorkflows.findIndex(\n      (wf) => wf.key === e.location.current.dropTargets[0]?.data.workflowKey\n    )\n    if (fromIndex !== toIndex) {\n      workflowStore.reorderWorkflows(fromIndex, toIndex)\n    }\n  }\n})\n</script>\n\n<style scoped>\n.status-indicator {\n  @apply absolute font-bold;\n  font-size: 1.5rem;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n</style>\n", "<template>\n  <div class=\"workflow-tabs-container flex flex-row max-w-full h-full\">\n    <ScrollPanel\n      ref=\"scrollPanelRef\"\n      class=\"overflow-hidden no-drag\"\n      :pt:content=\"{\n        class: 'p-0 w-full',\n        onwheel: handleWheel\n      }\"\n      pt:bar-x=\"h-1\"\n    >\n      <SelectButton\n        class=\"workflow-tabs bg-transparent\"\n        :class=\"props.class\"\n        :model-value=\"selectedWorkflow\"\n        :options=\"options\"\n        option-label=\"label\"\n        data-key=\"value\"\n        @update:model-value=\"onWorkflowChange\"\n      >\n        <template #option=\"{ option }\">\n          <WorkflowTab\n            :workflow-option=\"option\"\n            @contextmenu=\"showContextMenu($event, option)\"\n            @click.middle=\"onCloseWorkflow(option)\"\n          />\n        </template>\n      </SelectButton>\n    </ScrollPanel>\n    <Button\n      v-tooltip=\"{ value: $t('sideToolbar.newBlankWorkflow'), showDelay: 300 }\"\n      class=\"new-blank-workflow-button flex-shrink-0 no-drag\"\n      icon=\"pi pi-plus\"\n      text\n      severity=\"secondary\"\n      :aria-label=\"$t('sideToolbar.newBlankWorkflow')\"\n      @click=\"() => commandStore.execute('Comfy.NewBlankWorkflow')\"\n    />\n    <ContextMenu ref=\"menu\" :model=\"contextMenuItems\" />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport ContextMenu from 'primevue/contextmenu'\nimport ScrollPanel from 'primevue/scrollpanel'\nimport SelectButton from 'primevue/selectbutton'\nimport { computed, nextTick, ref, watch } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport WorkflowTab from '@/components/topbar/WorkflowTab.vue'\nimport { useWorkflowService } from '@/services/workflowService'\nimport { useCommandStore } from '@/stores/commandStore'\nimport { ComfyWorkflow, useWorkflowBookmarkStore } from '@/stores/workflowStore'\nimport { useWorkflowStore } from '@/stores/workflowStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\n\ninterface WorkflowOption {\n  value: string\n  workflow: ComfyWorkflow\n}\n\nconst props = defineProps<{\n  class?: string\n}>()\n\nconst { t } = useI18n()\nconst workspaceStore = useWorkspaceStore()\nconst workflowStore = useWorkflowStore()\nconst workflowService = useWorkflowService()\nconst workflowBookmarkStore = useWorkflowBookmarkStore()\nconst rightClickedTab = ref<WorkflowOption | undefined>()\nconst menu = ref()\nconst scrollPanelRef = ref()\n\nconst workflowToOption = (workflow: ComfyWorkflow): WorkflowOption => ({\n  value: workflow.path,\n  workflow\n})\n\nconst options = computed<WorkflowOption[]>(() =>\n  workflowStore.openWorkflows.map(workflowToOption)\n)\nconst selectedWorkflow = computed<WorkflowOption | null>(() =>\n  workflowStore.activeWorkflow\n    ? workflowToOption(workflowStore.activeWorkflow as ComfyWorkflow)\n    : null\n)\nconst onWorkflowChange = async (option: WorkflowOption) => {\n  // Prevent unselecting the current workflow\n  if (!option) {\n    return\n  }\n  // Prevent reloading the current workflow\n  if (selectedWorkflow.value?.value === option.value) {\n    return\n  }\n\n  await workflowService.openWorkflow(option.workflow)\n}\n\nconst closeWorkflows = async (options: WorkflowOption[]) => {\n  for (const opt of options) {\n    if (\n      !(await workflowService.closeWorkflow(opt.workflow, {\n        warnIfUnsaved: !workspaceStore.shiftDown\n      }))\n    ) {\n      // User clicked cancel\n      break\n    }\n  }\n}\n\nconst onCloseWorkflow = async (option: WorkflowOption) => {\n  await closeWorkflows([option])\n}\n\nconst showContextMenu = (event: MouseEvent, option: WorkflowOption) => {\n  rightClickedTab.value = option\n  menu.value.show(event)\n}\nconst contextMenuItems = computed(() => {\n  const tab = rightClickedTab.value as WorkflowOption\n  if (!tab) return []\n  const index = options.value.findIndex((v) => v.workflow === tab.workflow)\n\n  return [\n    {\n      label: t('tabMenu.duplicateTab'),\n      command: async () => {\n        await workflowService.duplicateWorkflow(tab.workflow)\n      }\n    },\n    {\n      separator: true\n    },\n    {\n      label: t('tabMenu.closeTab'),\n      command: () => onCloseWorkflow(tab)\n    },\n    {\n      label: t('tabMenu.closeTabsToLeft'),\n      command: () => closeWorkflows(options.value.slice(0, index)),\n      disabled: index <= 0\n    },\n    {\n      label: t('tabMenu.closeTabsToRight'),\n      command: () => closeWorkflows(options.value.slice(index + 1)),\n      disabled: index === options.value.length - 1\n    },\n    {\n      label: t('tabMenu.closeOtherTabs'),\n      command: () =>\n        closeWorkflows([\n          ...options.value.slice(index + 1),\n          ...options.value.slice(0, index)\n        ]),\n      disabled: options.value.length <= 1\n    },\n    {\n      label: workflowBookmarkStore.isBookmarked(tab.workflow.path)\n        ? t('tabMenu.removeFromBookmarks')\n        : t('tabMenu.addToBookmarks'),\n      command: () => workflowBookmarkStore.toggleBookmarked(tab.workflow.path),\n      disabled: tab.workflow.isTemporary\n    }\n  ]\n})\nconst commandStore = useCommandStore()\n\n// Horizontal scroll on wheel\nconst handleWheel = (event: WheelEvent) => {\n  const scrollElement = event.currentTarget as HTMLElement\n  const scrollAmount = event.deltaX || event.deltaY\n  scrollElement.scroll({\n    left: scrollElement.scrollLeft + scrollAmount\n  })\n}\n\n// Scroll to active offscreen tab when opened\nwatch(\n  () => workflowStore.activeWorkflow,\n  async () => {\n    if (!selectedWorkflow.value) return\n\n    await nextTick()\n\n    const activeTabElement = document.querySelector('.p-togglebutton-checked')\n    if (!activeTabElement || !scrollPanelRef.value) return\n\n    const container = scrollPanelRef.value.$el.querySelector(\n      '.p-scrollpanel-content'\n    )\n    if (!container) return\n\n    const tabRect = activeTabElement.getBoundingClientRect()\n    const containerRect = container.getBoundingClientRect()\n\n    const offsetLeft = tabRect.left - containerRect.left\n    const offsetRight = tabRect.right - containerRect.right\n\n    if (offsetRight > 0) {\n      container.scrollBy({ left: offsetRight })\n    } else if (offsetLeft < 0) {\n      container.scrollBy({ left: offsetLeft })\n    }\n  },\n  { immediate: true }\n)\n</script>\n\n<style scoped>\n:deep(.p-togglebutton) {\n  @apply p-0 bg-transparent rounded-none flex-shrink-0 relative border-0 border-r border-solid;\n  border-right-color: var(--border-color);\n}\n\n:deep(.p-togglebutton::before) {\n  @apply hidden;\n}\n\n:deep(.p-togglebutton:first-child) {\n  @apply border-l border-solid;\n  border-left-color: var(--border-color);\n}\n\n:deep(.p-togglebutton:not(:first-child)) {\n  @apply border-l-0;\n}\n\n:deep(.p-togglebutton.p-togglebutton-checked) {\n  @apply border-b border-solid h-full;\n  border-bottom-color: var(--p-button-text-primary-color);\n}\n\n:deep(.p-togglebutton:not(.p-togglebutton-checked)) {\n  @apply opacity-75;\n}\n\n:deep(.p-togglebutton-checked) .close-button,\n:deep(.p-togglebutton:hover) .close-button {\n  @apply visible;\n}\n\n:deep(.p-togglebutton:hover) .status-indicator {\n  @apply hidden;\n}\n\n:deep(.p-togglebutton) .close-button {\n  @apply invisible;\n}\n\n:deep(.p-scrollpanel-content) {\n  @apply h-full;\n}\n\n/* Scrollbar half opacity to avoid blocking the active tab bottom border */\n:deep(.p-scrollpanel:hover .p-scrollpanel-bar),\n:deep(.p-scrollpanel:active .p-scrollpanel-bar) {\n  @apply opacity-50;\n}\n\n:deep(.p-selectbutton) {\n  @apply rounded-none h-full;\n}\n</style>\n", "import type { LGraphNode } from '@comfyorg/litegraph'\n\nimport { ApiNodeCostRecord } from '@/types/apiNodeTypes'\n\nconst apiNodeCosts: ApiNodeCostRecord = {\n  FluxProCannyNode: {\n    vendor: 'BFL',\n    nodeName: 'Flux 1: Canny Control Image',\n    pricingParams: '-',\n    pricePerRunRange: '$0.05',\n    displayPrice: '$0.05/Run'\n  },\n  FluxProDepthNode: {\n    vendor: 'BFL',\n    nodeName: 'Flux 1: Depth Control Image',\n    pricingParams: '-',\n    pricePerRunRange: '$0.05',\n    displayPrice: '$0.05/Run'\n  },\n  FluxProExpandNode: {\n    vendor: 'BFL',\n    nodeName: 'Flux 1: Expand Image',\n    pricingParams: '-',\n    pricePerRunRange: '$0.05',\n    rateDocumentationUrl: 'https://docs.bfl.ml/pricing/',\n    displayPrice: '$0.05/Run'\n  },\n  FluxProFillNode: {\n    vendor: 'BFL',\n    nodeName: 'Flux 1: Fill Image',\n    pricingParams: '-',\n    pricePerRunRange: '$0.05',\n    displayPrice: '$0.05/Run'\n  },\n  FluxProUltraImageNode: {\n    vendor: 'BFL',\n    nodeName: 'Flux 1.1: [pro] Ultra Image',\n    pricingParams: '-',\n    pricePerRunRange: '$0.06',\n    displayPrice: '$0.06/Run'\n  },\n  IdeogramV1: {\n    vendor: 'Ideogram',\n    nodeName: 'Ideogram V1',\n    pricingParams: '-',\n    pricePerRunRange: '$0.06',\n    rateDocumentationUrl: 'https://about.ideogram.ai/api-pricing',\n    displayPrice: '$0.06/Run'\n  },\n  IdeogramV2: {\n    vendor: 'Ideogram',\n    nodeName: 'Ideogram V2',\n    pricingParams: '-',\n    pricePerRunRange: '$0.08',\n    displayPrice: '$0.08/Run'\n  },\n  IdeogramV3: {\n    vendor: 'Ideogram',\n    nodeName: 'Ideogram V3',\n    pricingParams: 'rendering_speed',\n    pricePerRunRange: 'dynamic',\n    displayPrice: 'Variable pricing (low to medium)'\n  },\n  KlingCameraControlI2VNode: {\n    vendor: 'Kling',\n    nodeName: 'Kling Image to Video (Camera Control)',\n    pricingParams: '-',\n    pricePerRunRange: '$0.49',\n    displayPrice: '$0.49/Run'\n  },\n  KlingCameraControlT2VNode: {\n    vendor: 'Kling',\n    nodeName: 'Kling Text to Video (Camera Control)',\n    pricingParams: '-',\n    pricePerRunRange: '$0.14',\n    displayPrice: '$0.14/Run'\n  },\n  KlingDualCharacterVideoEffectNode: {\n    vendor: 'Kling',\n    nodeName: 'Kling Dual Character Video Effects',\n    pricingParams: 'Priced the same as t2v based on mode, model, and duration.',\n    pricePerRunRange: 'dynamic',\n    displayPrice: 'Variable pricing (medium)'\n  },\n  KlingImage2VideoNode: {\n    vendor: 'Kling',\n    nodeName: 'Kling Image to Video',\n    pricingParams: 'Same as Text to Video',\n    pricePerRunRange: 'dynamic',\n    displayPrice: 'Variable pricing (medium)'\n  },\n  KlingImageGenerationNode: {\n    vendor: 'Kling',\n    nodeName: 'Kling Image Generation',\n    pricingParams: 'modality | model',\n    pricePerRunRange: 'dynamic',\n    displayPrice: 'Variable pricing (low)'\n  },\n  KlingLipSyncAudioToVideoNode: {\n    vendor: 'Kling',\n    nodeName: 'Kling Lip Sync Video with Audio',\n    pricingParams: 'duration of input video',\n    pricePerRunRange: '$0.07',\n    displayPrice: '$0.07/Run'\n  },\n  KlingLipSyncTextToVideoNode: {\n    vendor: 'Kling',\n    nodeName: 'Kling Lip Sync Video with Text',\n    pricingParams: 'duration of input video',\n    pricePerRunRange: '$0.07',\n    displayPrice: '$0.07/Run'\n  },\n  KlingSingleImageVideoEffectNode: {\n    vendor: 'Kling',\n    nodeName: 'Kling Video Effects',\n    pricingParams: 'effect_scene',\n    pricePerRunRange: 'dynamic',\n    displayPrice: 'Variable pricing (medium)'\n  },\n  KlingStartEndFrameNode: {\n    vendor: 'Kling',\n    nodeName: 'Kling Start-End Frame to Video',\n    pricingParams: 'Same as text to video',\n    pricePerRunRange: 'dynamic',\n    displayPrice: 'Variable pricing (medium)'\n  },\n  KlingTextToVideoNode: {\n    vendor: 'Kling',\n    nodeName: 'Kling Text to Video',\n    pricingParams: 'model | duration | mode',\n    pricePerRunRange: 'dynamic',\n    displayPrice: 'Variable pricing (medium to high)'\n  },\n  KlingVideoExtendNode: {\n    vendor: 'Kling',\n    nodeName: 'Kling Video Extend',\n    pricingParams: '-',\n    pricePerRunRange: '$0.28',\n    displayPrice: '$0.28/Run'\n  },\n  KlingVirtualTryOnNode: {\n    vendor: 'Kling',\n    nodeName: 'Kling Virtual Try On',\n    pricingParams: '-',\n    pricePerRunRange: '$0.07',\n    displayPrice: '$0.07/Run'\n  },\n  LumaImageToVideoNode: {\n    vendor: 'Luma',\n    nodeName: 'Luma Image to Video',\n    pricingParams: 'Same as Text to Video',\n    pricePerRunRange: 'dynamic',\n    rateDocumentationUrl: 'https://lumalabs.ai/api/pricing',\n    displayPrice: 'Variable pricing (medium to high)'\n  },\n  LumaVideoNode: {\n    vendor: 'Luma',\n    nodeName: 'Luma Text to Video',\n    pricingParams: 'model | resolution | duration',\n    pricePerRunRange: 'dynamic',\n    rateDocumentationUrl: 'https://lumalabs.ai/api/pricing',\n    displayPrice: 'Variable pricing (medium to high)'\n  },\n  MinimaxImageToVideoNode: {\n    vendor: 'Minimax',\n    nodeName: 'MiniMax Image to Video',\n    pricingParams: '-',\n    pricePerRunRange: '$0.43',\n    rateDocumentationUrl: 'https://www.minimax.io/price',\n    displayPrice: '$0.43/Run'\n  },\n  MinimaxTextToVideoNode: {\n    vendor: 'Minimax',\n    nodeName: 'MiniMax Text to Video',\n    pricingParams: '-',\n    pricePerRunRange: '$0.43',\n    rateDocumentationUrl: 'https://www.minimax.io/price',\n    displayPrice: '$0.43/Run'\n  },\n  OpenAIDalle2: {\n    vendor: 'OpenAI',\n    nodeName: 'dall-e-2',\n    pricingParams: 'size',\n    pricePerRunRange: 'dynamic',\n    rateDocumentationUrl: 'https://platform.openai.com/docs/pricing',\n    displayPrice: 'Variable pricing (low)'\n  },\n  OpenAIDalle3: {\n    vendor: 'OpenAI',\n    nodeName: 'dall-e-3',\n    pricingParams: 'size | quality',\n    pricePerRunRange: 'dynamic',\n    rateDocumentationUrl: 'https://platform.openai.com/docs/pricing',\n    displayPrice: 'Variable pricing (medium)'\n  },\n  OpenAIGPTImage1: {\n    vendor: 'OpenAI',\n    nodeName: 'gpt-image-1',\n    pricingParams: 'quality',\n    pricePerRunRange: 'dynamic',\n    rateDocumentationUrl: 'https://platform.openai.com/docs/pricing',\n    displayPrice: 'Variable pricing (low to high)'\n  },\n  PikaImageToVideoNode2_2: {\n    vendor: 'Pika',\n    nodeName: 'Pika Image to Video',\n    pricingParams: 'duration | resolution',\n    pricePerRunRange: 'dynamic',\n    displayPrice: 'Variable pricing (medium)'\n  },\n  PikaScenesV2_2: {\n    vendor: 'Pika',\n    nodeName: 'Pika Scenes (Video Image Composition)',\n    pricingParams: 'duration | resolution',\n    pricePerRunRange: 'dynamic',\n    displayPrice: 'Variable pricing (medium)'\n  },\n  PikaStartEndFrameNode2_2: {\n    vendor: 'Pika',\n    nodeName: 'Pika Start and End Frame to Video',\n    pricingParams: 'duration | resolution',\n    pricePerRunRange: 'dynamic',\n    displayPrice: 'Variable pricing (medium)'\n  },\n  PikaTextToVideoNode2_2: {\n    vendor: 'Pika',\n    nodeName: 'Pika Text to Video',\n    pricingParams: 'duration | resolution',\n    pricePerRunRange: 'dynamic',\n    displayPrice: 'Variable pricing (medium)'\n  },\n  Pikadditions: {\n    vendor: 'Pika',\n    nodeName: 'Pikadditions (Video Object Insertion)',\n    pricingParams: '-',\n    pricePerRunRange: '$0.3',\n    displayPrice: '$0.3/Run'\n  },\n  Pikaffects: {\n    vendor: 'Pika',\n    nodeName: 'Pikaffects (Video Effects)',\n    pricingParams: '-',\n    pricePerRunRange: '$0.45',\n    displayPrice: '$0.45/Run'\n  },\n  Pikaswaps: {\n    vendor: 'Pika',\n    nodeName: 'Pika Swaps (Video Object Replacement)',\n    pricingParams: '-',\n    pricePerRunRange: '$0.3',\n    displayPrice: '$0.3/Run'\n  },\n  PixverseImageToVideoNode: {\n    vendor: 'Pixverse',\n    nodeName: 'PixVerse Image to Video',\n    pricingParams: 'same as text to video',\n    pricePerRunRange: '$0.9',\n    displayPrice: '$0.9/Run'\n  },\n  PixverseTextToVideoNode: {\n    vendor: 'Pixverse',\n    nodeName: 'PixVerse Text to Video',\n    pricingParams: 'duration | quality | motion_mode',\n    pricePerRunRange: 'dynamic',\n    displayPrice: 'Variable pricing (medium to high)'\n  },\n  PixverseTransitionVideoNode: {\n    vendor: 'Pixverse',\n    nodeName: 'PixVerse Transition Video',\n    pricingParams: 'same as text to video',\n    pricePerRunRange: '$0.9',\n    displayPrice: '$0.9/Run'\n  },\n  RecraftCreativeUpscaleNode: {\n    vendor: 'Recraft',\n    nodeName: 'Recraft Creative Upscale Image',\n    pricingParams: '-',\n    pricePerRunRange: '$0.25',\n    rateDocumentationUrl: 'https://www.recraft.ai/docs#pricing',\n    displayPrice: '$0.25/Run'\n  },\n  RecraftCrispUpscaleNode: {\n    vendor: 'Recraft',\n    nodeName: 'Recraft Crisp Upscale Image',\n    pricingParams: '-',\n    pricePerRunRange: '$0.004',\n    rateDocumentationUrl: 'https://www.recraft.ai/docs#pricing',\n    displayPrice: '$0.004/Run'\n  },\n  RecraftImageInpaintingNode: {\n    vendor: 'Recraft',\n    nodeName: 'Recraft Image Inpainting',\n    pricingParams: 'n',\n    pricePerRunRange: '$$0.04 x n',\n    rateDocumentationUrl: 'https://www.recraft.ai/docs#pricing',\n    displayPrice: '$0.04 x n/Run'\n  },\n  RecraftImageToImageNode: {\n    vendor: 'Recraft',\n    nodeName: 'Recraft Image to Image',\n    pricingParams: 'n',\n    pricePerRunRange: '$0.04 x n',\n    rateDocumentationUrl: 'https://www.recraft.ai/docs#pricing',\n    displayPrice: '$0.04 x n/Run'\n  },\n  RecraftRemoveBackgroundNode: {\n    vendor: 'Recraft',\n    nodeName: 'Recraft Remove Background',\n    pricingParams: '-',\n    pricePerRunRange: '$0.01',\n    rateDocumentationUrl: 'https://www.recraft.ai/docs#pricing',\n    displayPrice: '$0.01/Run'\n  },\n  RecraftReplaceBackgroundNode: {\n    vendor: 'Recraft',\n    nodeName: 'Recraft Replace Background',\n    pricingParams: 'n',\n    pricePerRunRange: '$0.04',\n    rateDocumentationUrl: 'https://www.recraft.ai/docs#pricing',\n    displayPrice: '$0.04/Run'\n  },\n  RecraftTextToImageNode: {\n    vendor: 'Recraft',\n    nodeName: 'Recraft Text to Image',\n    pricingParams: 'model | n',\n    pricePerRunRange: '$0.04 x n',\n    rateDocumentationUrl: 'https://www.recraft.ai/docs#pricing',\n    displayPrice: '$0.04 x n/Run'\n  },\n  RecraftTextToVectorNode: {\n    vendor: 'Recraft',\n    nodeName: 'Recraft Text to Vector',\n    pricingParams: 'model | n',\n    pricePerRunRange: '$0.08 x n',\n    rateDocumentationUrl: 'https://www.recraft.ai/docs#pricing',\n    displayPrice: '$0.08 x n/Run'\n  },\n  RecraftVectorizeImageNode: {\n    vendor: 'Recraft',\n    nodeName: 'Recraft Vectorize Image',\n    pricingParams: '-',\n    pricePerRunRange: '$0.01',\n    rateDocumentationUrl: 'https://www.recraft.ai/docs#pricing',\n    displayPrice: '$0.01/Run'\n  },\n  StabilityStableImageSD_3_5Node: {\n    vendor: 'Stability',\n    nodeName: 'Stability AI Stable Diffusion 3.5 Image',\n    pricingParams: 'model',\n    pricePerRunRange: 'dynamic',\n    displayPrice: 'Variable pricing (low)'\n  },\n  StabilityStableImageUltraNode: {\n    vendor: 'Stability',\n    nodeName: 'Stability AI Stable Image Ultra',\n    pricingParams: '-',\n    pricePerRunRange: '$0.08',\n    displayPrice: '$0.08/Run'\n  },\n  StabilityUpscaleConservativeNode: {\n    vendor: 'Stability',\n    nodeName: 'Stability AI Upscale Conservative',\n    pricingParams: '-',\n    pricePerRunRange: '$0.25',\n    displayPrice: '$0.25/Run'\n  },\n  StabilityUpscaleCreativeNode: {\n    vendor: 'Stability',\n    nodeName: 'Stability AI Upscale Creative',\n    pricingParams: '-',\n    pricePerRunRange: '$0.25',\n    displayPrice: '$0.25/Run'\n  },\n  StabilityUpscaleFastNode: {\n    vendor: 'Stability',\n    nodeName: 'Stability AI Upscale Fast',\n    pricingParams: '-',\n    pricePerRunRange: '$0.01',\n    displayPrice: '$0.01/Run'\n  },\n  VeoVideoGenerationNode: {\n    vendor: 'Veo',\n    nodeName: 'Google Veo2 Video Generation',\n    pricingParams: 'duration_seconds',\n    pricePerRunRange: 'dynamic',\n    rateDocumentationUrl:\n      'https://cloud.google.com/vertex-ai/generative-ai/pricing',\n    displayPrice: 'Variable pricing (high)'\n  },\n  LumaTextToImageNode: {\n    vendor: 'Luma',\n    nodeName: 'Luma Text to Image',\n    pricingParams: 'model | aspect_ratio',\n    pricePerRunRange: 'dynamic',\n    rateDocumentationUrl: 'https://lumalabs.ai/api/pricing',\n    displayPrice: 'Variable pricing (low to medium)'\n  },\n  LumaImageToImageNode: {\n    vendor: 'Luma',\n    nodeName: 'Luma Image to Image',\n    pricingParams: 'Same as Text to Image',\n    pricePerRunRange: 'dynamic',\n    rateDocumentationUrl: 'https://lumalabs.ai/api/pricing',\n    displayPrice: 'Variable pricing (low to medium)'\n  }\n}\n\n/**\n * Composable to get node pricing information for API nodes\n */\nexport const useNodePricing = () => {\n  const getNodePrice = (nodeName: string): string =>\n    apiNodeCosts[nodeName]?.displayPrice || ''\n\n  /**\n   * Get the price display for a node\n   */\n  const getNodeDisplayPrice = (node: LGraphNode): string => {\n    if (!node.constructor.nodeData?.api_node) return ''\n    return getNodePrice(node.constructor.nodeData.name)\n  }\n\n  return {\n    getNodeDisplayPrice\n  }\n}\n", "import {\n  BadgePosition,\n  LGraphBadge,\n  type LGraphNode\n} from '@comfyorg/litegraph'\nimport _ from 'lodash'\nimport { computed, onMounted, watch } from 'vue'\n\nimport { useNodePricing } from '@/composables/node/useNodePricing'\nimport { app } from '@/scripts/app'\nimport { useExtensionStore } from '@/stores/extensionStore'\nimport { ComfyNodeDefImpl, useNodeDefStore } from '@/stores/nodeDefStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useColorPaletteStore } from '@/stores/workspace/colorPaletteStore'\nimport { NodeBadgeMode } from '@/types/nodeSource'\nimport { adjustColor } from '@/utils/colorUtil'\n\n/**\n * Add LGraphBadge to LGraphNode based on settings.\n *\n * Following badges are added:\n * - Node ID badge\n * - Node source badge\n * - Node life cycle badge\n * - API node credits badge\n */\nexport const useNodeBadge = () => {\n  const settingStore = useSettingStore()\n  const extensionStore = useExtensionStore()\n  const colorPaletteStore = useColorPaletteStore()\n\n  const nodeSourceBadgeMode = computed(\n    () =>\n      settingStore.get('Comfy.NodeBadge.NodeSourceBadgeMode') as NodeBadgeMode\n  )\n  const nodeIdBadgeMode = computed(\n    () => settingStore.get('Comfy.NodeBadge.NodeIdBadgeMode') as NodeBadgeMode\n  )\n  const nodeLifeCycleBadgeMode = computed(\n    () =>\n      settingStore.get(\n        'Comfy.NodeBadge.NodeLifeCycleBadgeMode'\n      ) as NodeBadgeMode\n  )\n\n  const showApiPricingBadge = computed(() =>\n    settingStore.get('Comfy.NodeBadge.ShowApiPricing')\n  )\n\n  watch(\n    [\n      nodeSourceBadgeMode,\n      nodeIdBadgeMode,\n      nodeLifeCycleBadgeMode,\n      showApiPricingBadge\n    ],\n    () => {\n      app.graph?.setDirtyCanvas(true, true)\n    }\n  )\n\n  const nodeDefStore = useNodeDefStore()\n  function badgeTextVisible(\n    nodeDef: ComfyNodeDefImpl | null,\n    badgeMode: NodeBadgeMode\n  ): boolean {\n    return !(\n      badgeMode === NodeBadgeMode.None ||\n      (nodeDef?.isCoreNode && badgeMode === NodeBadgeMode.HideBuiltIn)\n    )\n  }\n\n  onMounted(() => {\n    const nodePricing = useNodePricing()\n\n    extensionStore.registerExtension({\n      name: 'Comfy.NodeBadge',\n      nodeCreated(node: LGraphNode) {\n        node.badgePosition = BadgePosition.TopRight\n\n        const badge = computed(() => {\n          const nodeDef = nodeDefStore.fromLGraphNode(node)\n          return new LGraphBadge({\n            text: _.truncate(\n              [\n                badgeTextVisible(nodeDef, nodeIdBadgeMode.value)\n                  ? `#${node.id}`\n                  : '',\n                badgeTextVisible(nodeDef, nodeLifeCycleBadgeMode.value)\n                  ? nodeDef?.nodeLifeCycleBadgeText ?? ''\n                  : '',\n                badgeTextVisible(nodeDef, nodeSourceBadgeMode.value)\n                  ? nodeDef?.nodeSource?.badgeText ?? ''\n                  : ''\n              ]\n                .filter((s) => s.length > 0)\n                .join(' '),\n              {\n                length: 31\n              }\n            ),\n            fgColor:\n              colorPaletteStore.completedActivePalette.colors.litegraph_base\n                .BADGE_FG_COLOR,\n            bgColor:\n              colorPaletteStore.completedActivePalette.colors.litegraph_base\n                .BADGE_BG_COLOR\n          })\n        })\n\n        node.badges.push(() => badge.value)\n\n        if (node.constructor.nodeData?.api_node && showApiPricingBadge.value) {\n          const price = nodePricing.getNodeDisplayPrice(node)\n          // Always add the badge for API nodes, with or without price text\n          const creditsBadge = computed(() => {\n            // Use dynamic background color based on the theme\n            const isLightTheme =\n              colorPaletteStore.completedActivePalette.light_theme\n            return new LGraphBadge({\n              text: price,\n              iconOptions: {\n                unicode: '\\ue96b',\n                fontFamily: 'PrimeIcons',\n                color: isLightTheme\n                  ? adjustColor('#FABC25', { lightness: 0.5 })\n                  : '#FABC25',\n                bgColor: isLightTheme\n                  ? adjustColor('#654020', { lightness: 0.5 })\n                  : '#654020',\n                fontSize: 8\n              },\n              fgColor:\n                colorPaletteStore.completedActivePalette.colors.litegraph_base\n                  .BADGE_FG_COLOR,\n              bgColor: isLightTheme\n                ? adjustColor('#8D6932', { lightness: 0.5 })\n                : '#8D6932'\n            })\n          })\n\n          node.badges.push(() => creditsBadge.value)\n        }\n      }\n    })\n  })\n}\n", "import { LGraphNode } from '@comfyorg/litegraph'\nimport { LiteGraph } from '@comfyorg/litegraph'\nimport { Ref } from 'vue'\n\nimport { usePragmaticDroppable } from '@/composables/usePragmaticDragAndDrop'\nimport { app as comfyApp } from '@/scripts/app'\nimport { useLitegraphService } from '@/services/litegraphService'\nimport { useWorkflowService } from '@/services/workflowService'\nimport { ComfyModelDef } from '@/stores/modelStore'\nimport { ModelNodeProvider } from '@/stores/modelToNodeStore'\nimport { useModelToNodeStore } from '@/stores/modelToNodeStore'\nimport { ComfyNodeDefImpl } from '@/stores/nodeDefStore'\nimport { ComfyWorkflow } from '@/stores/workflowStore'\nimport { RenderedTreeExplorerNode } from '@/types/treeExplorerTypes'\n\nexport const useCanvasDrop = (canvasRef: Ref<HTMLCanvasElement>) => {\n  const modelToNodeStore = useModelToNodeStore()\n  const litegraphService = useLitegraphService()\n  const workflowService = useWorkflowService()\n\n  usePragmaticDroppable(() => canvasRef.value, {\n    getDropEffect: (args): Exclude<DataTransfer['dropEffect'], 'none'> =>\n      args.source.data.type === 'tree-explorer-node' ? 'copy' : 'move',\n    onDrop: async (event) => {\n      const loc = event.location.current.input\n      const dndData = event.source.data\n\n      if (dndData.type === 'tree-explorer-node') {\n        const node = dndData.data as RenderedTreeExplorerNode\n        if (node.data instanceof ComfyNodeDefImpl) {\n          const nodeDef = node.data\n          const pos = comfyApp.clientPosToCanvasPos([loc.clientX, loc.clientY])\n          // Add an offset on y to make sure after adding the node, the cursor\n          // is on the node (top left corner)\n          pos[1] += LiteGraph.NODE_TITLE_HEIGHT\n          litegraphService.addNodeOnGraph(nodeDef, { pos })\n        } else if (node.data instanceof ComfyModelDef) {\n          const model = node.data\n          const pos = comfyApp.clientPosToCanvasPos([loc.clientX, loc.clientY])\n          const nodeAtPos = comfyApp.graph.getNodeOnPos(pos[0], pos[1])\n          let targetProvider: ModelNodeProvider | null = null\n          let targetGraphNode: LGraphNode | null = null\n          if (nodeAtPos) {\n            const providers = modelToNodeStore.getAllNodeProviders(\n              model.directory\n            )\n            for (const provider of providers) {\n              if (provider.nodeDef.name === nodeAtPos.comfyClass) {\n                targetGraphNode = nodeAtPos\n                targetProvider = provider\n              }\n            }\n          }\n          if (!targetGraphNode) {\n            const provider = modelToNodeStore.getNodeProvider(model.directory)\n            if (provider) {\n              targetGraphNode = litegraphService.addNodeOnGraph(\n                provider.nodeDef,\n                {\n                  pos\n                }\n              )\n              targetProvider = provider\n            }\n          }\n          if (targetGraphNode) {\n            const widget = targetGraphNode.widgets?.find(\n              (widget) => widget.name === targetProvider?.key\n            )\n            if (widget) {\n              widget.value = model.file_name\n            }\n          }\n        } else if (node.data instanceof ComfyWorkflow) {\n          const workflow = node.data\n          const position = comfyApp.clientPosToCanvasPos([\n            loc.clientX,\n            loc.clientY\n          ])\n          await workflowService.insertWorkflow(workflow, { position })\n        }\n      }\n    }\n  })\n}\n", "import type {\n  IContextMenuOptions,\n  IContextMenuValue,\n  INodeInputSlot,\n  IWidget\n} from '@comfyorg/litegraph'\nimport { LGraphCanvas, LiteGraph } from '@comfyorg/litegraph'\n\nimport { st, te } from '@/i18n'\nimport { normalizeI18nKey } from '@/utils/formatUtil'\n\n/**\n * Add translation for litegraph context menu.\n */\nexport const useContextMenuTranslation = () => {\n  const f = LGraphCanvas.prototype.getCanvasMenuOptions\n  const getCanvasCenterMenuOptions = function (\n    this: LGraphCanvas,\n    ...args: Parameters<typeof f>\n  ) {\n    const res = f.apply(this, args) as ReturnType<typeof f>\n    for (const item of res) {\n      if (item?.content) {\n        item.content = st(`contextMenu.${item.content}`, item.content)\n      }\n    }\n    return res\n  }\n\n  LGraphCanvas.prototype.getCanvasMenuOptions = getCanvasCenterMenuOptions\n\n  function translateMenus(\n    values: readonly (IContextMenuValue | string | null)[] | undefined,\n    options: IContextMenuOptions\n  ) {\n    if (!values) return\n    const reInput = /Convert (.*) to input/\n    const reWidget = /Convert (.*) to widget/\n    const cvt = st('contextMenu.Convert ', 'Convert ')\n    const tinp = st('contextMenu. to input', ' to input')\n    const twgt = st('contextMenu. to widget', ' to widget')\n    for (const value of values) {\n      if (typeof value === 'string') continue\n\n      translateMenus(value?.submenu?.options, options)\n      if (!value?.content) {\n        continue\n      }\n      if (te(`contextMenu.${value.content}`)) {\n        value.content = st(`contextMenu.${value.content}`, value.content)\n      }\n\n      // for capture translation text of input and widget\n      const extraInfo: any = options.extra || options.parentMenu?.options?.extra\n      // widgets and inputs\n      const matchInput = value.content?.match(reInput)\n      if (matchInput) {\n        let match = matchInput[1]\n        extraInfo?.inputs?.find((i: INodeInputSlot) => {\n          if (i.name != match) return false\n          match = i.label ? i.label : i.name\n        })\n        extraInfo?.widgets?.find((i: IWidget) => {\n          if (i.name != match) return false\n          match = i.label ? i.label : i.name\n        })\n        value.content = cvt + match + tinp\n        continue\n      }\n      const matchWidget = value.content?.match(reWidget)\n      if (matchWidget) {\n        let match = matchWidget[1]\n        extraInfo?.inputs?.find((i: INodeInputSlot) => {\n          if (i.name != match) return false\n          match = i.label ? i.label : i.name\n        })\n        extraInfo?.widgets?.find((i: IWidget) => {\n          if (i.name != match) return false\n          match = i.label ? i.label : i.name\n        })\n        value.content = cvt + match + twgt\n        continue\n      }\n    }\n  }\n\n  const OriginalContextMenu = LiteGraph.ContextMenu\n  function ContextMenu(\n    values: (IContextMenuValue | string)[],\n    options: IContextMenuOptions\n  ) {\n    if (options.title) {\n      options.title = st(\n        `nodeDefs.${normalizeI18nKey(options.title)}.display_name`,\n        options.title\n      )\n    }\n    translateMenus(values, options)\n    const ctx = new OriginalContextMenu(values, options)\n    return ctx\n  }\n\n  LiteGraph.ContextMenu = ContextMenu as unknown as typeof LiteGraph.ContextMenu\n  LiteGraph.ContextMenu.prototype = OriginalContextMenu.prototype\n}\n", "import { useEventListener } from '@vueuse/core'\n\nimport { useCanvasStore } from '@/stores/graphStore'\n\n/**\n * Adds a handler on copy that serializes selected nodes to JSON\n */\nexport const useCopy = () => {\n  const canvasStore = useCanvasStore()\n\n  useEventListener(document, 'copy', (e) => {\n    if (!(e.target instanceof Element)) {\n      return\n    }\n    if (\n      (e.target instanceof HTMLTextAreaElement &&\n        e.target.type === 'textarea') ||\n      (e.target instanceof HTMLInputElement && e.target.type === 'text')\n    ) {\n      // Default system copy\n      return\n    }\n    const isTargetInGraph =\n      e.target.classList.contains('litegraph') ||\n      e.target.classList.contains('graph-canvas-container') ||\n      e.target.id === 'graph-canvas'\n\n    // copy nodes and clear clipboard\n    const canvas = canvasStore.canvas\n    if (isTargetInGraph && canvas?.selectedItems) {\n      canvas.copyToClipboard()\n      // clearData doesn't remove images from clipboard\n      e.clipboardData?.setData('text', ' ')\n      e.preventDefault()\n      e.stopImmediatePropagation()\n      return false\n    }\n  })\n}\n", "import {\n  ContextMenu,\n  DragAndScale,\n  LGraph,\n  LGraphBadge,\n  LGraphCanvas,\n  LGraphGroup,\n  LGraphNode,\n  LLink,\n  LiteGraph\n} from '@comfyorg/litegraph'\n\n/**\n * Assign all properties of LiteGraph to window to make it backward compatible.\n */\nexport const useGlobalLitegraph = () => {\n  // @ts-expect-error fixme ts strict error\n  window['LiteGraph'] = LiteGraph\n  // @ts-expect-error fixme ts strict error\n  window['LGraph'] = LGraph\n  // @ts-expect-error fixme ts strict error\n  window['LLink'] = LLink\n  // @ts-expect-error fixme ts strict error\n  window['LGraphNode'] = LGraphNode\n  // @ts-expect-error fixme ts strict error\n  window['LGraphGroup'] = LGraphGroup\n  // @ts-expect-error fixme ts strict error\n  window['DragAndScale'] = DragAndScale\n  // @ts-expect-error fixme ts strict error\n  window['LGraphCanvas'] = LGraphCanvas\n  // @ts-expect-error fixme ts strict error\n  window['ContextMenu'] = ContextMenu\n  // @ts-expect-error fixme ts strict error\n  window['LGraphBadge'] = LGraphBadge\n}\n", "import { Canvas<PERSON>ointer, LGraphNode, LiteGraph } from '@comfyorg/litegraph'\nimport { watchEffect } from 'vue'\n\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { useSettingStore } from '@/stores/settingStore'\n\n/**\n * Watch for changes in the setting store and update the LiteGraph settings accordingly.\n */\nexport const useLitegraphSettings = () => {\n  const settingStore = useSettingStore()\n  const canvasStore = useCanvasStore()\n\n  watchEffect(() => {\n    const canvasInfoEnabled = settingStore.get('Comfy.Graph.CanvasInfo')\n    if (canvasStore.canvas) {\n      canvasStore.canvas.show_info = canvasInfoEnabled\n    }\n  })\n\n  watchEffect(() => {\n    const zoomSpeed = settingStore.get('Comfy.Graph.ZoomSpeed')\n    if (canvasStore.canvas) {\n      canvasStore.canvas.zoom_speed = zoomSpeed\n    }\n  })\n\n  watchEffect(() => {\n    LiteGraph.snaps_for_comfy = settingStore.get(\n      'Comfy.Node.AutoSnapLinkToSlot'\n    )\n  })\n\n  watchEffect(() => {\n    LiteGraph.snap_highlights_node = settingStore.get(\n      'Comfy.Node.SnapHighlightsNode'\n    )\n  })\n\n  watchEffect(() => {\n    LGraphNode.keepAllLinksOnBypass = settingStore.get(\n      'Comfy.Node.BypassAllLinksOnDelete'\n    )\n  })\n\n  watchEffect(() => {\n    LiteGraph.middle_click_slot_add_default_node = settingStore.get(\n      'Comfy.Node.MiddleClickRerouteNode'\n    )\n  })\n\n  watchEffect(() => {\n    const linkRenderMode = settingStore.get('Comfy.LinkRenderMode')\n    if (canvasStore.canvas) {\n      canvasStore.canvas.links_render_mode = linkRenderMode\n      canvasStore.canvas.setDirty(/* fg */ false, /* bg */ true)\n    }\n  })\n\n  watchEffect(() => {\n    const lowQualityRenderingZoomThreshold = settingStore.get(\n      'LiteGraph.Canvas.LowQualityRenderingZoomThreshold'\n    )\n    if (canvasStore.canvas) {\n      canvasStore.canvas.low_quality_zoom_threshold =\n        lowQualityRenderingZoomThreshold\n      canvasStore.canvas.setDirty(/* fg */ true, /* bg */ true)\n    }\n  })\n\n  watchEffect(() => {\n    const linkMarkerShape = settingStore.get('Comfy.Graph.LinkMarkers')\n    const { canvas } = canvasStore\n    if (canvas) {\n      canvas.linkMarkerShape = linkMarkerShape\n      canvas.setDirty(false, true)\n    }\n  })\n\n  watchEffect(() => {\n    const maximumFps = settingStore.get('LiteGraph.Canvas.MaximumFps')\n    const { canvas } = canvasStore\n    if (canvas) canvas.maximumFps = maximumFps\n  })\n\n  watchEffect(() => {\n    const dragZoomEnabled = settingStore.get('Comfy.Graph.CtrlShiftZoom')\n    const { canvas } = canvasStore\n    if (canvas) canvas.dragZoomEnabled = dragZoomEnabled\n  })\n\n  watchEffect(() => {\n    CanvasPointer.doubleClickTime = settingStore.get(\n      'Comfy.Pointer.DoubleClickTime'\n    )\n  })\n\n  watchEffect(() => {\n    CanvasPointer.bufferTime = settingStore.get('Comfy.Pointer.ClickBufferTime')\n  })\n\n  watchEffect(() => {\n    CanvasPointer.maxClickDrift = settingStore.get('Comfy.Pointer.ClickDrift')\n  })\n\n  watchEffect(() => {\n    LiteGraph.CANVAS_GRID_SIZE = settingStore.get('Comfy.SnapToGrid.GridSize')\n  })\n\n  watchEffect(() => {\n    LiteGraph.alwaysSnapToGrid = settingStore.get('pysssss.SnapToGrid')\n  })\n\n  watchEffect(() => {\n    LiteGraph.context_menu_scaling = settingStore.get(\n      'LiteGraph.ContextMenu.Scaling'\n    )\n  })\n\n  watchEffect(() => {\n    LiteGraph.Reroute.maxSplineOffset = settingStore.get(\n      'LiteGraph.Reroute.SplineOffset'\n    )\n  })\n\n  watchEffect(() => {\n    LiteGraph.macTrackpadGestures = settingStore.get(\n      'LiteGraph.Pointer.TrackpadGestures'\n    )\n  })\n\n  watchEffect(() => {\n    LiteGraph.saveViewportWithGraph = settingStore.get(\n      'Comfy.EnableWorkflowViewRestore'\n    )\n  })\n}\n", "import { LiteGraph } from '@comfyorg/litegraph'\nimport type { LGraphNode } from '@comfyorg/litegraph'\nimport { useEventListener } from '@vueuse/core'\n\nimport { ComfyWorkflowJSON } from '@/schemas/comfyWorkflowSchema'\nimport { app } from '@/scripts/app'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\nimport { isAudioNode, isImageNode, isVideoNode } from '@/utils/litegraphUtil'\n\n/**\n * Adds a handler on paste that extracts and loads images or workflows from pasted JSON data\n */\nexport const usePaste = () => {\n  const workspaceStore = useWorkspaceStore()\n  const canvasStore = useCanvasStore()\n\n  const pasteItemsOnNode = (\n    items: DataTransferItemList,\n    node: LGraphNode | null,\n    contentType: string\n  ) => {\n    if (!node) return\n\n    const filteredItems = Array.from(items).filter((item) =>\n      item.type.startsWith(contentType)\n    )\n\n    const blob = filteredItems[0]?.getAsFile()\n    if (!blob) return\n\n    node.pasteFile?.(blob)\n    node.pasteFiles?.(\n      Array.from(filteredItems)\n        .map((i) => i.getAsFile())\n        .filter((f) => f !== null)\n    )\n  }\n\n  useEventListener(document, 'paste', async (e) => {\n    const isTargetInGraph =\n      e.target instanceof Element &&\n      (e.target.classList.contains('litegraph') ||\n        e.target.classList.contains('graph-canvas-container') ||\n        e.target.id === 'graph-canvas')\n\n    // If the target is not in the graph, we don't want to handle the paste event\n    if (!isTargetInGraph) return\n\n    // ctrl+shift+v is used to paste nodes with connections\n    // this is handled by litegraph\n    if (workspaceStore.shiftDown) return\n\n    const { canvas } = canvasStore\n    if (!canvas) return\n\n    const { graph } = canvas\n    let data: DataTransfer | string | null = e.clipboardData\n    if (!data) throw new Error('No clipboard data on clipboard event')\n\n    const { items } = data\n\n    const currentNode = canvas.current_node as LGraphNode\n    const isNodeSelected = currentNode?.is_selected\n\n    const isImageNodeSelected = isNodeSelected && isImageNode(currentNode)\n    const isVideoNodeSelected = isNodeSelected && isVideoNode(currentNode)\n    const isAudioNodeSelected = isNodeSelected && isAudioNode(currentNode)\n\n    let imageNode: LGraphNode | null = isImageNodeSelected ? currentNode : null\n    let audioNode: LGraphNode | null = isAudioNodeSelected ? currentNode : null\n    const videoNode: LGraphNode | null = isVideoNodeSelected\n      ? currentNode\n      : null\n\n    // Look for image paste data\n    for (const item of items) {\n      if (item.type.startsWith('image/')) {\n        if (!imageNode) {\n          // No image node selected: add a new one\n          const newNode = LiteGraph.createNode('LoadImage')\n          if (newNode) {\n            newNode.pos = [canvas.graph_mouse[0], canvas.graph_mouse[1]]\n            imageNode = graph?.add(newNode) ?? null\n          }\n          graph?.change()\n        }\n        pasteItemsOnNode(items, imageNode, 'image')\n        return\n      } else if (item.type.startsWith('video/')) {\n        if (!videoNode) {\n          // No video node selected: add a new one\n          // TODO: when video node exists\n        } else {\n          pasteItemsOnNode(items, videoNode, 'video')\n          return\n        }\n      } else if (item.type.startsWith('audio/')) {\n        if (!audioNode) {\n          // No audio node selected: add a new one\n          const newNode = LiteGraph.createNode('LoadAudio')\n          if (newNode) {\n            newNode.pos = [canvas.graph_mouse[0], canvas.graph_mouse[1]]\n            audioNode = graph?.add(newNode) ?? null\n          }\n          graph?.change()\n        }\n        pasteItemsOnNode(items, audioNode, 'audio')\n        return\n      }\n    }\n\n    // No image found. Look for node data\n    data = data.getData('text/plain')\n    let workflow: ComfyWorkflowJSON | null = null\n    try {\n      data = data.slice(data.indexOf('{'))\n      workflow = JSON.parse(data)\n    } catch (err) {\n      try {\n        data = data.slice(data.indexOf('workflow\\n'))\n        data = data.slice(data.indexOf('{'))\n        workflow = JSON.parse(data)\n      } catch (error) {\n        workflow = null\n      }\n    }\n\n    if (workflow && workflow.version && workflow.nodes && workflow.extra) {\n      await app.loadGraphData(workflow)\n    } else {\n      if (\n        (e.target instanceof HTMLTextAreaElement &&\n          e.target.type === 'textarea') ||\n        (e.target instanceof HTMLInputElement && e.target.type === 'text')\n      ) {\n        return\n      }\n\n      // Litegraph default paste\n      canvas.pasteFromClipboard()\n    }\n  })\n}\n", "import { computed, onUnmounted, watch } from 'vue'\n\nimport { api } from '@/scripts/api'\nimport { useWorkflowService } from '@/services/workflowService'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useWorkflowStore } from '@/stores/workflowStore'\n\nexport function useWorkflowAutoSave() {\n  const workflowStore = useWorkflowStore()\n  const settingStore = useSettingStore()\n  const workflowService = useWorkflowService()\n\n  // Use computed refs to cache autosave settings\n  const autoSaveSetting = computed(() =>\n    settingStore.get('Comfy.Workflow.AutoSave')\n  )\n  const autoSaveDelay = computed(() =>\n    settingStore.get('Comfy.Workflow.AutoSaveDelay')\n  )\n\n  let autoSaveTimeout: NodeJS.Timeout | null = null\n  let isSaving = false\n  let needsAutoSave = false\n\n  const scheduleAutoSave = () => {\n    // Clear any existing timeout\n    if (autoSaveTimeout) {\n      clearTimeout(autoSaveTimeout)\n      autoSaveTimeout = null\n    }\n\n    // If autosave is enabled\n    if (autoSaveSetting.value === 'after delay') {\n      // If a save is in progress, mark that we need an autosave after saving\n      if (isSaving) {\n        needsAutoSave = true\n        return\n      }\n      const delay = autoSaveDelay.value\n      autoSaveTimeout = setTimeout(async () => {\n        const activeWorkflow = workflowStore.activeWorkflow\n        if (activeWorkflow?.isModified && activeWorkflow.isPersisted) {\n          try {\n            isSaving = true\n            await workflowService.saveWorkflow(activeWorkflow)\n          } catch (err) {\n            console.error('Auto save failed:', err)\n          } finally {\n            isSaving = false\n            if (needsAutoSave) {\n              needsAutoSave = false\n              scheduleAutoSave()\n            }\n          }\n        }\n      }, delay)\n    }\n  }\n\n  // Watch for autosave setting changes\n  watch(\n    autoSaveSetting,\n    (newSetting) => {\n      // Clear any existing timeout when settings change\n      if (autoSaveTimeout) {\n        clearTimeout(autoSaveTimeout)\n        autoSaveTimeout = null\n      }\n\n      // If there's an active modified workflow and autosave is enabled, schedule a save\n      if (\n        newSetting === 'after delay' &&\n        workflowStore.activeWorkflow?.isModified\n      ) {\n        scheduleAutoSave()\n      }\n    },\n    { immediate: true }\n  )\n\n  // Listen for graph changes and schedule autosave when they occur\n  const onGraphChanged = () => {\n    scheduleAutoSave()\n  }\n\n  api.addEventListener('graphChanged', onGraphChanged)\n\n  onUnmounted(() => {\n    if (autoSaveTimeout) {\n      clearTimeout(autoSaveTimeout)\n      autoSaveTimeout = null\n    }\n    api.removeEventListener('graphChanged', onGraphChanged)\n  })\n}\n", "import { computed, watch } from 'vue'\n\nimport { api } from '@/scripts/api'\nimport { app as comfyApp } from '@/scripts/app'\nimport { getStorageValue, setStorageValue } from '@/scripts/utils'\nimport { useWorkflowService } from '@/services/workflowService'\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useWorkflowStore } from '@/stores/workflowStore'\n\nexport function useWorkflowPersistence() {\n  const workflowStore = useWorkflowStore()\n  const settingStore = useSettingStore()\n\n  const workflowPersistenceEnabled = computed(() =>\n    settingStore.get('Comfy.Workflow.Persist')\n  )\n\n  const persistCurrentWorkflow = () => {\n    if (!workflowPersistenceEnabled.value) return\n    const workflow = JSON.stringify(comfyApp.graph.serialize())\n    localStorage.setItem('workflow', workflow)\n    if (api.clientId) {\n      sessionStorage.setItem(`workflow:${api.clientId}`, workflow)\n    }\n  }\n\n  const loadWorkflowFromStorage = async (\n    json: string | null,\n    workflowName: string | null\n  ) => {\n    if (!json) return false\n    const workflow = JSON.parse(json)\n    await comfyApp.loadGraphData(workflow, true, true, workflowName)\n    return true\n  }\n\n  const loadPreviousWorkflowFromStorage = async () => {\n    const workflowName = getStorageValue('Comfy.PreviousWorkflow')\n    const clientId = api.initialClientId ?? api.clientId\n\n    // Try loading from session storage first\n    if (clientId) {\n      const sessionWorkflow = sessionStorage.getItem(`workflow:${clientId}`)\n      if (await loadWorkflowFromStorage(sessionWorkflow, workflowName)) {\n        return true\n      }\n    }\n\n    // Fall back to local storage\n    const localWorkflow = localStorage.getItem('workflow')\n    return await loadWorkflowFromStorage(localWorkflow, workflowName)\n  }\n\n  const loadDefaultWorkflow = async () => {\n    if (!settingStore.get('Comfy.TutorialCompleted')) {\n      await settingStore.set('Comfy.TutorialCompleted', true)\n      await useWorkflowService().loadBlankWorkflow()\n      await useCommandStore().execute('Comfy.BrowseTemplates')\n    } else {\n      await comfyApp.loadGraphData()\n    }\n  }\n\n  const restorePreviousWorkflow = async () => {\n    if (!workflowPersistenceEnabled.value) return\n    try {\n      const restored = await loadPreviousWorkflowFromStorage()\n      if (!restored) {\n        await loadDefaultWorkflow()\n      }\n    } catch (err) {\n      console.error('Error loading previous workflow', err)\n      await loadDefaultWorkflow()\n    }\n  }\n\n  // Setup watchers\n  watch(\n    () => workflowStore.activeWorkflow?.key,\n    (activeWorkflowKey) => {\n      if (!activeWorkflowKey) return\n      setStorageValue('Comfy.PreviousWorkflow', activeWorkflowKey)\n      // When the activeWorkflow changes, the graph has already been loaded.\n      // Saving the current state of the graph to the localStorage.\n      persistCurrentWorkflow()\n    }\n  )\n  api.addEventListener('graphChanged', persistCurrentWorkflow)\n\n  // Restore workflow tabs states\n  const openWorkflows = computed(() => workflowStore.openWorkflows)\n  const activeWorkflow = computed(() => workflowStore.activeWorkflow)\n  const restoreState = computed<{ paths: string[]; activeIndex: number }>(\n    () => {\n      if (!openWorkflows.value || !activeWorkflow.value) {\n        return { paths: [], activeIndex: -1 }\n      }\n\n      const paths = openWorkflows.value\n        .filter((workflow) => workflow?.isPersisted && !workflow.isModified)\n        .map((workflow) => workflow.path)\n      const activeIndex = openWorkflows.value.findIndex(\n        (workflow) => workflow.path === activeWorkflow.value?.path\n      )\n\n      return { paths, activeIndex }\n    }\n  )\n\n  // Get storage values before setting watchers\n  const storedWorkflows = JSON.parse(\n    getStorageValue('Comfy.OpenWorkflowsPaths') || '[]'\n  )\n  const storedActiveIndex = JSON.parse(\n    getStorageValue('Comfy.ActiveWorkflowIndex') || '-1'\n  )\n\n  watch(restoreState, ({ paths, activeIndex }) => {\n    if (workflowPersistenceEnabled.value) {\n      setStorageValue('Comfy.OpenWorkflowsPaths', JSON.stringify(paths))\n      setStorageValue('Comfy.ActiveWorkflowIndex', JSON.stringify(activeIndex))\n    }\n  })\n\n  const restoreWorkflowTabsState = () => {\n    if (!workflowPersistenceEnabled.value) return\n    const isRestorable = storedWorkflows?.length > 0 && storedActiveIndex >= 0\n    if (isRestorable) {\n      workflowStore.openWorkflowsInBackground({\n        left: storedWorkflows.slice(0, storedActiveIndex),\n        right: storedWorkflows.slice(storedActiveIndex)\n      })\n    }\n  }\n\n  return {\n    restorePreviousWorkflow,\n    restoreWorkflowTabsState\n  }\n}\n", "import { LinkMarkerShape, LiteGraph } from '@comfyorg/litegraph'\n\nimport type { ColorPalettes } from '@/schemas/colorPaletteSchema'\nimport type { Keybinding } from '@/schemas/keyBindingSchema'\nimport { NodeBadgeMode } from '@/types/nodeSource'\nimport { LinkReleaseTriggerAction } from '@/types/searchBoxTypes'\nimport type { SettingParams } from '@/types/settingTypes'\n\n/**\n * Core settings are essential configuration parameters required for ComfyUI's basic functionality.\n * These settings must be present in the settings store and cannot be omitted.\n *\n * IMPORTANT: To prevent ID conflicts, settings should be marked as deprecated rather than removed\n * when they are no longer needed.\n */\nexport const CORE_SETTINGS: SettingParams[] = [\n  {\n    id: 'Comfy.Validation.Workflows',\n    name: 'Validate workflows',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.NodeSearchBoxImpl',\n    category: ['Comfy', 'Node Search Box', 'Implementation'],\n    experimental: true,\n    name: 'Node search box implementation',\n    type: 'combo',\n    options: ['default', 'litegraph (legacy)'],\n    defaultValue: 'default'\n  },\n  {\n    id: 'Comfy.LinkRelease.Action',\n    category: ['LiteGraph', 'LinkRelease', 'Action'],\n    name: 'Action on link release (No modifier)',\n    type: 'combo',\n    options: Object.values(LinkReleaseTriggerAction),\n    defaultValue: LinkReleaseTriggerAction.CONTEXT_MENU\n  },\n  {\n    id: 'Comfy.LinkRelease.ActionShift',\n    category: ['LiteGraph', 'LinkRelease', 'ActionShift'],\n    name: 'Action on link release (Shift)',\n    type: 'combo',\n    options: Object.values(LinkReleaseTriggerAction),\n    defaultValue: LinkReleaseTriggerAction.SEARCH_BOX\n  },\n  {\n    id: 'Comfy.NodeSearchBoxImpl.NodePreview',\n    category: ['Comfy', 'Node Search Box', 'NodePreview'],\n    name: 'Node preview',\n    tooltip: 'Only applies to the default implementation',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.NodeSearchBoxImpl.ShowCategory',\n    category: ['Comfy', 'Node Search Box', 'ShowCategory'],\n    name: 'Show node category in search results',\n    tooltip: 'Only applies to the default implementation',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.NodeSearchBoxImpl.ShowIdName',\n    category: ['Comfy', 'Node Search Box', 'ShowIdName'],\n    name: 'Show node id name in search results',\n    tooltip: 'Only applies to the default implementation',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.NodeSearchBoxImpl.ShowNodeFrequency',\n    category: ['Comfy', 'Node Search Box', 'ShowNodeFrequency'],\n    name: 'Show node frequency in search results',\n    tooltip: 'Only applies to the default implementation',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.Sidebar.Location',\n    category: ['Appearance', 'Sidebar', 'Location'],\n    name: 'Sidebar location',\n    type: 'combo',\n    options: ['left', 'right'],\n    defaultValue: 'left'\n  },\n  {\n    id: 'Comfy.Sidebar.Size',\n    category: ['Appearance', 'Sidebar', 'Size'],\n    name: 'Sidebar size',\n    type: 'combo',\n    options: ['normal', 'small'],\n    // Default to small if the window is less than 1536px(2xl) wide.\n    defaultValue: () => (window.innerWidth < 1536 ? 'small' : 'normal')\n  },\n  {\n    id: 'Comfy.Sidebar.UnifiedWidth',\n    category: ['Appearance', 'Sidebar', 'UnifiedWidth'],\n    name: 'Unified sidebar width',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.18.1'\n  },\n  {\n    id: 'Comfy.TextareaWidget.FontSize',\n    category: ['Appearance', 'Node Widget', 'TextareaWidget', 'FontSize'],\n    name: 'Textarea widget font size',\n    type: 'slider',\n    defaultValue: 10,\n    attrs: {\n      min: 8,\n      max: 24\n    }\n  },\n  {\n    id: 'Comfy.TextareaWidget.Spellcheck',\n    category: ['Comfy', 'Node Widget', 'TextareaWidget', 'Spellcheck'],\n    name: 'Textarea widget spellcheck',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.Workflow.SortNodeIdOnSave',\n    name: 'Sort node IDs when saving workflow',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.Graph.CanvasInfo',\n    category: ['LiteGraph', 'Canvas', 'CanvasInfo'],\n    name: 'Show canvas info on bottom left corner (fps, etc.)',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.Node.ShowDeprecated',\n    name: 'Show deprecated nodes in search',\n    tooltip:\n      'Deprecated nodes are hidden by default in the UI, but remain functional in existing workflows that use them.',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.Node.ShowExperimental',\n    name: 'Show experimental nodes in search',\n    tooltip:\n      'Experimental nodes are marked as such in the UI and may be subject to significant changes or removal in future versions. Use with caution in production workflows',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.Node.Opacity',\n    category: ['Appearance', 'Node', 'Opacity'],\n    name: 'Node opacity',\n    type: 'slider',\n    defaultValue: 1,\n    attrs: {\n      min: 0.01,\n      max: 1,\n      step: 0.01\n    }\n  },\n  {\n    id: 'Comfy.Workflow.ShowMissingNodesWarning',\n    name: 'Show missing nodes warning',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.Workflow.ShowMissingModelsWarning',\n    name: 'Show missing models warning',\n    type: 'boolean',\n    defaultValue: true,\n    experimental: true\n  },\n  {\n    id: 'Comfy.Graph.ZoomSpeed',\n    category: ['LiteGraph', 'Canvas', 'ZoomSpeed'],\n    name: 'Canvas zoom speed',\n    type: 'slider',\n    defaultValue: 1.1,\n    attrs: {\n      min: 1.01,\n      max: 2.5,\n      step: 0.01\n    }\n  },\n  // Bookmarks are stored in the settings store.\n  // Bookmarks are in format of category/display_name. e.g. \"conditioning/CLIPTextEncode\"\n  {\n    id: 'Comfy.NodeLibrary.Bookmarks',\n    name: 'Node library bookmarks with display name (deprecated)',\n    type: 'hidden',\n    defaultValue: [],\n    deprecated: true\n  },\n  {\n    id: 'Comfy.NodeLibrary.Bookmarks.V2',\n    name: 'Node library bookmarks v2 with unique name',\n    type: 'hidden',\n    defaultValue: []\n  },\n  // Stores mapping from bookmark folder name to its customization.\n  {\n    id: 'Comfy.NodeLibrary.BookmarksCustomization',\n    name: 'Node library bookmarks customization',\n    type: 'hidden',\n    defaultValue: {}\n  },\n  // Hidden setting used by the queue for how to fit images\n  {\n    id: 'Comfy.Queue.ImageFit',\n    name: 'Queue image fit',\n    type: 'hidden',\n    defaultValue: 'cover'\n  },\n  {\n    id: 'Comfy.GroupSelectedNodes.Padding',\n    category: ['LiteGraph', 'Group', 'Padding'],\n    name: 'Group selected nodes padding',\n    type: 'slider',\n    defaultValue: 10,\n    attrs: {\n      min: 0,\n      max: 100\n    }\n  },\n  {\n    id: 'Comfy.Node.DoubleClickTitleToEdit',\n    category: ['LiteGraph', 'Node', 'DoubleClickTitleToEdit'],\n    name: 'Double click node title to edit',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.Node.AllowImageSizeDraw',\n    category: ['LiteGraph', 'Node Widget', 'AllowImageSizeDraw'],\n    name: 'Show width × height below the image preview',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.Group.DoubleClickTitleToEdit',\n    category: ['LiteGraph', 'Group', 'DoubleClickTitleToEdit'],\n    name: 'Double click group title to edit',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.Window.UnloadConfirmation',\n    name: 'Show confirmation when closing window',\n    type: 'boolean',\n    defaultValue: true,\n    versionModified: '1.7.12'\n  },\n  {\n    id: 'Comfy.TreeExplorer.ItemPadding',\n    category: ['Appearance', 'Tree Explorer', 'ItemPadding'],\n    name: 'Tree explorer item padding',\n    type: 'slider',\n    defaultValue: 2,\n    attrs: {\n      min: 0,\n      max: 8,\n      step: 1\n    }\n  },\n  {\n    id: 'Comfy.ModelLibrary.AutoLoadAll',\n    name: 'Automatically load all model folders',\n    tooltip:\n      'If true, all folders will load as soon as you open the model library (this may cause delays while it loads). If false, root level model folders will only load once you click on them.',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.ModelLibrary.NameFormat',\n    name: 'What name to display in the model library tree view',\n    tooltip:\n      'Select \"filename\" to render a simplified view of the raw filename (without directory or \".safetensors\" extension) in the model list. Select \"title\" to display the configurable model metadata title.',\n    type: 'combo',\n    options: ['filename', 'title'],\n    defaultValue: 'title'\n  },\n  {\n    id: 'Comfy.Locale',\n    name: 'Language',\n    type: 'combo',\n    options: [\n      { value: 'en', text: 'English' },\n      { value: 'zh', text: '中文' },\n      { value: 'ru', text: 'Русский' },\n      { value: 'ja', text: '日本語' },\n      { value: 'ko', text: '한국어' },\n      { value: 'fr', text: 'Français' },\n      { value: 'es', text: 'Español' }\n    ],\n    defaultValue: () => navigator.language.split('-')[0] || 'en'\n  },\n  {\n    id: 'Comfy.NodeBadge.NodeSourceBadgeMode',\n    category: ['LiteGraph', 'Node', 'NodeSourceBadgeMode'],\n    name: 'Node source badge mode',\n    type: 'combo',\n    options: Object.values(NodeBadgeMode),\n    defaultValue: NodeBadgeMode.HideBuiltIn\n  },\n  {\n    id: 'Comfy.NodeBadge.NodeIdBadgeMode',\n    category: ['LiteGraph', 'Node', 'NodeIdBadgeMode'],\n    name: 'Node ID badge mode',\n    type: 'combo',\n    options: [NodeBadgeMode.None, NodeBadgeMode.ShowAll],\n    defaultValue: NodeBadgeMode.None\n  },\n  {\n    id: 'Comfy.NodeBadge.NodeLifeCycleBadgeMode',\n    category: ['LiteGraph', 'Node', 'NodeLifeCycleBadgeMode'],\n    name: 'Node life cycle badge mode',\n    type: 'combo',\n    options: [NodeBadgeMode.None, NodeBadgeMode.ShowAll],\n    defaultValue: NodeBadgeMode.ShowAll\n  },\n  {\n    id: 'Comfy.NodeBadge.ShowApiPricing',\n    category: ['Comfy', 'API Nodes'],\n    name: 'Show API node pricing badge',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.20.3'\n  },\n  {\n    id: 'Comfy.ConfirmClear',\n    category: ['Comfy', 'Workflow', 'ConfirmClear'],\n    name: 'Require confirmation when clearing workflow',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.PromptFilename',\n    category: ['Comfy', 'Workflow', 'PromptFilename'],\n    name: 'Prompt for filename when saving workflow',\n    type: 'boolean',\n    defaultValue: true\n  },\n  /**\n   * file format for preview\n   *\n   * format;quality\n   *\n   * ex)\n   * webp;50 -> webp, quality 50\n   * jpeg;80 -> rgb, jpeg, quality 80\n   *\n   * @type {string}\n   */\n  {\n    id: 'Comfy.PreviewFormat',\n    category: ['LiteGraph', 'Node Widget', 'PreviewFormat'],\n    name: 'Preview image format',\n    tooltip:\n      'When displaying a preview in the image widget, convert it to a lightweight image, e.g. webp, jpeg, webp;50, etc.',\n    type: 'text',\n    defaultValue: ''\n  },\n  {\n    id: 'Comfy.DisableSliders',\n    category: ['LiteGraph', 'Node Widget', 'DisableSliders'],\n    name: 'Disable node widget sliders',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.DisableFloatRounding',\n    category: ['LiteGraph', 'Node Widget', 'DisableFloatRounding'],\n    name: 'Disable default float widget rounding.',\n    tooltip:\n      '(requires page reload) Cannot disable round when round is set by the node in the backend.',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'Comfy.FloatRoundingPrecision',\n    category: ['LiteGraph', 'Node Widget', 'FloatRoundingPrecision'],\n    name: 'Float widget rounding decimal places [0 = auto].',\n    tooltip: '(requires page reload)',\n    type: 'slider',\n    attrs: {\n      min: 0,\n      max: 6,\n      step: 1\n    },\n    defaultValue: 0\n  },\n  {\n    id: 'LiteGraph.Node.TooltipDelay',\n    name: 'Tooltip Delay',\n    type: 'number',\n    attrs: {\n      min: 100,\n      max: 3000,\n      step: 50\n    },\n    defaultValue: 500,\n    versionAdded: '1.9.0'\n  },\n  {\n    id: 'Comfy.EnableTooltips',\n    category: ['LiteGraph', 'Node', 'EnableTooltips'],\n    name: 'Enable Tooltips',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.DevMode',\n    name: 'Enable dev mode options (API save, etc.)',\n    type: 'boolean',\n    defaultValue: false,\n    onChange: (value) => {\n      const element = document.getElementById('comfy-dev-save-api-button')\n      if (element) {\n        element.style.display = value ? 'flex' : 'none'\n      }\n    }\n  },\n  {\n    id: 'Comfy.UseNewMenu',\n    category: ['Comfy', 'Menu', 'UseNewMenu'],\n    defaultValue: 'Top',\n    name: 'Use new menu',\n    type: 'combo',\n    options: ['Disabled', 'Top', 'Bottom'],\n    migrateDeprecatedValue: (value: string) => {\n      // Floating is now supported by dragging the docked actionbar off.\n      if (value === 'Floating') {\n        return 'Top'\n      }\n      return value\n    }\n  },\n  {\n    id: 'Comfy.Workflow.WorkflowTabsPosition',\n    name: 'Opened workflows position',\n    type: 'combo',\n    options: ['Sidebar', 'Topbar', 'Topbar (2nd-row)'],\n    // Default to topbar (2nd-row) if the window is less than 1536px(2xl) wide.\n    defaultValue: () =>\n      window.innerWidth < 1536 ? 'Topbar (2nd-row)' : 'Topbar'\n  },\n  {\n    id: 'Comfy.Graph.CanvasMenu',\n    category: ['LiteGraph', 'Canvas', 'CanvasMenu'],\n    name: 'Show graph canvas menu',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.QueueButton.BatchCountLimit',\n    name: 'Batch count limit',\n    tooltip:\n      'The maximum number of tasks added to the queue at one button click',\n    type: 'number',\n    defaultValue: 100,\n    versionAdded: '1.3.5'\n  },\n  {\n    id: 'Comfy.Keybinding.UnsetBindings',\n    name: 'Keybindings unset by the user',\n    type: 'hidden',\n    defaultValue: [] as Keybinding[],\n    versionAdded: '1.3.7',\n    versionModified: '1.7.3',\n    migrateDeprecatedValue: (value: any[]) => {\n      return value.map((keybinding) => {\n        if (keybinding['targetSelector'] === '#graph-canvas') {\n          keybinding['targetElementId'] = 'graph-canvas'\n        }\n        return keybinding\n      })\n    }\n  },\n  {\n    id: 'Comfy.Keybinding.NewBindings',\n    name: 'Keybindings set by the user',\n    type: 'hidden',\n    defaultValue: [] as Keybinding[],\n    versionAdded: '1.3.7'\n  },\n  {\n    id: 'Comfy.Extension.Disabled',\n    name: 'Disabled extension names',\n    type: 'hidden',\n    defaultValue: [] as string[],\n    versionAdded: '1.3.11'\n  },\n  {\n    id: 'Comfy.Validation.NodeDefs',\n    name: 'Validate node definitions (slow)',\n    type: 'boolean',\n    tooltip:\n      'Recommended for node developers. This will validate all node definitions on startup.',\n    defaultValue: false,\n    versionAdded: '1.3.14'\n  },\n  {\n    id: 'Comfy.LinkRenderMode',\n    category: ['LiteGraph', 'Graph', 'LinkRenderMode'],\n    name: 'Link Render Mode',\n    defaultValue: 2,\n    type: 'combo',\n    options: [\n      { value: LiteGraph.STRAIGHT_LINK, text: 'Straight' },\n      { value: LiteGraph.LINEAR_LINK, text: 'Linear' },\n      { value: LiteGraph.SPLINE_LINK, text: 'Spline' },\n      { value: LiteGraph.HIDDEN_LINK, text: 'Hidden' }\n    ]\n  },\n  {\n    id: 'Comfy.Node.AutoSnapLinkToSlot',\n    category: ['LiteGraph', 'Node', 'AutoSnapLinkToSlot'],\n    name: 'Auto snap link to node slot',\n    tooltip:\n      'When dragging a link over a node, the link automatically snap to a viable input slot on the node',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.3.29'\n  },\n  {\n    id: 'Comfy.Node.SnapHighlightsNode',\n    category: ['LiteGraph', 'Node', 'SnapHighlightsNode'],\n    name: 'Snap highlights node',\n    tooltip:\n      'When dragging a link over a node with viable input slot, highlight the node',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.3.29'\n  },\n  {\n    id: 'Comfy.Node.BypassAllLinksOnDelete',\n    category: ['LiteGraph', 'Node', 'BypassAllLinksOnDelete'],\n    name: 'Keep all links when deleting nodes',\n    tooltip:\n      'When deleting a node, attempt to reconnect all of its input and output links (bypassing the deleted node)',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.3.40'\n  },\n  {\n    id: 'Comfy.Node.MiddleClickRerouteNode',\n    category: ['LiteGraph', 'Node', 'MiddleClickRerouteNode'],\n    name: 'Middle-click creates a new Reroute node',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.3.42'\n  },\n  {\n    id: 'Comfy.Graph.LinkMarkers',\n    category: ['LiteGraph', 'Link', 'LinkMarkers'],\n    name: 'Link midpoint markers',\n    defaultValue: LinkMarkerShape.Circle,\n    type: 'combo',\n    options: [\n      { value: LinkMarkerShape.None, text: 'None' },\n      { value: LinkMarkerShape.Circle, text: 'Circle' },\n      { value: LinkMarkerShape.Arrow, text: 'Arrow' }\n    ],\n    versionAdded: '1.3.42'\n  },\n  {\n    id: 'Comfy.DOMClippingEnabled',\n    category: ['LiteGraph', 'Node', 'DOMClippingEnabled'],\n    name: 'Enable DOM element clipping (enabling may reduce performance)',\n    type: 'boolean',\n    defaultValue: true\n  },\n  {\n    id: 'Comfy.Graph.CtrlShiftZoom',\n    category: ['LiteGraph', 'Canvas', 'CtrlShiftZoom'],\n    name: 'Enable fast-zoom shortcut (Ctrl + Shift + Drag)',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.4.0'\n  },\n  {\n    id: 'Comfy.Pointer.ClickDrift',\n    category: ['LiteGraph', 'Pointer', 'ClickDrift'],\n    name: 'Pointer click drift (maximum distance)',\n    tooltip:\n      'If the pointer moves more than this distance while holding a button down, it is considered dragging (rather than clicking).\\n\\nHelps prevent objects from being unintentionally nudged if the pointer is moved whilst clicking.',\n    experimental: true,\n    type: 'slider',\n    attrs: {\n      min: 0,\n      max: 20,\n      step: 1\n    },\n    defaultValue: 6,\n    versionAdded: '1.4.3'\n  },\n  {\n    id: 'Comfy.Pointer.ClickBufferTime',\n    category: ['LiteGraph', 'Pointer', 'ClickBufferTime'],\n    name: 'Pointer click drift delay',\n    tooltip:\n      'After pressing a pointer button down, this is the maximum time (in milliseconds) that pointer movement can be ignored for.\\n\\nHelps prevent objects from being unintentionally nudged if the pointer is moved whilst clicking.',\n    experimental: true,\n    type: 'slider',\n    attrs: {\n      min: 0,\n      max: 1000,\n      step: 25\n    },\n    defaultValue: 150,\n    versionAdded: '1.4.3'\n  },\n  {\n    id: 'Comfy.Pointer.DoubleClickTime',\n    category: ['LiteGraph', 'Pointer', 'DoubleClickTime'],\n    name: 'Double click interval (maximum)',\n    tooltip:\n      'The maximum time in milliseconds between the two clicks of a double-click.  Increasing this value may assist if double-clicks are sometimes not registered.',\n    type: 'slider',\n    attrs: {\n      min: 100,\n      max: 1000,\n      step: 50\n    },\n    defaultValue: 300,\n    versionAdded: '1.4.3'\n  },\n  {\n    id: 'Comfy.SnapToGrid.GridSize',\n    category: ['LiteGraph', 'Canvas', 'GridSize'],\n    name: 'Snap to grid size',\n    type: 'slider',\n    attrs: {\n      min: 1,\n      max: 500\n    },\n    tooltip:\n      'When dragging and resizing nodes while holding shift they will be aligned to the grid, this controls the size of that grid.',\n    defaultValue: LiteGraph.CANVAS_GRID_SIZE\n  },\n  // Keep the 'pysssss.SnapToGrid' setting id so we don't need to migrate setting values.\n  // Using a new setting id can cause existing users to lose their existing settings.\n  {\n    id: 'pysssss.SnapToGrid',\n    category: ['LiteGraph', 'Canvas', 'AlwaysSnapToGrid'],\n    name: 'Always snap to grid',\n    type: 'boolean',\n    defaultValue: false,\n    versionAdded: '1.3.13'\n  },\n  {\n    id: 'Comfy.Server.ServerConfigValues',\n    name: 'Server config values for frontend display',\n    tooltip: 'Server config values used for frontend display only',\n    type: 'hidden',\n    // Mapping from server config id to value.\n    defaultValue: {} as Record<string, any>,\n    versionAdded: '1.4.8'\n  },\n  {\n    id: 'Comfy.Server.LaunchArgs',\n    name: 'Server launch arguments',\n    tooltip:\n      'These are the actual arguments that are passed to the server when it is launched.',\n    type: 'hidden',\n    defaultValue: {} as Record<string, string>,\n    versionAdded: '1.4.8'\n  },\n  {\n    id: 'Comfy.Queue.MaxHistoryItems',\n    name: 'Queue history size',\n    tooltip: 'The maximum number of tasks that show in the queue history.',\n    type: 'slider',\n    attrs: {\n      min: 2,\n      max: 256,\n      step: 2\n    },\n    defaultValue: 64,\n    versionAdded: '1.4.12'\n  },\n  {\n    id: 'LiteGraph.Canvas.MaximumFps',\n    name: 'Maximum FPS',\n    tooltip:\n      'The maximum frames per second that the canvas is allowed to render. Caps GPU usage at the cost of smoothness. If 0, the screen refresh rate is used. Default: 0',\n    type: 'slider',\n    attrs: {\n      min: 0,\n      max: 120\n    },\n    defaultValue: 0,\n    versionAdded: '1.5.1'\n  },\n  {\n    id: 'Comfy.EnableWorkflowViewRestore',\n    category: ['Comfy', 'Workflow', 'EnableWorkflowViewRestore'],\n    name: 'Save and restore canvas position and zoom level in workflows',\n    type: 'boolean',\n    defaultValue: true,\n    versionModified: '1.5.4'\n  },\n  {\n    id: 'Comfy.Workflow.ConfirmDelete',\n    name: 'Show confirmation when deleting workflows',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.5.6'\n  },\n  {\n    id: 'Comfy.ColorPalette',\n    name: 'The active color palette id',\n    type: 'hidden',\n    defaultValue: 'dark',\n    versionModified: '1.6.7',\n    migrateDeprecatedValue(value: string) {\n      // Legacy custom palettes were prefixed with 'custom_'\n      return value.startsWith('custom_') ? value.replace('custom_', '') : value\n    }\n  },\n  {\n    id: 'Comfy.CustomColorPalettes',\n    name: 'Custom color palettes',\n    type: 'hidden',\n    defaultValue: {} as ColorPalettes,\n    versionModified: '1.6.7'\n  },\n  {\n    id: 'Comfy.WidgetControlMode',\n    category: ['Comfy', 'Node Widget', 'WidgetControlMode'],\n    name: 'Widget control mode',\n    tooltip:\n      'Controls when widget values are updated (randomize/increment/decrement), either before the prompt is queued or after.',\n    type: 'combo',\n    defaultValue: 'after',\n    options: ['before', 'after'],\n    versionModified: '1.6.10'\n  },\n  {\n    id: 'Comfy.TutorialCompleted',\n    name: 'Tutorial completed',\n    type: 'hidden',\n    defaultValue: false,\n    versionAdded: '1.8.7'\n  },\n  {\n    id: 'LiteGraph.ContextMenu.Scaling',\n    name: 'Scale node combo widget menus (lists) when zoomed in',\n    defaultValue: false,\n    type: 'boolean',\n    versionAdded: '1.8.8'\n  },\n  {\n    id: 'LiteGraph.Canvas.LowQualityRenderingZoomThreshold',\n    name: 'Low quality rendering zoom threshold',\n    tooltip: 'Render low quality shapes when zoomed out',\n    type: 'slider',\n    attrs: {\n      min: 0.1,\n      max: 1,\n      step: 0.01\n    },\n    defaultValue: 0.6,\n    versionAdded: '1.9.1'\n  },\n  {\n    id: 'Comfy.Canvas.SelectionToolbox',\n    category: ['LiteGraph', 'Canvas', 'SelectionToolbox'],\n    name: 'Show selection toolbox',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.10.5'\n  },\n  {\n    id: 'LiteGraph.Reroute.SplineOffset',\n    name: 'Reroute spline offset',\n    tooltip: 'The bezier control point offset from the reroute centre point',\n    type: 'slider',\n    defaultValue: 20,\n    attrs: {\n      min: 0,\n      max: 400\n    },\n    versionAdded: '1.15.7'\n  },\n  {\n    id: 'Comfy.Toast.DisableReconnectingToast',\n    name: 'Disable toasts when reconnecting or reconnected',\n    type: 'hidden',\n    defaultValue: false,\n    versionAdded: '1.15.12'\n  },\n  {\n    id: 'Comfy.Workflow.AutoSaveDelay',\n    name: 'Auto Save Delay (ms)',\n    defaultValue: 1000,\n    type: 'number',\n    tooltip: 'Only applies if Auto Save is set to \"after delay\".',\n    versionAdded: '1.16.0'\n  },\n  {\n    id: 'Comfy.Workflow.AutoSave',\n    name: 'Auto Save',\n    type: 'combo',\n    options: ['off', 'after delay'], // Room for other options like on focus change, tab change, window change\n    defaultValue: 'off', // Popular requst by users (https://github.com/Comfy-Org/ComfyUI_frontend/issues/1584#issuecomment-2536610154)\n    versionAdded: '1.16.0'\n  },\n  {\n    id: 'Comfy.Workflow.Persist',\n    name: 'Persist workflow state and restore on page (re)load',\n    type: 'boolean',\n    defaultValue: true,\n    versionAdded: '1.16.1'\n  },\n  {\n    id: 'LiteGraph.Node.DefaultPadding',\n    name: 'Always shrink new nodes',\n    tooltip:\n      'Resize nodes to the smallest possible size when created. When disabled, a newly added node will be widened slightly to show widget values.',\n    type: 'boolean',\n    defaultValue: false,\n    versionAdded: '1.18.0'\n  },\n  {\n    id: 'LiteGraph.Pointer.TrackpadGestures',\n    category: ['LiteGraph', 'Pointer', 'Trackpad Gestures'],\n    experimental: true,\n    name: 'Enable trackpad gestures',\n    tooltip:\n      'This setting enables trackpad mode for the canvas, allowing pinch-to-zoom and panning with two fingers.',\n    type: 'boolean',\n    defaultValue: false,\n    versionAdded: '1.19.1'\n  }\n]\n", "<template>\n  <!-- Load splitter overlay only after comfyApp is ready. -->\n  <!-- If load immediately, the top-level splitter stateKey won't be correctly\n  synced with the stateStorage (localStorage). -->\n  <LiteGraphCanvasSplitterOverlay\n    v-if=\"comfyAppReady && betaMenuEnabled && !workspaceStore.focusMode\"\n  >\n    <template #side-bar-panel>\n      <SideToolbar />\n    </template>\n    <template #bottom-panel>\n      <BottomPanel />\n    </template>\n    <template #graph-canvas-panel>\n      <SecondRowWorkflowTabs\n        v-if=\"workflowTabsPosition === 'Topbar (2nd-row)'\"\n        class=\"pointer-events-auto\"\n      />\n      <GraphCanvasMenu v-if=\"canvasMenuEnabled\" class=\"pointer-events-auto\" />\n    </template>\n  </LiteGraphCanvasSplitterOverlay>\n  <GraphCanvasMenu v-if=\"!betaMenuEnabled && canvasMenuEnabled\" />\n  <canvas\n    id=\"graph-canvas\"\n    ref=\"canvasRef\"\n    tabindex=\"1\"\n    class=\"w-full h-full touch-none\"\n  />\n\n  <NodeTooltip v-if=\"tooltipEnabled\" />\n  <NodeSearchboxPopover />\n\n  <!-- Initialize components after comfyApp is ready. useAbsolutePosition requires\n  canvasStore.canvas to be initialized. -->\n  <template v-if=\"comfyAppReady\">\n    <TitleEditor />\n    <SelectionOverlay v-if=\"selectionToolboxEnabled\">\n      <SelectionToolbox />\n    </SelectionOverlay>\n    <DomWidgets />\n  </template>\n  <SubgraphBreadcrumb />\n</template>\n\n<script setup lang=\"ts\">\nimport type { LGraphNode } from '@comfyorg/litegraph'\nimport { useEventListener } from '@vueuse/core'\nimport { computed, onMounted, ref, watch, watchEffect } from 'vue'\n\nimport LiteGraphCanvasSplitterOverlay from '@/components/LiteGraphCanvasSplitterOverlay.vue'\nimport BottomPanel from '@/components/bottomPanel/BottomPanel.vue'\nimport SubgraphBreadcrumb from '@/components/breadcrumb/SubgraphBreadcrumb.vue'\nimport DomWidgets from '@/components/graph/DomWidgets.vue'\nimport GraphCanvasMenu from '@/components/graph/GraphCanvasMenu.vue'\nimport NodeTooltip from '@/components/graph/NodeTooltip.vue'\nimport SelectionOverlay from '@/components/graph/SelectionOverlay.vue'\nimport SelectionToolbox from '@/components/graph/SelectionToolbox.vue'\nimport TitleEditor from '@/components/graph/TitleEditor.vue'\nimport NodeSearchboxPopover from '@/components/searchbox/NodeSearchBoxPopover.vue'\nimport SideToolbar from '@/components/sidebar/SideToolbar.vue'\nimport SecondRowWorkflowTabs from '@/components/topbar/SecondRowWorkflowTabs.vue'\nimport { useChainCallback } from '@/composables/functional/useChainCallback'\nimport { useNodeBadge } from '@/composables/node/useNodeBadge'\nimport { useCanvasDrop } from '@/composables/useCanvasDrop'\nimport { useContextMenuTranslation } from '@/composables/useContextMenuTranslation'\nimport { useCopy } from '@/composables/useCopy'\nimport { useGlobalLitegraph } from '@/composables/useGlobalLitegraph'\nimport { useLitegraphSettings } from '@/composables/useLitegraphSettings'\nimport { usePaste } from '@/composables/usePaste'\nimport { useWorkflowAutoSave } from '@/composables/useWorkflowAutoSave'\nimport { useWorkflowPersistence } from '@/composables/useWorkflowPersistence'\nimport { CORE_SETTINGS } from '@/constants/coreSettings'\nimport { i18n, t } from '@/i18n'\nimport type { NodeId } from '@/schemas/comfyWorkflowSchema'\nimport { UnauthorizedError, api } from '@/scripts/api'\nimport { app as comfyApp } from '@/scripts/app'\nimport { ChangeTracker } from '@/scripts/changeTracker'\nimport { IS_CONTROL_WIDGET, updateControlWidgetLabel } from '@/scripts/widgets'\nimport { useColorPaletteService } from '@/services/colorPaletteService'\nimport { useWorkflowService } from '@/services/workflowService'\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useExecutionStore } from '@/stores/executionStore'\nimport { useCanvasStore } from '@/stores/graphStore'\nimport { useNodeDefStore } from '@/stores/nodeDefStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useToastStore } from '@/stores/toastStore'\nimport { useColorPaletteStore } from '@/stores/workspace/colorPaletteStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\n\nconst emit = defineEmits<{\n  ready: []\n}>()\nconst canvasRef = ref<HTMLCanvasElement | null>(null)\nconst settingStore = useSettingStore()\nconst nodeDefStore = useNodeDefStore()\nconst workspaceStore = useWorkspaceStore()\nconst canvasStore = useCanvasStore()\nconst executionStore = useExecutionStore()\nconst toastStore = useToastStore()\nconst betaMenuEnabled = computed(\n  () => settingStore.get('Comfy.UseNewMenu') !== 'Disabled'\n)\nconst workflowTabsPosition = computed(() =>\n  settingStore.get('Comfy.Workflow.WorkflowTabsPosition')\n)\nconst canvasMenuEnabled = computed(() =>\n  settingStore.get('Comfy.Graph.CanvasMenu')\n)\nconst tooltipEnabled = computed(() => settingStore.get('Comfy.EnableTooltips'))\nconst selectionToolboxEnabled = computed(() =>\n  settingStore.get('Comfy.Canvas.SelectionToolbox')\n)\n\nwatchEffect(() => {\n  nodeDefStore.showDeprecated = settingStore.get('Comfy.Node.ShowDeprecated')\n})\n\nwatchEffect(() => {\n  nodeDefStore.showExperimental = settingStore.get(\n    'Comfy.Node.ShowExperimental'\n  )\n})\n\nwatchEffect(() => {\n  const spellcheckEnabled = settingStore.get('Comfy.TextareaWidget.Spellcheck')\n  const textareas = document.querySelectorAll<HTMLTextAreaElement>(\n    'textarea.comfy-multiline-input'\n  )\n\n  textareas.forEach((textarea: HTMLTextAreaElement) => {\n    textarea.spellcheck = spellcheckEnabled\n    // Force recheck to ensure visual update\n    textarea.focus()\n    textarea.blur()\n  })\n})\n\nwatch(\n  () => settingStore.get('Comfy.WidgetControlMode'),\n  () => {\n    if (!canvasStore.canvas) return\n\n    for (const n of comfyApp.graph.nodes) {\n      if (!n.widgets) continue\n      for (const w of n.widgets) {\n        // @ts-expect-error fixme ts strict error\n        if (w[IS_CONTROL_WIDGET]) {\n          updateControlWidgetLabel(w)\n          if (w.linkedWidgets) {\n            for (const l of w.linkedWidgets) {\n              updateControlWidgetLabel(l)\n            }\n          }\n        }\n      }\n    }\n    comfyApp.graph.setDirtyCanvas(true)\n  }\n)\n\nconst colorPaletteService = useColorPaletteService()\nconst colorPaletteStore = useColorPaletteStore()\nwatch(\n  [() => canvasStore.canvas, () => settingStore.get('Comfy.ColorPalette')],\n  async ([canvas, currentPaletteId]) => {\n    if (!canvas) return\n\n    await colorPaletteService.loadColorPalette(currentPaletteId)\n  }\n)\nwatch(\n  () => colorPaletteStore.activePaletteId,\n  async (newValue) => {\n    await settingStore.set('Comfy.ColorPalette', newValue)\n  }\n)\n\n// Update the progress of the executing node\nwatch(\n  () =>\n    [executionStore.executingNodeId, executionStore.executingNodeProgress] as [\n      NodeId | null,\n      number | null\n    ],\n  ([executingNodeId, executingNodeProgress]) => {\n    for (const node of comfyApp.graph.nodes) {\n      if (node.id == executingNodeId) {\n        node.progress = executingNodeProgress ?? undefined\n      } else {\n        node.progress = undefined\n      }\n    }\n  }\n)\n\n// Update node slot errors\nwatch(\n  () => executionStore.lastNodeErrors,\n  (lastNodeErrors) => {\n    const removeSlotError = (node: LGraphNode) => {\n      for (const slot of node.inputs) {\n        delete slot.hasErrors\n      }\n      for (const slot of node.outputs) {\n        delete slot.hasErrors\n      }\n    }\n\n    for (const node of comfyApp.graph.nodes) {\n      removeSlotError(node)\n      const nodeErrors = lastNodeErrors?.[node.id]\n      if (!nodeErrors) continue\n      for (const error of nodeErrors.errors) {\n        if (error.extra_info && error.extra_info.input_name) {\n          const inputIndex = node.findInputSlot(error.extra_info.input_name)\n          if (inputIndex !== -1) {\n            node.inputs[inputIndex].hasErrors = true\n          }\n        }\n      }\n    }\n\n    comfyApp.canvas.draw(true, true)\n  }\n)\n\nuseEventListener(\n  canvasRef,\n  'litegraph:no-items-selected',\n  () => {\n    toastStore.add({\n      severity: 'warn',\n      summary: t('toastMessages.nothingSelected'),\n      life: 2000\n    })\n  },\n  { passive: true }\n)\n\nconst loadCustomNodesI18n = async () => {\n  try {\n    const i18nData = await api.getCustomNodesI18n()\n    Object.entries(i18nData).forEach(([locale, message]) => {\n      i18n.global.mergeLocaleMessage(locale, message)\n    })\n  } catch (error) {\n    console.error('Failed to load custom nodes i18n', error)\n  }\n}\n\nconst comfyAppReady = ref(false)\nconst workflowPersistence = useWorkflowPersistence()\n// @ts-expect-error fixme ts strict error\nuseCanvasDrop(canvasRef)\nuseLitegraphSettings()\nuseNodeBadge()\n\nonMounted(async () => {\n  useGlobalLitegraph()\n  useContextMenuTranslation()\n  useCopy()\n  usePaste()\n  useWorkflowAutoSave()\n\n  comfyApp.vueAppReady = true\n\n  workspaceStore.spinner = true\n  // ChangeTracker needs to be initialized before setup, as it will overwrite\n  // some listeners of litegraph canvas.\n  ChangeTracker.init()\n  await loadCustomNodesI18n()\n  try {\n    await settingStore.loadSettingValues()\n  } catch (error) {\n    if (error instanceof UnauthorizedError) {\n      console.log(\n        'Failed loading user settings, user unauthorized, cleaning local Comfy.userId'\n      )\n      localStorage.removeItem('Comfy.userId')\n      localStorage.removeItem('Comfy.userName')\n      window.location.reload()\n    } else {\n      throw error\n    }\n  }\n  CORE_SETTINGS.forEach((setting) => {\n    settingStore.addSetting(setting)\n  })\n  // @ts-expect-error fixme ts strict error\n  await comfyApp.setup(canvasRef.value)\n  canvasStore.canvas = comfyApp.canvas\n  canvasStore.canvas.render_canvas_border = false\n  workspaceStore.spinner = false\n\n  window.app = comfyApp\n  window.graph = comfyApp.graph\n\n  comfyAppReady.value = true\n\n  comfyApp.canvas.onSelectionChange = useChainCallback(\n    comfyApp.canvas.onSelectionChange,\n    () => canvasStore.updateSelectedItems()\n  )\n\n  // Load color palette\n  colorPaletteStore.customPalettes = settingStore.get(\n    'Comfy.CustomColorPalettes'\n  )\n\n  // Restore workflow and workflow tabs state from storage\n  await workflowPersistence.restorePreviousWorkflow()\n  workflowPersistence.restoreWorkflowTabsState()\n\n  // Start watching for locale change after the initial value is loaded.\n  watch(\n    () => settingStore.get('Comfy.Locale'),\n    async () => {\n      await useCommandStore().execute('Comfy.RefreshNodeDefinitions')\n      await useWorkflowService().reloadCurrentWorkflow()\n    }\n  )\n\n  emit('ready')\n})\n</script>\n", "<template>\n  <Toast />\n</template>\n\n<script setup lang=\"ts\">\nimport Toast from 'primevue/toast'\nimport { useToast } from 'primevue/usetoast'\nimport { nextTick, watch } from 'vue'\n\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useToastStore } from '@/stores/toastStore'\n\nconst toast = useToast()\nconst toastStore = useToastStore()\nconst settingStore = useSettingStore()\n\nwatch(\n  () => toastStore.messagesToAdd,\n  (newMessages) => {\n    if (newMessages.length === 0) {\n      return\n    }\n\n    newMessages.forEach((message) => {\n      toast.add(message)\n    })\n    toastStore.messagesToAdd = []\n  },\n  { deep: true }\n)\n\nwatch(\n  () => toastStore.messagesToRemove,\n  (messagesToRemove) => {\n    if (messagesToRemove.length === 0) {\n      return\n    }\n\n    messagesToRemove.forEach((message) => {\n      toast.remove(message)\n    })\n    toastStore.messagesToRemove = []\n  },\n  { deep: true }\n)\n\nwatch(\n  () => toastStore.removeAllRequested,\n  (requested) => {\n    if (requested) {\n      toast.removeAllGroups()\n      toastStore.removeAllRequested = false\n    }\n  }\n)\n\nfunction updateToastPosition() {\n  const styleElement =\n    document.getElementById('dynamic-toast-style') || createStyleElement()\n  const rect = document\n    .querySelector('.graph-canvas-container')\n    ?.getBoundingClientRect()\n  if (!rect) return\n\n  styleElement.textContent = `\n    .p-toast.p-component.p-toast-top-right {\n      top: ${rect.top + 20}px !important;\n      right: ${window.innerWidth - (rect.left + rect.width) + 20}px !important;\n    }\n  `\n}\n\nfunction createStyleElement() {\n  const style = document.createElement('style')\n  style.id = 'dynamic-toast-style'\n  document.head.appendChild(style)\n  return style\n}\n\nwatch(\n  () => settingStore.get('Comfy.UseNewMenu'),\n  () => nextTick(updateToastPosition),\n  { immediate: true }\n)\nwatch(\n  () => settingStore.get('Comfy.Sidebar.Location'),\n  () => nextTick(updateToastPosition),\n  { immediate: true }\n)\n</script>\n", "<template>\n  <Toast group=\"reroute-migration\">\n    <template #message>\n      <div class=\"flex flex-col items-start flex-auto\">\n        <div class=\"font-medium text-lg my-4\">\n          {{ t('toastMessages.migrateToLitegraphReroute') }}\n        </div>\n        <Button\n          class=\"self-end\"\n          size=\"small\"\n          :label=\"t('g.migrate')\"\n          severity=\"warn\"\n          @click=\"migrateToLitegraphReroute\"\n        />\n      </div>\n    </template>\n  </Toast>\n</template>\n\n<script setup lang=\"ts\">\nimport { useToast } from 'primevue'\nimport Button from 'primevue/button'\nimport Toast from 'primevue/toast'\nimport { useI18n } from 'vue-i18n'\n\nimport type { WorkflowJSON04 } from '@/schemas/comfyWorkflowSchema'\nimport { app } from '@/scripts/app'\nimport { useWorkflowStore } from '@/stores/workflowStore'\nimport { migrateLegacyRerouteNodes } from '@/utils/migration/migrateReroute'\n\nconst { t } = useI18n()\nconst toast = useToast()\n\nconst workflowStore = useWorkflowStore()\nconst migrateToLitegraphReroute = async () => {\n  const workflowJSON = app.graph.serialize() as unknown as WorkflowJSON04\n  const migratedWorkflowJSON = migrateLegacyRerouteNodes(workflowJSON)\n  await app.loadGraphData(\n    migratedWorkflowJSON,\n    false,\n    false,\n    workflowStore.activeWorkflow\n  )\n  toast.removeGroup('reroute-migration')\n}\n</script>\n", "<template>\n  <div\n    v-tooltip.bottom=\"{\n      value: $t('menu.batchCount'),\n      showDelay: 600\n    }\"\n    class=\"batch-count\"\n    :aria-label=\"$t('menu.batchCount')\"\n  >\n    <InputNumber\n      v-model=\"batchCount\"\n      class=\"w-14\"\n      :min=\"minQueueCount\"\n      :max=\"maxQueueCount\"\n      fluid\n      show-buttons\n      :pt=\"{\n        incrementButton: {\n          class: 'w-6',\n          onmousedown: () => {\n            handleClick(true)\n          }\n        },\n        decrementButton: {\n          class: 'w-6',\n          onmousedown: () => {\n            handleClick(false)\n          }\n        }\n      }\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { storeToRefs } from 'pinia'\nimport InputNumber from 'primevue/inputnumber'\nimport { computed } from 'vue'\n\nimport { useQueueSettingsStore } from '@/stores/queueStore'\nimport { useSettingStore } from '@/stores/settingStore'\n\nconst queueSettingsStore = useQueueSettingsStore()\nconst { batchCount } = storeToRefs(queueSettingsStore)\nconst minQueueCount = 1\n\nconst settingStore = useSettingStore()\nconst maxQueueCount = computed(() =>\n  settingStore.get('Comfy.QueueButton.BatchCountLimit')\n)\n\nconst handleClick = (increment: boolean) => {\n  let newCount: number\n  if (increment) {\n    const originalCount = batchCount.value - 1\n    newCount = Math.min(originalCount * 2, maxQueueCount.value)\n  } else {\n    const originalCount = batchCount.value + 1\n    newCount = Math.floor(originalCount / 2)\n  }\n\n  batchCount.value = newCount\n}\n</script>\n\n<style scoped>\n:deep(.p-inputtext) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n</style>\n", "<template>\n  <div class=\"queue-button-group flex\">\n    <SplitButton\n      v-tooltip.bottom=\"{\n        value: workspaceStore.shiftDown\n          ? $t('menu.runWorkflowFront')\n          : $t('menu.runWorkflow'),\n        showDelay: 600\n      }\"\n      class=\"comfyui-queue-button\"\n      :label=\"activeQueueModeMenuItem.label\"\n      severity=\"primary\"\n      size=\"small\"\n      :model=\"queueModeMenuItems\"\n      data-testid=\"queue-button\"\n      @click=\"queuePrompt\"\n    >\n      <template #icon>\n        <i-lucide:list-start v-if=\"workspaceStore.shiftDown\" />\n        <i-lucide:play v-else-if=\"queueMode === 'disabled'\" />\n        <i-lucide:fast-forward v-else-if=\"queueMode === 'instant'\" />\n        <i-lucide:step-forward v-else-if=\"queueMode === 'change'\" />\n      </template>\n      <template #item=\"{ item }\">\n        <Button\n          v-tooltip=\"{\n            value: item.tooltip,\n            showDelay: 600\n          }\"\n          :label=\"String(item.label)\"\n          :icon=\"item.icon\"\n          :severity=\"item.key === queueMode ? 'primary' : 'secondary'\"\n          size=\"small\"\n          text\n        />\n      </template>\n    </SplitButton>\n    <BatchCountEdit />\n    <ButtonGroup class=\"execution-actions flex flex-nowrap\">\n      <Button\n        v-tooltip.bottom=\"{\n          value: $t('menu.interrupt'),\n          showDelay: 600\n        }\"\n        icon=\"pi pi-times\"\n        :severity=\"executingPrompt ? 'danger' : 'secondary'\"\n        :disabled=\"!executingPrompt\"\n        text\n        :aria-label=\"$t('menu.interrupt')\"\n        @click=\"() => commandStore.execute('Comfy.Interrupt')\"\n      />\n      <Button\n        v-tooltip.bottom=\"{\n          value: $t('sideToolbar.queueTab.clearPendingTasks'),\n          showDelay: 600\n        }\"\n        icon=\"pi pi-stop\"\n        :severity=\"hasPendingTasks ? 'danger' : 'secondary'\"\n        :disabled=\"!hasPendingTasks\"\n        text\n        :aria-label=\"$t('sideToolbar.queueTab.clearPendingTasks')\"\n        @click=\"\n          () => {\n            if (queueCountStore.count.value > 1) {\n              commandStore.execute('Comfy.ClearPendingTasks')\n            }\n            queueMode = 'disabled'\n          }\n        \"\n      />\n    </ButtonGroup>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { storeToRefs } from 'pinia'\nimport Button from 'primevue/button'\nimport ButtonGroup from 'primevue/buttongroup'\nimport SplitButton from 'primevue/splitbutton'\nimport { computed } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { useCommandStore } from '@/stores/commandStore'\nimport {\n  useQueuePendingTaskCountStore,\n  useQueueSettingsStore\n} from '@/stores/queueStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\n\nimport BatchCountEdit from './BatchCountEdit.vue'\n\nconst workspaceStore = useWorkspaceStore()\nconst queueCountStore = storeToRefs(useQueuePendingTaskCountStore())\nconst { mode: queueMode } = storeToRefs(useQueueSettingsStore())\n\nconst { t } = useI18n()\nconst queueModeMenuItemLookup = computed(() => ({\n  disabled: {\n    key: 'disabled',\n    label: t('menu.run'),\n    tooltip: t('menu.disabledTooltip'),\n    command: () => {\n      queueMode.value = 'disabled'\n    }\n  },\n  instant: {\n    key: 'instant',\n    label: `${t('menu.run')} (${t('menu.instant')})`,\n    tooltip: t('menu.instantTooltip'),\n    command: () => {\n      queueMode.value = 'instant'\n    }\n  },\n  change: {\n    key: 'change',\n    label: `${t('menu.run')} (${t('menu.onChange')})`,\n    tooltip: t('menu.onChangeTooltip'),\n    command: () => {\n      queueMode.value = 'change'\n    }\n  }\n}))\n\nconst activeQueueModeMenuItem = computed(\n  () => queueModeMenuItemLookup.value[queueMode.value]\n)\nconst queueModeMenuItems = computed(() =>\n  Object.values(queueModeMenuItemLookup.value)\n)\n\nconst executingPrompt = computed(() => !!queueCountStore.count.value)\nconst hasPendingTasks = computed(\n  () => queueCountStore.count.value > 1 || queueMode.value !== 'disabled'\n)\n\nconst commandStore = useCommandStore()\nconst queuePrompt = async (e: Event) => {\n  const commandId =\n    'shiftKey' in e && e.shiftKey\n      ? 'Comfy.QueuePromptFront'\n      : 'Comfy.QueuePrompt'\n  await commandStore.execute(commandId)\n}\n</script>\n\n<style scoped>\n.comfyui-queue-button :deep(.p-splitbutton-dropdown) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n</style>\n", "<template>\n  <Panel\n    class=\"actionbar w-fit\"\n    :style=\"style\"\n    :class=\"{ 'is-dragging': isDragging, 'is-docked': isDocked }\"\n  >\n    <div ref=\"panelRef\" class=\"actionbar-content flex items-center select-none\">\n      <span ref=\"dragHandleRef\" class=\"drag-handle cursor-move mr-2 p-0!\" />\n      <ComfyQueueButton />\n    </div>\n  </Panel>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  useDraggable,\n  useElementBounding,\n  useEventBus,\n  useEventListener,\n  useLocalStorage,\n  watchDebounced\n} from '@vueuse/core'\nimport { clamp } from 'lodash'\nimport Panel from 'primevue/panel'\nimport { Ref, computed, inject, nextTick, onMounted, ref, watch } from 'vue'\n\nimport { useSettingStore } from '@/stores/settingStore'\n\nimport ComfyQueueButton from './ComfyQueueButton.vue'\n\nconst settingsStore = useSettingStore()\n\nconst visible = computed(\n  () => settingsStore.get('Comfy.UseNewMenu') !== 'Disabled'\n)\n\nconst panelRef = ref<HTMLElement | null>(null)\nconst dragHandleRef = ref<HTMLElement | null>(null)\nconst isDocked = useLocalStorage('Comfy.MenuPosition.Docked', false)\nconst storedPosition = useLocalStorage('Comfy.MenuPosition.Floating', {\n  x: 0,\n  y: 0\n})\nconst {\n  x,\n  y,\n  style: style,\n  isDragging\n} = useDraggable(panelRef, {\n  initialValue: { x: 0, y: 0 },\n  handle: dragHandleRef,\n  containerElement: document.body\n})\n\n// Update storedPosition when x or y changes\nwatchDebounced(\n  [x, y],\n  ([newX, newY]) => {\n    storedPosition.value = { x: newX, y: newY }\n  },\n  { debounce: 300 }\n)\n\n// Set initial position to bottom center\nconst setInitialPosition = () => {\n  if (panelRef.value) {\n    const screenWidth = window.innerWidth\n    const screenHeight = window.innerHeight\n    const menuWidth = panelRef.value.offsetWidth\n    const menuHeight = panelRef.value.offsetHeight\n\n    if (menuWidth === 0 || menuHeight === 0) {\n      return\n    }\n\n    // Check if stored position exists and is within bounds\n    if (storedPosition.value.x !== 0 || storedPosition.value.y !== 0) {\n      // Ensure stored position is within screen bounds\n      x.value = clamp(storedPosition.value.x, 0, screenWidth - menuWidth)\n      y.value = clamp(storedPosition.value.y, 0, screenHeight - menuHeight)\n      captureLastDragState()\n      return\n    }\n\n    // If no stored position or current position, set to bottom center\n    if (x.value === 0 && y.value === 0) {\n      x.value = clamp((screenWidth - menuWidth) / 2, 0, screenWidth - menuWidth)\n      y.value = clamp(\n        screenHeight - menuHeight - 10,\n        0,\n        screenHeight - menuHeight\n      )\n      captureLastDragState()\n    }\n  }\n}\nonMounted(setInitialPosition)\nwatch(visible, async (newVisible) => {\n  if (newVisible) {\n    await nextTick(setInitialPosition)\n  }\n})\n\nconst lastDragState = ref({\n  x: x.value,\n  y: y.value,\n  windowWidth: window.innerWidth,\n  windowHeight: window.innerHeight\n})\nconst captureLastDragState = () => {\n  lastDragState.value = {\n    x: x.value,\n    y: y.value,\n    windowWidth: window.innerWidth,\n    windowHeight: window.innerHeight\n  }\n}\nwatch(\n  isDragging,\n  (newIsDragging) => {\n    if (!newIsDragging) {\n      // Stop dragging\n      captureLastDragState()\n    }\n  },\n  { immediate: true }\n)\n\nconst adjustMenuPosition = () => {\n  if (panelRef.value) {\n    const screenWidth = window.innerWidth\n    const screenHeight = window.innerHeight\n    const menuWidth = panelRef.value.offsetWidth\n    const menuHeight = panelRef.value.offsetHeight\n\n    // Calculate distances to all edges\n    const distanceLeft = lastDragState.value.x\n    const distanceRight =\n      lastDragState.value.windowWidth - (lastDragState.value.x + menuWidth)\n    const distanceTop = lastDragState.value.y\n    const distanceBottom =\n      lastDragState.value.windowHeight - (lastDragState.value.y + menuHeight)\n\n    // Find the smallest distance to determine which edge to anchor to\n    const distances = [\n      { edge: 'left', distance: distanceLeft },\n      { edge: 'right', distance: distanceRight },\n      { edge: 'top', distance: distanceTop },\n      { edge: 'bottom', distance: distanceBottom }\n    ]\n    const closestEdge = distances.reduce((min, curr) =>\n      curr.distance < min.distance ? curr : min\n    )\n\n    // Calculate vertical position as a percentage of screen height\n    const verticalRatio =\n      lastDragState.value.y / lastDragState.value.windowHeight\n    const horizontalRatio =\n      lastDragState.value.x / lastDragState.value.windowWidth\n\n    // Apply positioning based on closest edge\n    if (closestEdge.edge === 'left') {\n      x.value = closestEdge.distance // Maintain exact distance from left\n      y.value = verticalRatio * screenHeight\n    } else if (closestEdge.edge === 'right') {\n      x.value = screenWidth - menuWidth - closestEdge.distance // Maintain exact distance from right\n      y.value = verticalRatio * screenHeight\n    } else if (closestEdge.edge === 'top') {\n      x.value = horizontalRatio * screenWidth\n      y.value = closestEdge.distance // Maintain exact distance from top\n    } else {\n      // bottom\n      x.value = horizontalRatio * screenWidth\n      y.value = screenHeight - menuHeight - closestEdge.distance // Maintain exact distance from bottom\n    }\n\n    // Ensure the menu stays within the screen bounds\n    x.value = clamp(x.value, 0, screenWidth - menuWidth)\n    y.value = clamp(y.value, 0, screenHeight - menuHeight)\n  }\n}\n\nuseEventListener(window, 'resize', adjustMenuPosition)\n\nconst topMenuRef = inject<Ref<HTMLDivElement | null>>('topMenuRef')\nconst topMenuBounds = useElementBounding(topMenuRef)\nconst overlapThreshold = 20 // pixels\nconst isOverlappingWithTopMenu = computed(() => {\n  if (!panelRef.value) {\n    return false\n  }\n  const { height } = panelRef.value.getBoundingClientRect()\n  const actionbarBottom = y.value + height\n  const topMenuBottom = topMenuBounds.bottom.value\n\n  const overlapPixels =\n    Math.min(actionbarBottom, topMenuBottom) -\n    Math.max(y.value, topMenuBounds.top.value)\n  return overlapPixels > overlapThreshold\n})\n\nwatch(isDragging, (newIsDragging) => {\n  if (!newIsDragging) {\n    // Stop dragging\n    isDocked.value = isOverlappingWithTopMenu.value\n  } else {\n    // Start dragging\n    isDocked.value = false\n  }\n})\n\nconst eventBus = useEventBus<string>('topMenu')\nwatch([isDragging, isOverlappingWithTopMenu], ([dragging, overlapping]) => {\n  eventBus.emit('updateHighlight', {\n    isDragging: dragging,\n    isOverlapping: overlapping\n  })\n})\n</script>\n\n<style scoped>\n.actionbar {\n  pointer-events: all;\n  position: fixed;\n  z-index: 1000;\n}\n\n.actionbar.is-docked {\n  position: static;\n  @apply bg-transparent border-none p-0;\n}\n\n.actionbar.is-dragging {\n  user-select: none;\n}\n\n:deep(.p-panel-content) {\n  @apply p-1;\n}\n\n.is-docked :deep(.p-panel-content) {\n  @apply p-0;\n}\n\n:deep(.p-panel-header) {\n  display: none;\n}\n\n.drag-handle {\n  @apply w-3 h-max;\n}\n</style>\n", "<template>\n  <Button\n    v-show=\"bottomPanelStore.bottomPanelTabs.length > 0\"\n    v-tooltip=\"{ value: $t('menu.toggleBottomPanel'), showDelay: 300 }\"\n    severity=\"secondary\"\n    text\n    :aria-label=\"$t('menu.toggleBottomPanel')\"\n    @click=\"bottomPanelStore.toggleBottomPanel\"\n  >\n    <template #icon>\n      <i-material-symbols:dock-to-bottom\n        v-if=\"bottomPanelStore.bottomPanelVisible\"\n      />\n      <i-material-symbols:dock-to-bottom-outline v-else />\n    </template>\n  </Button>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\n\nimport { useBottomPanelStore } from '@/stores/workspace/bottomPanelStore'\n\nconst bottomPanelStore = useBottomPanelStore()\n</script>\n", "<template>\n  <Menubar\n    :model=\"translatedItems\"\n    class=\"top-menubar border-none p-0 bg-transparent\"\n    :pt=\"{\n      rootList: 'gap-0 flex-nowrap w-auto',\n      submenu: `dropdown-direction-${dropdownDirection}`,\n      item: 'relative'\n    }\"\n  >\n    <template #item=\"{ item, props, root }\">\n      <a\n        class=\"p-menubar-item-link\"\n        v-bind=\"props.action\"\n        :href=\"item.url\"\n        target=\"_blank\"\n      >\n        <span v-if=\"item.icon\" class=\"p-menubar-item-icon\" :class=\"item.icon\" />\n        <span class=\"p-menubar-item-label\">{{ item.label }}</span>\n        <span\n          v-if=\"item?.comfyCommand?.keybinding\"\n          class=\"ml-auto border border-surface rounded text-muted text-xs text-nowrap p-1 keybinding-tag\"\n        >\n          {{ item.comfyCommand.keybinding.combo.toString() }}\n        </span>\n        <i v-if=\"!root && item.items\" class=\"ml-auto pi pi-angle-right\" />\n      </a>\n    </template>\n  </Menubar>\n</template>\n\n<script setup lang=\"ts\">\nimport Menubar from 'primevue/menubar'\nimport type { MenuItem } from 'primevue/menuitem'\nimport { computed } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport { useMenuItemStore } from '@/stores/menuItemStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { normalizeI18nKey } from '@/utils/formatUtil'\n\nconst settingStore = useSettingStore()\nconst dropdownDirection = computed(() =>\n  settingStore.get('Comfy.UseNewMenu') === 'Top' ? 'down' : 'up'\n)\n\nconst menuItemsStore = useMenuItemStore()\nconst { t } = useI18n()\nconst translateMenuItem = (item: MenuItem): MenuItem => {\n  const label = typeof item.label === 'function' ? item.label() : item.label\n  const translatedLabel = label\n    ? t(`menuLabels.${normalizeI18nKey(label)}`, label)\n    : undefined\n\n  return {\n    ...item,\n    label: translatedLabel,\n    items: item.items?.map(translateMenuItem)\n  }\n}\n\nconst translatedItems = computed(() =>\n  menuItemsStore.menuItems.map(translateMenuItem)\n)\n</script>\n\n<style scoped>\n:deep(.p-menubar-submenu.dropdown-direction-up) {\n  @apply top-auto bottom-full flex-col-reverse;\n}\n\n.keybinding-tag {\n  background: var(--p-content-hover-background);\n  border-color: var(--p-content-border-color);\n  border-style: solid;\n}\n</style>\n", "<!-- A popover that shows current user information and actions -->\n<template>\n  <div class=\"current-user-popover w-72\">\n    <!-- User Info Section -->\n    <div class=\"p-3\">\n      <div class=\"flex flex-col items-center\">\n        <UserAvatar\n          class=\"mb-3\"\n          :photo-url=\"userPhotoUrl\"\n          :pt:icon:class=\"{\n            '!text-2xl': !userPhotoUrl\n          }\"\n          size=\"large\"\n        />\n\n        <!-- User Details -->\n        <h3 class=\"text-lg font-semibold truncate my-0 mb-1\">\n          {{ userDisplayName || $t('g.user') }}\n        </h3>\n        <p v-if=\"userEmail\" class=\"text-sm text-muted truncate my-0\">\n          {{ userEmail }}\n        </p>\n      </div>\n    </div>\n\n    <Divider class=\"my-2\" />\n\n    <Button\n      class=\"justify-start\"\n      :label=\"$t('userSettings.title')\"\n      icon=\"pi pi-cog\"\n      text\n      fluid\n      severity=\"secondary\"\n      @click=\"handleOpenUserSettings\"\n    />\n\n    <Divider class=\"my-2\" />\n\n    <Button\n      class=\"justify-start\"\n      :label=\"$t('credits.apiPricing')\"\n      icon=\"pi pi-external-link\"\n      text\n      fluid\n      severity=\"secondary\"\n      @click=\"handleOpenApiPricing\"\n    />\n\n    <Divider class=\"my-2\" />\n\n    <div class=\"w-full flex flex-col gap-2 p-2\">\n      <div class=\"text-muted text-sm\">\n        {{ $t('credits.yourCreditBalance') }}\n      </div>\n      <div class=\"flex justify-between items-center\">\n        <UserCredit text-class=\"text-2xl\" />\n        <Button :label=\"$t('credits.topUp.topUp')\" @click=\"handleTopUp\" />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport Divider from 'primevue/divider'\nimport { onMounted } from 'vue'\n\nimport UserAvatar from '@/components/common/UserAvatar.vue'\nimport UserCredit from '@/components/common/UserCredit.vue'\nimport { useCurrentUser } from '@/composables/auth/useCurrentUser'\nimport { useFirebaseAuthActions } from '@/composables/auth/useFirebaseAuthActions'\nimport { useDialogService } from '@/services/dialogService'\n\nconst emit = defineEmits<{\n  close: []\n}>()\n\nconst { userDisplayName, userEmail, userPhotoUrl } = useCurrentUser()\nconst authActions = useFirebaseAuthActions()\nconst dialogService = useDialogService()\n\nconst handleOpenUserSettings = () => {\n  dialogService.showSettingsDialog('user')\n  emit('close')\n}\n\nconst handleTopUp = () => {\n  dialogService.showTopUpCreditsDialog()\n  emit('close')\n}\n\nconst handleOpenApiPricing = () => {\n  window.open('https://docs.comfy.org/tutorials/api-nodes/pricing', '_blank')\n  emit('close')\n}\n\nonMounted(() => {\n  void authActions.fetchBalance()\n})\n</script>\n", "<!-- A button that shows current authenticated user's avatar -->\n<template>\n  <div>\n    <Button\n      v-if=\"isLoggedIn\"\n      class=\"user-profile-button p-1\"\n      severity=\"secondary\"\n      text\n      aria-label=\"user profile\"\n      @click=\"popover?.toggle($event)\"\n    >\n      <div\n        class=\"flex items-center rounded-full bg-[var(--p-content-background)]\"\n      >\n        <UserAvatar :photo-url=\"photoURL\" />\n\n        <i class=\"pi pi-chevron-down px-1\" :style=\"{ fontSize: '0.5rem' }\" />\n      </div>\n    </Button>\n\n    <Popover ref=\"popover\" :show-arrow=\"false\">\n      <CurrentUserPopover @close=\"closePopover\" />\n    </Popover>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport Popover from 'primevue/popover'\nimport { computed, ref } from 'vue'\n\nimport UserAvatar from '@/components/common/UserAvatar.vue'\nimport { useCurrentUser } from '@/composables/auth/useCurrentUser'\n\nimport CurrentUserPopover from './CurrentUserPopover.vue'\n\nconst { isLoggedIn, userPhotoUrl } = useCurrentUser()\n\nconst popover = ref<InstanceType<typeof Popover> | null>(null)\nconst photoURL = computed<string | undefined>(\n  () => userPhotoUrl.value ?? undefined\n)\n\nconst closePopover = () => {\n  popover.value?.hide()\n}\n</script>\n", "<template>\n  <div\n    v-show=\"showTopMenu\"\n    ref=\"topMenuRef\"\n    class=\"comfyui-menu flex items-center\"\n    :class=\"{ dropzone: isDropZone, 'dropzone-active': isDroppable }\"\n  >\n    <img\n      src=\"/assets/images/comfy-logo-mono.svg\"\n      alt=\"ComfyUI Logo\"\n      class=\"comfyui-logo ml-2 app-drag h-6\"\n    />\n    <CommandMenubar />\n    <div class=\"flex-grow min-w-0 app-drag h-full\">\n      <WorkflowTabs v-if=\"workflowTabsPosition === 'Topbar'\" />\n    </div>\n    <div ref=\"menuRight\" class=\"comfyui-menu-right flex-shrink-0\" />\n    <Actionbar />\n    <CurrentUserButton class=\"flex-shrink-0\" />\n    <BottomPanelToggleButton class=\"flex-shrink-0\" />\n    <Button\n      v-tooltip=\"{ value: $t('menu.hideMenu'), showDelay: 300 }\"\n      class=\"flex-shrink-0\"\n      icon=\"pi pi-bars\"\n      severity=\"secondary\"\n      text\n      :aria-label=\"$t('menu.hideMenu')\"\n      @click=\"workspaceState.focusMode = true\"\n      @contextmenu=\"showNativeSystemMenu\"\n    />\n    <div\n      v-show=\"menuSetting !== 'Bottom'\"\n      class=\"window-actions-spacer flex-shrink-0\"\n    />\n  </div>\n\n  <!-- Virtual top menu for native window (drag handle) -->\n  <div\n    v-show=\"isNativeWindow() && !showTopMenu\"\n    class=\"fixed top-0 left-0 app-drag w-full h-[var(--comfy-topbar-height)]\"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport { useEventBus } from '@vueuse/core'\nimport Button from 'primevue/button'\nimport { computed, onMounted, provide, ref } from 'vue'\n\nimport Actionbar from '@/components/actionbar/ComfyActionbar.vue'\nimport BottomPanelToggleButton from '@/components/topbar/BottomPanelToggleButton.vue'\nimport CommandMenubar from '@/components/topbar/CommandMenubar.vue'\nimport CurrentUserButton from '@/components/topbar/CurrentUserButton.vue'\nimport WorkflowTabs from '@/components/topbar/WorkflowTabs.vue'\nimport { app } from '@/scripts/app'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\nimport {\n  electronAPI,\n  isElectron,\n  isNativeWindow,\n  showNativeSystemMenu\n} from '@/utils/envUtil'\n\nconst workspaceState = useWorkspaceStore()\nconst settingStore = useSettingStore()\n\nconst workflowTabsPosition = computed(() =>\n  settingStore.get('Comfy.Workflow.WorkflowTabsPosition')\n)\nconst menuSetting = computed(() => settingStore.get('Comfy.UseNewMenu'))\nconst betaMenuEnabled = computed(() => menuSetting.value !== 'Disabled')\nconst showTopMenu = computed(\n  () => betaMenuEnabled.value && !workspaceState.focusMode\n)\n\nconst menuRight = ref<HTMLDivElement | null>(null)\n// Menu-right holds legacy topbar elements attached by custom scripts\nonMounted(() => {\n  if (menuRight.value) {\n    menuRight.value.appendChild(app.menu.element)\n  }\n})\n\nconst topMenuRef = ref<HTMLDivElement | null>(null)\nprovide('topMenuRef', topMenuRef)\nconst eventBus = useEventBus<string>('topMenu')\nconst isDropZone = ref(false)\nconst isDroppable = ref(false)\neventBus.on((event: string, payload: any) => {\n  if (event === 'updateHighlight') {\n    isDropZone.value = payload.isDragging\n    isDroppable.value = payload.isOverlapping && payload.isDragging\n  }\n})\n\nonMounted(() => {\n  if (isElectron()) {\n    electronAPI().changeTheme({\n      height: topMenuRef.value?.getBoundingClientRect().height ?? 0\n    })\n  }\n})\n</script>\n\n<style scoped>\n.comfyui-menu {\n  width: 100vw;\n  height: var(--comfy-topbar-height);\n  background: var(--comfy-menu-bg);\n  color: var(--fg-color);\n  box-shadow: var(--bar-shadow);\n  font-family: Arial, Helvetica, sans-serif;\n  font-size: 0.8em;\n  box-sizing: border-box;\n  z-index: 1000;\n  order: 0;\n  grid-column: 1/-1;\n}\n\n.comfyui-menu.dropzone {\n  background: var(--p-highlight-background);\n}\n\n.comfyui-menu.dropzone-active {\n  background: var(--p-highlight-background-focus);\n}\n\n:deep(.p-menubar-item-label) {\n  line-height: revert;\n}\n\n.comfyui-logo {\n  user-select: none;\n  cursor: default;\n  filter: invert(0);\n}\n\n.dark-theme .comfyui-logo {\n  filter: invert(1);\n}\n</style>\n", "import { useTitle } from '@vueuse/core'\nimport { computed } from 'vue'\n\nimport { useExecutionStore } from '@/stores/executionStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useWorkflowStore } from '@/stores/workflowStore'\n\nconst DEFAULT_TITLE = 'ComfyUI'\nconst TITLE_SUFFIX = ' - ComfyUI'\n\nexport const useBrowserTabTitle = () => {\n  const executionStore = useExecutionStore()\n  const settingStore = useSettingStore()\n  const workflowStore = useWorkflowStore()\n\n  const executionText = computed(() =>\n    executionStore.isIdle\n      ? ''\n      : `[${Math.round(executionStore.executionProgress * 100)}%]`\n  )\n\n  const newMenuEnabled = computed(\n    () => settingStore.get('Comfy.UseNewMenu') !== 'Disabled'\n  )\n\n  const isUnsavedText = computed(() =>\n    workflowStore.activeWorkflow?.isModified ||\n    !workflowStore.activeWorkflow?.isPersisted\n      ? ' *'\n      : ''\n  )\n  const workflowNameText = computed(() => {\n    const workflowName = workflowStore.activeWorkflow?.filename\n    return workflowName\n      ? isUnsavedText.value + workflowName + TITLE_SUFFIX\n      : DEFAULT_TITLE\n  })\n\n  const nodeExecutionTitle = computed(() =>\n    executionStore.executingNode && executionStore.executingNodeProgress\n      ? `${executionText.value}[${Math.round(executionStore.executingNodeProgress * 100)}%] ${executionStore.executingNode.type}`\n      : ''\n  )\n\n  const workflowTitle = computed(\n    () =>\n      executionText.value +\n      (newMenuEnabled.value ? workflowNameText.value : DEFAULT_TITLE)\n  )\n\n  const title = computed(() => nodeExecutionTitle.value || workflowTitle.value)\n  useTitle(title)\n}\n", "import {\n  LGraphEventMode,\n  LGraphGroup,\n  LGraphNode,\n  LiteGraph\n} from '@comfyorg/litegraph'\n\nimport { useFirebaseAuthActions } from '@/composables/auth/useFirebaseAuthActions'\nimport {\n  DEFAULT_DARK_COLOR_PALETTE,\n  DEFAULT_LIGHT_COLOR_PALETTE\n} from '@/constants/coreColorPalettes'\nimport { t } from '@/i18n'\nimport { api } from '@/scripts/api'\nimport { app } from '@/scripts/app'\nimport { useDialogService } from '@/services/dialogService'\nimport { useLitegraphService } from '@/services/litegraphService'\nimport { useWorkflowService } from '@/services/workflowService'\nimport type { ComfyCommand } from '@/stores/commandStore'\nimport { useTitleEditorStore } from '@/stores/graphStore'\nimport { useQueueSettingsStore, useQueueStore } from '@/stores/queueStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useToastStore } from '@/stores/toastStore'\nimport { type ComfyWorkflow, useWorkflowStore } from '@/stores/workflowStore'\nimport { useBottomPanelStore } from '@/stores/workspace/bottomPanelStore'\nimport { useColorPaletteStore } from '@/stores/workspace/colorPaletteStore'\nimport { useSearchBoxStore } from '@/stores/workspace/searchBoxStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\n\nexport function useCoreCommands(): ComfyCommand[] {\n  const workflowService = useWorkflowService()\n  const workflowStore = useWorkflowStore()\n  const dialogService = useDialogService()\n  const colorPaletteStore = useColorPaletteStore()\n  const firebaseAuthActions = useFirebaseAuthActions()\n  const toastStore = useToastStore()\n  const getTracker = () => workflowStore.activeWorkflow?.changeTracker\n\n  const getSelectedNodes = (): LGraphNode[] => {\n    const selectedNodes = app.canvas.selected_nodes\n    const result: LGraphNode[] = []\n    if (selectedNodes) {\n      for (const i in selectedNodes) {\n        const node = selectedNodes[i]\n        result.push(node)\n      }\n    }\n    return result\n  }\n\n  const toggleSelectedNodesMode = (mode: LGraphEventMode) => {\n    getSelectedNodes().forEach((node) => {\n      if (node.mode === mode) {\n        node.mode = LGraphEventMode.ALWAYS\n      } else {\n        node.mode = mode\n      }\n    })\n  }\n\n  const commands = [\n    {\n      id: 'Comfy.NewBlankWorkflow',\n      icon: 'pi pi-plus',\n      label: 'New Blank Workflow',\n      menubarLabel: 'New',\n      function: () => workflowService.loadBlankWorkflow()\n    },\n    {\n      id: 'Comfy.OpenWorkflow',\n      icon: 'pi pi-folder-open',\n      label: 'Open Workflow',\n      menubarLabel: 'Open',\n      function: () => {\n        app.ui.loadFile()\n      }\n    },\n    {\n      id: 'Comfy.LoadDefaultWorkflow',\n      icon: 'pi pi-code',\n      label: 'Load Default Workflow',\n      function: () => workflowService.loadDefaultWorkflow()\n    },\n    {\n      id: 'Comfy.SaveWorkflow',\n      icon: 'pi pi-save',\n      label: 'Save Workflow',\n      menubarLabel: 'Save',\n      function: async () => {\n        const workflow = useWorkflowStore().activeWorkflow as ComfyWorkflow\n        if (!workflow) return\n\n        await workflowService.saveWorkflow(workflow)\n      }\n    },\n    {\n      id: 'Comfy.SaveWorkflowAs',\n      icon: 'pi pi-save',\n      label: 'Save Workflow As',\n      menubarLabel: 'Save As',\n      function: async () => {\n        const workflow = useWorkflowStore().activeWorkflow as ComfyWorkflow\n        if (!workflow) return\n\n        await workflowService.saveWorkflowAs(workflow)\n      }\n    },\n    {\n      id: 'Comfy.ExportWorkflow',\n      icon: 'pi pi-download',\n      label: 'Export Workflow',\n      menubarLabel: 'Export',\n      function: async () => {\n        await workflowService.exportWorkflow('workflow', 'workflow')\n      }\n    },\n    {\n      id: 'Comfy.ExportWorkflowAPI',\n      icon: 'pi pi-download',\n      label: 'Export Workflow (API Format)',\n      menubarLabel: 'Export (API)',\n      function: async () => {\n        await workflowService.exportWorkflow('workflow_api', 'output')\n      }\n    },\n    {\n      id: 'Comfy.Undo',\n      icon: 'pi pi-undo',\n      label: 'Undo',\n      function: async () => {\n        await getTracker()?.undo?.()\n      }\n    },\n    {\n      id: 'Comfy.Redo',\n      icon: 'pi pi-refresh',\n      label: 'Redo',\n      function: async () => {\n        await getTracker()?.redo?.()\n      }\n    },\n    {\n      id: 'Comfy.ClearWorkflow',\n      icon: 'pi pi-trash',\n      label: 'Clear Workflow',\n      function: () => {\n        const settingStore = useSettingStore()\n        if (\n          !settingStore.get('Comfy.ComfirmClear') ||\n          confirm('Clear workflow?')\n        ) {\n          app.clean()\n          app.graph.clear()\n          api.dispatchCustomEvent('graphCleared')\n        }\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ResetView',\n      icon: 'pi pi-expand',\n      label: 'Reset View',\n      function: () => {\n        useLitegraphService().resetView()\n      }\n    },\n    {\n      id: 'Comfy.OpenClipspace',\n      icon: 'pi pi-clipboard',\n      label: 'Clipspace',\n      function: () => {\n        app.openClipspace()\n      }\n    },\n    {\n      id: 'Comfy.RefreshNodeDefinitions',\n      icon: 'pi pi-refresh',\n      label: 'Refresh Node Definitions',\n      function: async () => {\n        await app.refreshComboInNodes()\n      }\n    },\n    {\n      id: 'Comfy.Interrupt',\n      icon: 'pi pi-stop',\n      label: 'Interrupt',\n      function: async () => {\n        await api.interrupt()\n        toastStore.add({\n          severity: 'info',\n          summary: t('g.interrupted'),\n          detail: t('toastMessages.interrupted'),\n          life: 1000\n        })\n      }\n    },\n    {\n      id: 'Comfy.ClearPendingTasks',\n      icon: 'pi pi-stop',\n      label: 'Clear Pending Tasks',\n      function: async () => {\n        await useQueueStore().clear(['queue'])\n        toastStore.add({\n          severity: 'info',\n          summary: t('g.confirmed'),\n          detail: t('toastMessages.pendingTasksDeleted'),\n          life: 3000\n        })\n      }\n    },\n    {\n      id: 'Comfy.BrowseTemplates',\n      icon: 'pi pi-folder-open',\n      label: 'Browse Templates',\n      function: () => {\n        dialogService.showTemplateWorkflowsDialog()\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ZoomIn',\n      icon: 'pi pi-plus',\n      label: 'Zoom In',\n      function: () => {\n        const ds = app.canvas.ds\n        ds.changeScale(\n          ds.scale * 1.1,\n          ds.element ? [ds.element.width / 2, ds.element.height / 2] : undefined\n        )\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ZoomOut',\n      icon: 'pi pi-minus',\n      label: 'Zoom Out',\n      function: () => {\n        const ds = app.canvas.ds\n        ds.changeScale(\n          ds.scale / 1.1,\n          ds.element ? [ds.element.width / 2, ds.element.height / 2] : undefined\n        )\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Canvas.FitView',\n      icon: 'pi pi-expand',\n      label: 'Fit view to selected nodes',\n      function: () => {\n        if (app.canvas.empty) {\n          toastStore.add({\n            severity: 'error',\n            summary: t('toastMessages.emptyCanvas'),\n            life: 3000\n          })\n          return\n        }\n        app.canvas.fitViewToSelectionAnimated()\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ToggleLock',\n      icon: 'pi pi-lock',\n      label: 'Canvas Toggle Lock',\n      function: () => {\n        app.canvas['read_only'] = !app.canvas['read_only']\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ToggleLinkVisibility',\n      icon: 'pi pi-eye',\n      label: 'Canvas Toggle Link Visibility',\n      versionAdded: '1.3.6',\n\n      function: (() => {\n        const settingStore = useSettingStore()\n        let lastLinksRenderMode = LiteGraph.SPLINE_LINK\n\n        return async () => {\n          const currentMode = settingStore.get('Comfy.LinkRenderMode')\n\n          if (currentMode === LiteGraph.HIDDEN_LINK) {\n            // If links are hidden, restore the last positive value or default to spline mode\n            await settingStore.set('Comfy.LinkRenderMode', lastLinksRenderMode)\n          } else {\n            // If links are visible, store the current mode and hide links\n            lastLinksRenderMode = currentMode\n            await settingStore.set(\n              'Comfy.LinkRenderMode',\n              LiteGraph.HIDDEN_LINK\n            )\n          }\n        }\n      })()\n    },\n    {\n      id: 'Comfy.QueuePrompt',\n      icon: 'pi pi-play',\n      label: 'Queue Prompt',\n      versionAdded: '1.3.7',\n      function: async () => {\n        const batchCount = useQueueSettingsStore().batchCount\n        await app.queuePrompt(0, batchCount)\n      }\n    },\n    {\n      id: 'Comfy.QueuePromptFront',\n      icon: 'pi pi-play',\n      label: 'Queue Prompt (Front)',\n      versionAdded: '1.3.7',\n      function: async () => {\n        const batchCount = useQueueSettingsStore().batchCount\n        await app.queuePrompt(-1, batchCount)\n      }\n    },\n    {\n      id: 'Comfy.QueueSelectedOutputNodes',\n      icon: 'pi pi-play',\n      label: 'Queue Selected Output Nodes',\n      versionAdded: '1.19.6',\n      function: async () => {\n        const batchCount = useQueueSettingsStore().batchCount\n        const queueNodeIds = getSelectedNodes()\n          .filter((node) => node.constructor.nodeData?.output_node)\n          .map((node) => node.id)\n        if (queueNodeIds.length === 0) {\n          toastStore.add({\n            severity: 'error',\n            summary: t('toastMessages.nothingToQueue'),\n            detail: t('toastMessages.pleaseSelectOutputNodes'),\n            life: 3000\n          })\n          return\n        }\n        await app.queuePrompt(0, batchCount, queueNodeIds)\n      }\n    },\n    {\n      id: 'Comfy.ShowSettingsDialog',\n      icon: 'pi pi-cog',\n      label: 'Show Settings Dialog',\n      versionAdded: '1.3.7',\n      function: () => {\n        dialogService.showSettingsDialog()\n      }\n    },\n    {\n      id: 'Comfy.Graph.GroupSelectedNodes',\n      icon: 'pi pi-sitemap',\n      label: 'Group Selected Nodes',\n      versionAdded: '1.3.7',\n      function: () => {\n        const { canvas } = app\n        if (!canvas.selectedItems?.size) {\n          toastStore.add({\n            severity: 'error',\n            summary: t('toastMessages.nothingToGroup'),\n            detail: t('toastMessages.pleaseSelectNodesToGroup'),\n            life: 3000\n          })\n          return\n        }\n        const group = new LGraphGroup()\n        const padding = useSettingStore().get(\n          'Comfy.GroupSelectedNodes.Padding'\n        )\n        group.resizeTo(canvas.selectedItems, padding)\n        canvas.graph?.add(group)\n        useTitleEditorStore().titleEditorTarget = group\n      }\n    },\n    {\n      id: 'Workspace.NextOpenedWorkflow',\n      icon: 'pi pi-step-forward',\n      label: 'Next Opened Workflow',\n      versionAdded: '1.3.9',\n      function: async () => {\n        await workflowService.loadNextOpenedWorkflow()\n      }\n    },\n    {\n      id: 'Workspace.PreviousOpenedWorkflow',\n      icon: 'pi pi-step-backward',\n      label: 'Previous Opened Workflow',\n      versionAdded: '1.3.9',\n      function: async () => {\n        await workflowService.loadPreviousOpenedWorkflow()\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ToggleSelectedNodes.Mute',\n      icon: 'pi pi-volume-off',\n      label: 'Mute/Unmute Selected Nodes',\n      versionAdded: '1.3.11',\n      function: () => {\n        toggleSelectedNodesMode(LGraphEventMode.NEVER)\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ToggleSelectedNodes.Bypass',\n      icon: 'pi pi-shield',\n      label: 'Bypass/Unbypass Selected Nodes',\n      versionAdded: '1.3.11',\n      function: () => {\n        toggleSelectedNodesMode(LGraphEventMode.BYPASS)\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ToggleSelectedNodes.Pin',\n      icon: 'pi pi-pin',\n      label: 'Pin/Unpin Selected Nodes',\n      versionAdded: '1.3.11',\n      function: () => {\n        getSelectedNodes().forEach((node) => {\n          node.pin(!node.pinned)\n        })\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ToggleSelected.Pin',\n      icon: 'pi pi-pin',\n      label: 'Pin/Unpin Selected Items',\n      versionAdded: '1.3.33',\n      function: () => {\n        for (const item of app.canvas.selectedItems) {\n          if (item instanceof LGraphNode || item instanceof LGraphGroup) {\n            item.pin(!item.pinned)\n          }\n        }\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Canvas.Resize',\n      icon: 'pi pi-minus',\n      label: 'Resize Selected Nodes',\n      versionAdded: '',\n      function: () => {\n        getSelectedNodes().forEach((node) => {\n          const optimalSize = node.computeSize()\n          node.setSize([optimalSize[0], optimalSize[1]])\n        })\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Canvas.ToggleSelectedNodes.Collapse',\n      icon: 'pi pi-minus',\n      label: 'Collapse/Expand Selected Nodes',\n      versionAdded: '1.3.11',\n      function: () => {\n        getSelectedNodes().forEach((node) => {\n          node.collapse()\n        })\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.ToggleTheme',\n      icon: 'pi pi-moon',\n      label: 'Toggle Theme (Dark/Light)',\n      versionAdded: '1.3.12',\n      function: (() => {\n        let previousDarkTheme: string = DEFAULT_DARK_COLOR_PALETTE.id\n        let previousLightTheme: string = DEFAULT_LIGHT_COLOR_PALETTE.id\n\n        return async () => {\n          const settingStore = useSettingStore()\n          const theme = colorPaletteStore.completedActivePalette\n          if (theme.light_theme) {\n            previousLightTheme = theme.id\n            await settingStore.set('Comfy.ColorPalette', previousDarkTheme)\n          } else {\n            previousDarkTheme = theme.id\n            await settingStore.set('Comfy.ColorPalette', previousLightTheme)\n          }\n        }\n      })()\n    },\n    {\n      id: 'Workspace.ToggleBottomPanel',\n      icon: 'pi pi-list',\n      label: 'Toggle Bottom Panel',\n      versionAdded: '1.3.22',\n      function: () => {\n        useBottomPanelStore().toggleBottomPanel()\n      }\n    },\n    {\n      id: 'Workspace.ToggleFocusMode',\n      icon: 'pi pi-eye',\n      label: 'Toggle Focus Mode',\n      versionAdded: '1.3.27',\n      function: () => {\n        useWorkspaceStore().toggleFocusMode()\n      }\n    },\n    {\n      id: 'Comfy.Graph.FitGroupToContents',\n      icon: 'pi pi-expand',\n      label: 'Fit Group To Contents',\n      versionAdded: '1.4.9',\n      function: () => {\n        for (const group of app.canvas.selectedItems) {\n          if (group instanceof LGraphGroup) {\n            group.recomputeInsideNodes()\n            const padding = useSettingStore().get(\n              'Comfy.GroupSelectedNodes.Padding'\n            )\n            group.resizeTo(group.children, padding)\n            app.graph.change()\n          }\n        }\n      }\n    },\n    {\n      id: 'Comfy.Help.OpenComfyUIIssues',\n      icon: 'pi pi-github',\n      label: 'Open ComfyUI Issues',\n      menubarLabel: 'ComfyUI Issues',\n      versionAdded: '1.5.5',\n      function: () => {\n        window.open(\n          'https://github.com/comfyanonymous/ComfyUI/issues',\n          '_blank'\n        )\n      }\n    },\n    {\n      id: 'Comfy.Help.OpenComfyUIDocs',\n      icon: 'pi pi-info-circle',\n      label: 'Open ComfyUI Docs',\n      menubarLabel: 'ComfyUI Docs',\n      versionAdded: '1.5.5',\n      function: () => {\n        window.open('https://docs.comfy.org/', '_blank')\n      }\n    },\n    {\n      id: 'Comfy.Help.OpenComfyOrgDiscord',\n      icon: 'pi pi-discord',\n      label: 'Open Comfy-Org Discord',\n      menubarLabel: 'Comfy-Org Discord',\n      versionAdded: '1.5.5',\n      function: () => {\n        window.open('https://www.comfy.org/discord', '_blank')\n      }\n    },\n    {\n      id: 'Workspace.SearchBox.Toggle',\n      icon: 'pi pi-search',\n      label: 'Toggle Search Box',\n      versionAdded: '1.5.7',\n      function: () => {\n        useSearchBoxStore().toggleVisible()\n      }\n    },\n    {\n      id: 'Comfy.Help.AboutComfyUI',\n      icon: 'pi pi-info-circle',\n      label: 'Open About ComfyUI',\n      menubarLabel: 'About ComfyUI',\n      versionAdded: '1.6.4',\n      function: () => {\n        dialogService.showSettingsDialog('about')\n      }\n    },\n    {\n      id: 'Comfy.DuplicateWorkflow',\n      icon: 'pi pi-clone',\n      label: 'Duplicate Current Workflow',\n      versionAdded: '1.6.15',\n      function: async () => {\n        await workflowService.duplicateWorkflow(workflowStore.activeWorkflow!)\n      }\n    },\n    {\n      id: 'Workspace.CloseWorkflow',\n      icon: 'pi pi-times',\n      label: 'Close Current Workflow',\n      versionAdded: '1.7.3',\n      function: async () => {\n        if (workflowStore.activeWorkflow)\n          await workflowService.closeWorkflow(workflowStore.activeWorkflow)\n      }\n    },\n    {\n      id: 'Comfy.Feedback',\n      icon: 'pi pi-megaphone',\n      label: 'Give Feedback',\n      versionAdded: '1.8.2',\n      function: () => {\n        dialogService.showIssueReportDialog({\n          title: t('g.feedback'),\n          subtitle: t('issueReport.feedbackTitle'),\n          panelProps: {\n            errorType: 'Feedback',\n            defaultFields: ['SystemStats', 'Settings']\n          }\n        })\n      }\n    },\n    {\n      id: 'Comfy.ContactSupport',\n      icon: 'pi pi-question',\n      label: 'Contact Support',\n      versionAdded: '1.17.8',\n      function: () => {\n        dialogService.showIssueReportDialog({\n          title: t('issueReport.contactSupportTitle'),\n          subtitle: t('issueReport.contactSupportDescription'),\n          panelProps: {\n            errorType: 'ContactSupport',\n            defaultFields: ['Workflow', 'Logs', 'SystemStats', 'Settings']\n          }\n        })\n      }\n    },\n    {\n      id: 'Comfy.Help.OpenComfyUIForum',\n      icon: 'pi pi-comments',\n      label: 'Open ComfyUI Forum',\n      menubarLabel: 'ComfyUI Forum',\n      versionAdded: '1.8.2',\n      function: () => {\n        window.open('https://forum.comfy.org/', '_blank')\n      }\n    },\n    {\n      id: 'Comfy.Canvas.DeleteSelectedItems',\n      icon: 'pi pi-trash',\n      label: 'Delete Selected Items',\n      versionAdded: '1.10.5',\n      function: () => {\n        app.canvas.deleteSelected()\n        app.canvas.setDirty(true, true)\n      }\n    },\n    {\n      id: 'Comfy.Manager.CustomNodesManager',\n      icon: 'pi pi-puzzle',\n      label: 'Custom Nodes Manager',\n      versionAdded: '1.12.10',\n      function: () => {\n        dialogService.showManagerDialog()\n      }\n    },\n    {\n      id: 'Comfy.Manager.ToggleManagerProgressDialog',\n      icon: 'pi pi-spinner',\n      label: 'Toggle Progress Dialog',\n      versionAdded: '1.13.9',\n      function: () => {\n        dialogService.showManagerProgressDialog()\n      }\n    },\n    {\n      id: 'Comfy.User.OpenSignInDialog',\n      icon: 'pi pi-user',\n      label: 'Open Sign In Dialog',\n      versionAdded: '1.17.6',\n      function: async () => {\n        await dialogService.showSignInDialog()\n      }\n    },\n    {\n      id: 'Comfy.User.SignOut',\n      icon: 'pi pi-sign-out',\n      label: 'Sign Out',\n      versionAdded: '1.18.1',\n      function: async () => {\n        await firebaseAuthActions.logout()\n      }\n    }\n  ]\n\n  return commands.map((command) => ({ ...command, source: 'System' }))\n}\n", "import { useFavicon } from '@vueuse/core'\nimport { watch } from 'vue'\n\nimport { useExecutionStore } from '@/stores/executionStore'\n\nexport const useProgressFavicon = () => {\n  const defaultFavicon = '/assets/images/favicon_progress_16x16/frame_9.png'\n  const favicon = useFavicon(defaultFavicon)\n  const executionStore = useExecutionStore()\n  const totalFrames = 10\n\n  watch(\n    [() => executionStore.executionProgress, () => executionStore.isIdle],\n    ([progress, isIdle]) => {\n      if (isIdle) {\n        favicon.value = defaultFavicon\n      } else {\n        const frame = Math.floor(progress * totalFrames)\n        favicon.value = `/assets/images/favicon_progress_16x16/frame_${frame}.png`\n      }\n    }\n  )\n}\n", "export enum LatentPreviewMethod {\n  NoPreviews = 'none',\n  Auto = 'auto',\n  Latent2RGB = 'latent2rgb',\n  TAESD = 'taesd'\n}\n\nexport enum LogLevel {\n  DEBUG = 'DEBUG',\n  INFO = 'INFO',\n  WARNING = 'WARNING',\n  ERROR = 'ERROR',\n  CRITICAL = 'CRITICAL'\n}\n\nexport enum HashFunction {\n  MD5 = 'md5',\n  SHA1 = 'sha1',\n  SHA256 = 'sha256',\n  SHA512 = 'sha512'\n}\n\nexport enum AutoLaunch {\n  // Let server decide whether to auto launch based on the current environment\n  Auto = 'auto',\n  // Disable auto launch\n  Disable = 'disable',\n  // Enable auto launch\n  Enable = 'enable'\n}\n\nexport enum CudaMalloc {\n  // Let server decide whether to use CUDA malloc based on the current environment\n  Auto = 'auto',\n  // Disable CUDA malloc\n  Disable = 'disable',\n  // Enable CUDA malloc\n  Enable = 'enable'\n}\n\nexport enum FloatingPointPrecision {\n  AUTO = 'auto',\n  FP64 = 'fp64',\n  FP32 = 'fp32',\n  FP16 = 'fp16',\n  BF16 = 'bf16',\n  FP8E4M3FN = 'fp8_e4m3fn',\n  FP8E5M2 = 'fp8_e5m2'\n}\n\nexport enum CrossAttentionMethod {\n  Auto = 'auto',\n  Split = 'split',\n  Quad = 'quad',\n  Pytorch = 'pytorch'\n}\n\nexport enum VramManagement {\n  Auto = 'auto',\n  GPUOnly = 'gpu-only',\n  HighVram = 'highvram',\n  NormalVram = 'normalvram',\n  LowVram = 'lowvram',\n  NoVram = 'novram',\n  CPU = 'cpu'\n}\n", "import {\n  AutoLaunch,\n  CrossAttentionMethod,\n  CudaMalloc,\n  FloatingPointPrecision,\n  HashFunction,\n  LatentPreviewMethod,\n  LogLevel,\n  VramManagement\n} from '@/types/serverArgs'\nimport { FormItem } from '@/types/settingTypes'\n\nexport type ServerConfigValue = string | number | true | null | undefined\n\nexport interface ServerConfig<T> extends FormItem {\n  id: string\n  defaultValue: T\n  category?: string[]\n  // Override the default value getter with a custom function.\n  getValue?: (value: T) => Record<string, ServerConfigValue>\n}\n\nexport const WEB_ONLY_CONFIG_ITEMS: ServerConfig<any>[] = [\n  // Launch behavior\n  {\n    id: 'auto-launch',\n    name: 'Automatically opens in the browser on startup',\n    category: ['Launch'],\n    type: 'combo',\n    options: Object.values(AutoLaunch),\n    defaultValue: AutoLaunch.Auto,\n    getValue: (value: AutoLaunch) => {\n      switch (value) {\n        case AutoLaunch.Auto:\n          return {}\n        case AutoLaunch.Enable:\n          return {\n            ['auto-launch']: true\n          }\n        case AutoLaunch.Disable:\n          return {\n            ['disable-auto-launch']: true\n          }\n      }\n    }\n  }\n]\n\nexport const SERVER_CONFIG_ITEMS: ServerConfig<any>[] = [\n  // Network settings\n  {\n    id: 'listen',\n    name: 'Host: The IP address to listen on',\n    category: ['Network'],\n    type: 'text',\n    defaultValue: '127.0.0.1'\n  },\n  {\n    id: 'port',\n    name: 'Port: The port to listen on',\n    category: ['Network'],\n    type: 'number',\n    // The default launch port for desktop app is 8000 instead of 8188.\n    defaultValue: 8000\n  },\n  {\n    id: 'tls-keyfile',\n    name: 'TLS Key File: Path to TLS key file for HTTPS',\n    category: ['Network'],\n    type: 'text',\n    defaultValue: ''\n  },\n  {\n    id: 'tls-certfile',\n    name: 'TLS Certificate File: Path to TLS certificate file for HTTPS',\n    category: ['Network'],\n    type: 'text',\n    defaultValue: ''\n  },\n  {\n    id: 'enable-cors-header',\n    name: 'Enable CORS header: Use \"*\" for all origins or specify domain',\n    category: ['Network'],\n    type: 'text',\n    defaultValue: ''\n  },\n  {\n    id: 'max-upload-size',\n    name: 'Maximum upload size (MB)',\n    category: ['Network'],\n    type: 'number',\n    defaultValue: 100\n  },\n\n  // CUDA settings\n  {\n    id: 'cuda-device',\n    name: 'CUDA device index to use',\n    category: ['CUDA'],\n    type: 'number',\n    defaultValue: null\n  },\n  {\n    id: 'cuda-malloc',\n    name: 'Use CUDA malloc for memory allocation',\n    category: ['CUDA'],\n    type: 'combo',\n    options: Object.values(CudaMalloc),\n    defaultValue: CudaMalloc.Auto,\n    getValue: (value: CudaMalloc) => {\n      switch (value) {\n        case CudaMalloc.Auto:\n          return {}\n        case CudaMalloc.Enable:\n          return {\n            ['cuda-malloc']: true\n          }\n        case CudaMalloc.Disable:\n          return {\n            ['disable-cuda-malloc']: true\n          }\n      }\n    }\n  },\n\n  // Precision settings\n  {\n    id: 'global-precision',\n    name: 'Global floating point precision',\n    category: ['Inference'],\n    type: 'combo',\n    options: [\n      FloatingPointPrecision.AUTO,\n      FloatingPointPrecision.FP32,\n      FloatingPointPrecision.FP16\n    ],\n    defaultValue: FloatingPointPrecision.AUTO,\n    tooltip: 'Global floating point precision',\n    getValue: (value: FloatingPointPrecision) => {\n      switch (value) {\n        case FloatingPointPrecision.AUTO:\n          return {}\n        case FloatingPointPrecision.FP32:\n          return {\n            ['force-fp32']: true\n          }\n        case FloatingPointPrecision.FP16:\n          return {\n            ['force-fp16']: true\n          }\n        default:\n          return {}\n      }\n    }\n  },\n\n  // UNET precision\n  {\n    id: 'unet-precision',\n    name: 'UNET precision',\n    category: ['Inference'],\n    type: 'combo',\n    options: [\n      FloatingPointPrecision.AUTO,\n      FloatingPointPrecision.FP64,\n      FloatingPointPrecision.FP32,\n      FloatingPointPrecision.FP16,\n      FloatingPointPrecision.BF16,\n      FloatingPointPrecision.FP8E4M3FN,\n      FloatingPointPrecision.FP8E5M2\n    ],\n    defaultValue: FloatingPointPrecision.AUTO,\n    tooltip: 'UNET precision',\n    getValue: (value: FloatingPointPrecision) => {\n      switch (value) {\n        case FloatingPointPrecision.AUTO:\n          return {}\n        default:\n          return {\n            [`${value.toLowerCase()}-unet`]: true\n          }\n      }\n    }\n  },\n\n  // VAE settings\n  {\n    id: 'vae-precision',\n    name: 'VAE precision',\n    category: ['Inference'],\n    type: 'combo',\n    options: [\n      FloatingPointPrecision.AUTO,\n      FloatingPointPrecision.FP16,\n      FloatingPointPrecision.FP32,\n      FloatingPointPrecision.BF16\n    ],\n    defaultValue: FloatingPointPrecision.AUTO,\n    tooltip: 'VAE precision',\n    getValue: (value: FloatingPointPrecision) => {\n      switch (value) {\n        case FloatingPointPrecision.AUTO:\n          return {}\n        default:\n          return {\n            [`${value.toLowerCase()}-vae`]: true\n          }\n      }\n    }\n  },\n  {\n    id: 'cpu-vae',\n    name: 'Run VAE on CPU',\n    category: ['Inference'],\n    type: 'boolean',\n    defaultValue: false\n  },\n\n  // Text Encoder settings\n  {\n    id: 'text-encoder-precision',\n    name: 'Text Encoder precision',\n    category: ['Inference'],\n    type: 'combo',\n    options: [\n      FloatingPointPrecision.AUTO,\n      FloatingPointPrecision.FP8E4M3FN,\n      FloatingPointPrecision.FP8E5M2,\n      FloatingPointPrecision.FP16,\n      FloatingPointPrecision.FP32\n    ],\n    defaultValue: FloatingPointPrecision.AUTO,\n    tooltip: 'Text Encoder precision',\n    getValue: (value: FloatingPointPrecision) => {\n      switch (value) {\n        case FloatingPointPrecision.AUTO:\n          return {}\n        default:\n          return {\n            [`${value.toLowerCase()}-text-enc`]: true\n          }\n      }\n    }\n  },\n\n  // Memory and performance settings\n  {\n    id: 'force-channels-last',\n    name: 'Force channels-last memory format',\n    category: ['Memory'],\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'directml',\n    name: 'DirectML device index',\n    category: ['Memory'],\n    type: 'number',\n    defaultValue: null\n  },\n  {\n    id: 'disable-ipex-optimize',\n    name: 'Disable IPEX optimization',\n    category: ['Memory'],\n    type: 'boolean',\n    defaultValue: false\n  },\n\n  // Preview settings\n  {\n    id: 'preview-method',\n    name: 'Method used for latent previews',\n    category: ['Preview'],\n    type: 'combo',\n    options: Object.values(LatentPreviewMethod),\n    defaultValue: LatentPreviewMethod.NoPreviews\n  },\n  {\n    id: 'preview-size',\n    name: 'Size of preview images',\n    category: ['Preview'],\n    type: 'slider',\n    defaultValue: 512,\n    attrs: {\n      min: 128,\n      max: 2048,\n      step: 128\n    }\n  },\n\n  // Cache settings\n  {\n    id: 'cache-classic',\n    name: 'Use classic cache system',\n    category: ['Cache'],\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'cache-lru',\n    name: 'Use LRU caching with a maximum of N node results cached.',\n    category: ['Cache'],\n    type: 'number',\n    defaultValue: null,\n    tooltip: 'May use more RAM/VRAM.'\n  },\n\n  // Attention settings\n  {\n    id: 'cross-attention-method',\n    name: 'Cross attention method',\n    category: ['Attention'],\n    type: 'combo',\n    options: Object.values(CrossAttentionMethod),\n    defaultValue: CrossAttentionMethod.Auto,\n    getValue: (value: CrossAttentionMethod) => {\n      switch (value) {\n        case CrossAttentionMethod.Auto:\n          return {}\n        default:\n          return {\n            [`use-${value.toLowerCase()}-cross-attention`]: true\n          }\n      }\n    }\n  },\n  {\n    id: 'disable-xformers',\n    name: 'Disable xFormers optimization',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'force-upcast-attention',\n    name: 'Force attention upcast',\n    category: ['Attention'],\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'dont-upcast-attention',\n    name: 'Prevent attention upcast',\n    category: ['Attention'],\n    type: 'boolean',\n    defaultValue: false\n  },\n\n  // VRAM management\n  {\n    id: 'vram-management',\n    name: 'VRAM management mode',\n    category: ['Memory'],\n    type: 'combo',\n    options: Object.values(VramManagement),\n    defaultValue: VramManagement.Auto,\n    getValue: (value: VramManagement) => {\n      switch (value) {\n        case VramManagement.Auto:\n          return {}\n        default:\n          return {\n            [value]: true\n          }\n      }\n    }\n  },\n  {\n    id: 'reserve-vram',\n    name: 'Reserved VRAM (GB)',\n    category: ['Memory'],\n    type: 'number',\n    defaultValue: null,\n    tooltip:\n      'Set the amount of vram in GB you want to reserve for use by your OS/other software. By default some amount is reverved depending on your OS.'\n  },\n\n  // Misc settings\n  {\n    id: 'default-hashing-function',\n    name: 'Default hashing function for model files',\n    type: 'combo',\n    options: Object.values(HashFunction),\n    defaultValue: HashFunction.SHA256\n  },\n  {\n    id: 'disable-smart-memory',\n    name: 'Disable smart memory management',\n    tooltip:\n      'Force ComfyUI to aggressively offload to regular ram instead of keeping models in vram when it can.',\n    category: ['Memory'],\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'deterministic',\n    name: 'Make pytorch use slower deterministic algorithms when it can.',\n    type: 'boolean',\n    defaultValue: false,\n    tooltip: 'Note that this might not make images deterministic in all cases.'\n  },\n  {\n    id: 'fast',\n    name: 'Enable some untested and potentially quality deteriorating optimizations.',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'dont-print-server',\n    name: \"Don't print server output to console.\",\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'disable-metadata',\n    name: 'Disable saving prompt metadata in files.',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'disable-all-custom-nodes',\n    name: 'Disable loading all custom nodes.',\n    type: 'boolean',\n    defaultValue: false\n  },\n  {\n    id: 'log-level',\n    name: 'Logging verbosity level',\n    type: 'combo',\n    options: Object.values(LogLevel),\n    defaultValue: LogLevel.INFO,\n    getValue: (value: LogLevel) => {\n      return {\n        verbose: value\n      }\n    }\n  },\n  // Directories\n  {\n    id: 'input-directory',\n    name: 'Input directory',\n    category: ['Directories'],\n    type: 'text',\n    defaultValue: ''\n  },\n  {\n    id: 'output-directory',\n    name: 'Output directory',\n    category: ['Directories'],\n    type: 'text',\n    defaultValue: ''\n  }\n]\n", "import { api } from '@/scripts/api'\nimport { app } from '@/scripts/app'\nimport {\n  useQueuePendingTaskCountStore,\n  useQueueSettingsStore\n} from '@/stores/queueStore'\n\nexport function setupAutoQueueHandler() {\n  const queueCountStore = useQueuePendingTaskCountStore()\n  const queueSettingsStore = useQueueSettingsStore()\n\n  let graphHasChanged = false\n  let internalCount = 0 // Use an internal counter here so it is instantly updated when re-queuing\n  api.addEventListener('graphChanged', () => {\n    if (queueSettingsStore.mode === 'change') {\n      if (internalCount) {\n        graphHasChanged = true\n      } else {\n        graphHasChanged = false\n        // Queue the prompt in the background\n        void app.queuePrompt(0, queueSettingsStore.batchCount)\n        internalCount++\n      }\n    }\n  })\n\n  queueCountStore.$subscribe(\n    async () => {\n      internalCount = queueCountStore.count\n      if (!internalCount && !app.lastExecutionError) {\n        if (\n          queueSettingsStore.mode === 'instant' ||\n          (queueSettingsStore.mode === 'change' && graphHasChanged)\n        ) {\n          graphHasChanged = false\n          await app.queuePrompt(0, queueSettingsStore.batchCount)\n        }\n      }\n    },\n    { detached: true }\n  )\n}\n", "<template>\n  <div class=\"comfyui-body grid h-full w-full overflow-hidden\">\n    <div id=\"comfyui-body-top\" class=\"comfyui-body-top\">\n      <TopMenubar v-if=\"useNewMenu === 'Top'\" />\n    </div>\n    <div id=\"comfyui-body-bottom\" class=\"comfyui-body-bottom\">\n      <TopMenubar v-if=\"useNewMenu === 'Bottom'\" />\n    </div>\n    <div id=\"comfyui-body-left\" class=\"comfyui-body-left\" />\n    <div id=\"comfyui-body-right\" class=\"comfyui-body-right\" />\n    <div id=\"graph-canvas-container\" class=\"graph-canvas-container\">\n      <GraphCanvas @ready=\"onGraphReady\" />\n    </div>\n  </div>\n\n  <GlobalToast />\n  <RerouteMigrationToast />\n  <UnloadWindowConfirmDialog v-if=\"!isElectron()\" />\n  <MenuHamburger />\n</template>\n\n<script setup lang=\"ts\">\nimport { useEventListener } from '@vueuse/core'\nimport type { ToastMessageOptions } from 'primevue/toast'\nimport { useToast } from 'primevue/usetoast'\nimport { computed, onBeforeUnmount, onMounted, watch, watchEffect } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport MenuHamburger from '@/components/MenuHamburger.vue'\nimport UnloadWindowConfirmDialog from '@/components/dialog/UnloadWindowConfirmDialog.vue'\nimport GraphCanvas from '@/components/graph/GraphCanvas.vue'\nimport GlobalToast from '@/components/toast/GlobalToast.vue'\nimport RerouteMigrationToast from '@/components/toast/RerouteMigrationToast.vue'\nimport TopMenubar from '@/components/topbar/TopMenubar.vue'\nimport { useBrowserTabTitle } from '@/composables/useBrowserTabTitle'\nimport { useCoreCommands } from '@/composables/useCoreCommands'\nimport { useErrorHandling } from '@/composables/useErrorHandling'\nimport { useProgressFavicon } from '@/composables/useProgressFavicon'\nimport { SERVER_CONFIG_ITEMS } from '@/constants/serverConfig'\nimport { i18n } from '@/i18n'\nimport { StatusWsMessageStatus } from '@/schemas/apiSchema'\nimport { api } from '@/scripts/api'\nimport { app } from '@/scripts/app'\nimport { setupAutoQueueHandler } from '@/services/autoQueueService'\nimport { useKeybindingService } from '@/services/keybindingService'\nimport { useCommandStore } from '@/stores/commandStore'\nimport { useExecutionStore } from '@/stores/executionStore'\nimport { useMenuItemStore } from '@/stores/menuItemStore'\nimport { useModelStore } from '@/stores/modelStore'\nimport { useNodeDefStore, useNodeFrequencyStore } from '@/stores/nodeDefStore'\nimport {\n  useQueuePendingTaskCountStore,\n  useQueueStore\n} from '@/stores/queueStore'\nimport { useServerConfigStore } from '@/stores/serverConfigStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport { useBottomPanelStore } from '@/stores/workspace/bottomPanelStore'\nimport { useColorPaletteStore } from '@/stores/workspace/colorPaletteStore'\nimport { useSidebarTabStore } from '@/stores/workspace/sidebarTabStore'\nimport { useWorkspaceStore } from '@/stores/workspaceStore'\nimport { electronAPI, isElectron } from '@/utils/envUtil'\n\nsetupAutoQueueHandler()\nuseProgressFavicon()\nuseBrowserTabTitle()\n\nconst { t } = useI18n()\nconst toast = useToast()\nconst settingStore = useSettingStore()\nconst executionStore = useExecutionStore()\nconst colorPaletteStore = useColorPaletteStore()\nconst queueStore = useQueueStore()\n\nwatch(\n  () => colorPaletteStore.completedActivePalette,\n  (newTheme) => {\n    const DARK_THEME_CLASS = 'dark-theme'\n    if (newTheme.light_theme) {\n      document.body.classList.remove(DARK_THEME_CLASS)\n    } else {\n      document.body.classList.add(DARK_THEME_CLASS)\n    }\n\n    if (isElectron()) {\n      electronAPI().changeTheme({\n        color: 'rgba(0, 0, 0, 0)',\n        symbolColor: newTheme.colors.comfy_base['input-text']\n      })\n    }\n  },\n  { immediate: true }\n)\n\nif (isElectron()) {\n  watch(\n    () => queueStore.tasks,\n    (newTasks, oldTasks) => {\n      // Report tasks that previously running but are now completed (i.e. in history)\n      const oldRunningTaskIds = new Set(\n        oldTasks.filter((task) => task.isRunning).map((task) => task.promptId)\n      )\n      newTasks\n        .filter(\n          (task) => oldRunningTaskIds.has(task.promptId) && task.isHistory\n        )\n        .forEach((task) => {\n          electronAPI().Events.incrementUserProperty(\n            `execution:${task.displayStatus.toLowerCase()}`,\n            1\n          )\n          electronAPI().Events.trackEvent('execution', {\n            status: task.displayStatus.toLowerCase()\n          })\n        })\n    },\n    { deep: true }\n  )\n}\n\nwatchEffect(() => {\n  const fontSize = settingStore.get('Comfy.TextareaWidget.FontSize')\n  document.documentElement.style.setProperty(\n    '--comfy-textarea-font-size',\n    `${fontSize}px`\n  )\n})\n\nwatchEffect(() => {\n  const padding = settingStore.get('Comfy.TreeExplorer.ItemPadding')\n  document.documentElement.style.setProperty(\n    '--comfy-tree-explorer-item-padding',\n    `${padding}px`\n  )\n})\n\nwatchEffect(() => {\n  const locale = settingStore.get('Comfy.Locale')\n  if (locale) {\n    i18n.global.locale.value = locale as 'en' | 'zh' | 'ru' | 'ja'\n  }\n})\n\nconst useNewMenu = computed(() => {\n  return settingStore.get('Comfy.UseNewMenu')\n})\nwatchEffect(() => {\n  if (useNewMenu.value === 'Disabled') {\n    app.ui.menuContainer.style.setProperty('display', 'block')\n    app.ui.restoreMenuPosition()\n  } else {\n    app.ui.menuContainer.style.setProperty('display', 'none')\n  }\n})\n\nwatchEffect(() => {\n  queueStore.maxHistoryItems = settingStore.get('Comfy.Queue.MaxHistoryItems')\n})\n\nconst init = () => {\n  const coreCommands = useCoreCommands()\n  useCommandStore().registerCommands(coreCommands)\n  useMenuItemStore().registerCoreMenuCommands()\n  useKeybindingService().registerCoreKeybindings()\n  useSidebarTabStore().registerCoreSidebarTabs()\n  useBottomPanelStore().registerCoreBottomPanelTabs()\n  app.extensionManager = useWorkspaceStore()\n}\n\nconst queuePendingTaskCountStore = useQueuePendingTaskCountStore()\nconst onStatus = async (e: CustomEvent<StatusWsMessageStatus>) => {\n  queuePendingTaskCountStore.update(e)\n  await queueStore.update()\n}\n\nconst reconnectingMessage: ToastMessageOptions = {\n  severity: 'error',\n  summary: t('g.reconnecting')\n}\n\nconst onReconnecting = () => {\n  if (!settingStore.get('Comfy.Toast.DisableReconnectingToast')) {\n    toast.remove(reconnectingMessage)\n    toast.add(reconnectingMessage)\n  }\n}\n\nconst onReconnected = () => {\n  if (!settingStore.get('Comfy.Toast.DisableReconnectingToast')) {\n    toast.remove(reconnectingMessage)\n    toast.add({\n      severity: 'success',\n      summary: t('g.reconnected'),\n      life: 2000\n    })\n  }\n}\n\nonMounted(() => {\n  api.addEventListener('status', onStatus)\n  api.addEventListener('reconnecting', onReconnecting)\n  api.addEventListener('reconnected', onReconnected)\n  executionStore.bindExecutionEvents()\n\n  try {\n    init()\n  } catch (e) {\n    console.error('Failed to init ComfyUI frontend', e)\n  }\n})\n\nonBeforeUnmount(() => {\n  api.removeEventListener('status', onStatus)\n  api.removeEventListener('reconnecting', onReconnecting)\n  api.removeEventListener('reconnected', onReconnected)\n  executionStore.unbindExecutionEvents()\n})\n\nuseEventListener(window, 'keydown', useKeybindingService().keybindHandler)\n\nconst { wrapWithErrorHandling, wrapWithErrorHandlingAsync } = useErrorHandling()\nconst onGraphReady = () => {\n  requestIdleCallback(\n    () => {\n      // Setting values now available after comfyApp.setup.\n      // Load keybindings.\n      wrapWithErrorHandling(useKeybindingService().registerUserKeybindings)()\n\n      // Load server config\n      wrapWithErrorHandling(useServerConfigStore().loadServerConfig)(\n        SERVER_CONFIG_ITEMS,\n        settingStore.get('Comfy.Server.ServerConfigValues')\n      )\n\n      // Load model folders\n      void wrapWithErrorHandlingAsync(useModelStore().loadModelFolders)()\n\n      // Non-blocking load of node frequencies\n      void wrapWithErrorHandlingAsync(\n        useNodeFrequencyStore().loadNodeFrequencies\n      )()\n\n      // Node defs now available after comfyApp.setup.\n      // Explicitly initialize nodeSearchService to avoid indexing delay when\n      // node search is triggered\n      useNodeDefStore().nodeSearchService.searchNode('')\n    },\n    { timeout: 1000 }\n  )\n}\n</script>\n\n<style scoped>\n.comfyui-body {\n  grid-template-columns: auto 1fr auto;\n  grid-template-rows: auto 1fr auto;\n}\n\n/**\n  +------------------+------------------+------------------+\n  |                                                        |\n  |  .comfyui-body-                                        |\n  |       top                                              |\n  | (spans all cols)                                       |\n  |                                                        |\n  +------------------+------------------+------------------+\n  |                  |                  |                  |\n  | .comfyui-body-   |   #graph-canvas  | .comfyui-body-   |\n  |      left        |                  |      right       |\n  |                  |                  |                  |\n  |                  |                  |                  |\n  +------------------+------------------+------------------+\n  |                                                        |\n  |  .comfyui-body-                                        |\n  |      bottom                                            |\n  | (spans all cols)                                       |\n  |                                                        |\n  +------------------+------------------+------------------+\n*/\n\n.comfyui-body-top {\n  order: -5;\n  /* Span across all columns */\n  grid-column: 1/-1;\n  /* Position at the first row */\n  grid-row: 1;\n  /* Top menu bar dropdown needs to be above of graph canvas splitter overlay which is z-index: 999 */\n  /* Top menu bar z-index needs to be higher than bottom menu bar z-index as by default\n  pysssss's image feed is located at body-bottom, and it can overlap with the queue button, which\n  is located in body-top. */\n  z-index: 1001;\n  display: flex;\n  flex-direction: column;\n}\n\n.comfyui-body-left {\n  order: -4;\n  /* Position in the first column */\n  grid-column: 1;\n  /* Position below the top element */\n  grid-row: 2;\n  z-index: 10;\n  display: flex;\n}\n\n.graph-canvas-container {\n  width: 100%;\n  height: 100%;\n  order: -3;\n  grid-column: 2;\n  grid-row: 2;\n  position: relative;\n  overflow: hidden;\n}\n\n.comfyui-body-right {\n  order: -2;\n  z-index: 10;\n  grid-column: 3;\n  grid-row: 2;\n}\n\n.comfyui-body-bottom {\n  order: 4;\n  /* Span across all columns */\n  grid-column: 1/-1;\n  grid-row: 3;\n  /* Bottom menu bar dropdown needs to be above of graph canvas splitter overlay which is z-index: 999 */\n  z-index: 1000;\n  display: flex;\n  flex-direction: column;\n}\n</style>\n"], "names": ["_", "t", "comfyApp", "LinkReleaseTriggerMode", "LinkReleaseTriggerAction", "_sfc_main", "AutoComplete", "node", "options", "widget", "ContextMenu", "clamp", "LatentPreviewMethod", "LogLevel", "HashFunction", "AutoLaunch", "CudaMalloc", "FloatingPointPrecision", "CrossAttentionMethod", "VramManagement"], "mappings": ";;;;;;;;;;;;;AA8BA,UAAM,iBAAiB;AACvB,UAAM,eAAe;AACrB,UAAM,gBAAgB,6BAAM;AAC1B,qBAAe,YAAY;AAAA,IAAA,GADP;AAItB,gBAAY,MAAM;AAChB,UAAI,aAAa,IAAI,kBAAkB,MAAM,YAAY;AACvD;AAAA,MACF;AACA,UAAI,eAAe,WAAW;AACxB,YAAA,GAAG,cAAc,MAAM,UAAU;AAAA,MAAA,OAChC;AACD,YAAA,GAAG,cAAc,MAAM,UAAU;AAAA,MACvC;AAAA,IAAA,CACD;AAED,UAAM,cAAc,SAAS,MAAM,aAAa,IAAI,kBAAkB,CAAC;AACvE,UAAM,cAAc;AAAA,MAAwB;AAAA;AAAA;AAAA,QAG1C,YAAY,UAAU,WAClB,EAAE,QAAQ,OAAO,OAAO,MAAA,IACxB,EAAE,KAAK,OAAO,OAAO,MAAM;AAAA;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpCjC,UAAM,eAAe;AACrB,UAAM,gBAAgB;AAEhB,UAAA,qBAAqB,wBAAC,UAA6B;AACvD,UACE,aAAa,IAAI,iCAAiC,KAClD,cAAc,kBAAkB,SAAS,GACzC;AACA,cAAM,eAAe;AACd,eAAA;AAAA,MACT;AACO,aAAA;AAAA,IAAA,GARkB;AAW3B,cAAU,MAAM;AACP,aAAA,iBAAiB,gBAAgB,kBAAkB;AAAA,IAAA,CAC3D;AAED,oBAAgB,MAAM;AACb,aAAA,oBAAoB,gBAAgB,kBAAkB;AAAA,IAAA,CAC9D;;;;;;;;;ACmBD,UAAM,eAAe;AACrB,UAAM,kBAAkB;AAAA,MAA2B,MACjD,aAAa,IAAI,wBAAwB;AAAA,IAAA;AAG3C,UAAM,eAAe;AAAA,MAAS,MAC5B,aAAa,IAAI,4BAA4B;AAAA,IAAA;AAG/C,UAAM,sBAAsB;AAAA,MAC1B,MAAM,mBAAmB,EAAE,qBAAqB;AAAA,IAAA;AAElD,UAAM,qBAAqB;AAAA,MACzB,MAAM,oBAAsB,EAAA;AAAA,IAAA;AAE9B,UAAM,qBAAqB;AAAA,MACzB,MAAM,mBAAqB,EAAA;AAAA,IAAA;AAGvB,UAAA,kBAAkB,SAAS,MAAM;AACrC,aAAO,aAAa,QAAQ,oBAAoB,mBAAmB,SAAS;AAAA,IAAA,CAC7E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxDD,UAAM,QAAQ;AAIR,UAAA,uBAAuB,wBAAC,WAA4B,OAAoB;AAC5E,gBAAU,OAAO,EAAE;AAAA,IAAA,GADQ;AAI7B,oBAAgB,MAAM;AACpB,UAAI,MAAM,UAAU,SAAS,YAAY,MAAM,UAAU,SAAS;AAChE,cAAM,UAAU;MAClB;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;ACiBD,UAAM,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzBzB,UAAM,kBAAkB;AACxB,UAAM,gBAAgB;AAEtB,UAAM,eAAe,SAAS,MAAM,cAAc,gBAAgB,QAAQ;AAEpE,UAAA,QAAQ,SAAS,MAAM;AAC3B,UAAI,CAAC,cAAc,iBAAiB,eAAe,CAAA;AAEnD,aAAO,cAAc,iBAAiB,IAAc,CAAC,UAAU;AAAA,QAC7D,OAAO;AAAA,QACP,SAAS,mCAAY;AACb,gBAAA,WAAW,cAAc,kBAAkB,IAAI;AACrD,cAAI,SAAU,OAAM,gBAAgB,aAAa,QAAQ;AAAA,QAC3D,GAHS;AAAA,MAIT,EAAA;AAAA,IAAA,CACH;AAEK,UAAA,OAAO,SAAS,OAAO;AAAA,MAC3B,OAAO,aAAa;AAAA,MACpB,MAAM;AAAA,MACN,SAAS,mCAAY;AACb,cAAA,SAAS,iBAAiB;AAChC,YAAI,CAAC,OAAO,MAAa,OAAA,IAAI,UAAU,qBAAqB;AAErD,eAAA,SAAS,OAAO,MAAM,SAAS;AAAA,MACxC,GALS;AAAA,IAMT,EAAA;AAEI,UAAA,kBAAkB,wBAAC,UAAgC;AACjD,YAAA,KAAK,UAAU,KAAK;AAAA,IAAA,GADJ;AAIxB;AAAA,MACE,MAAM,eAAiB,EAAA;AAAA,MACvB,CAAC,WAAW;AACO,yBAAA,OAAO,QAAQ,uBAAuB,MAAM;AAC3D,2BAAA,EAAmB;QAAkB,CACtC;AAAA,MACH;AAAA,IAAA;;;;;;;;;;;;;;AChDc,SAAA,oBAAoB,UAAsC,IAAI;AACtE,QAAA,EAAE,eAAe,MAAU,IAAA;AAEjC,QAAM,cAAc;AACd,QAAA,WAAW,YAAY;AACvB,QAAA,EAAE,yBAAyB;AAAA,IAC/B,SAAS;AAAA,IACT;AAAA,EAAA;AAQI,QAAA,QAAQ,IAAmB,CAAA,CAAE;AAO7B,QAAA,eAAe,wBAAC,aAA4C;AAChE,UAAM,EAAE,KAAK,MAAM,QAAQ,SAAS,GAAG,MAAU,IAAA;AACjD,UAAM,CAAC,MAAM,GAAG,IAAI,qBAAqB,GAAG;AACtC,UAAA,CAAC,OAAO,MAAM,IAAI;AAExB,WAAO,eACH;AAAA,MACE,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,WAAW,SAAS,KAAK;AAAA,MACzB,MAAM,GAAG,IAAI;AAAA,MACb,KAAK,GAAG,GAAG;AAAA,MACX,OAAO,GAAG,KAAK;AAAA,MACf,QAAQ,GAAG,MAAM;AAAA,IAAA,IAEnB;AAAA,MACE,UAAU;AAAA,MACV,MAAM,GAAG,IAAI;AAAA,MACb,KAAK,GAAG,GAAG;AAAA,MACX,OAAO,GAAG,QAAQ,KAAK;AAAA,MACvB,QAAQ,GAAG,SAAS,KAAK;AAAA,IAAA;AAAA,EAC3B,GArBe;AA6Bf,QAAA,iBAAiB,wBAAC,WAA2B;AAC3C,UAAA,QAAQ,aAAa,MAAM;AAAA,EAAA,GADZ;AAIhB,SAAA;AAAA,IACL;AAAA,IACA;AAAA,EAAA;AAEJ;AA3DgB;ACHhB,SAAS,UAAU,GAAS,GAAkD;AAC5E,QAAM,KAAK,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC;AAC5B,QAAM,KAAK,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC;AACtB,QAAA,KAAK,KAAK,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK;AAC1C,QAAA,KAAK,KAAK,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM;AAE9C,MAAA,MAAM,MAAM,MAAM,IAAI;AACjB,WAAA;AAAA,EACT;AAEA,SAAO,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE;AAClC;AAXS;AAiBF,MAAM,iBAAiB,wBAAC,UAA2B,OAAO;AACzD,QAAA,QAAQ,IAAmB,CAAA,CAAE;AAC7B,QAAA,EAAE,SAAS,EAAM,IAAA;AAKvB,QAAM,oBAAoB,wBACxB,aACA,YACA,YACA,iBAQW;AACP,QAAA,CAAC,cAAc,cAAc;AACzB,YAAA,EAAE,OAAO,OAAW,IAAA;AAG1B,YAAM,eAAe;AAAA,QACnB;AAAA,UACE,GAAG,YAAY,OAAO,WAAW;AAAA,UACjC,GAAG,YAAY,MAAM,WAAW;AAAA,UAChC,OAAO,YAAY;AAAA,UACnB,QAAQ,YAAY;AAAA,QACtB;AAAA,QACA;AAAA,UACE,IAAI,aAAa,IAAI,OAAO,CAAC,IAAI,UAAU;AAAA,UAC3C,IAAI,aAAa,IAAI,OAAO,CAAC,IAAI,UAAU;AAAA,UAC3C,QAAQ,aAAa,QAAQ,IAAI,UAAU;AAAA,UAC3C,SAAS,aAAa,SAAS,IAAI,UAAU;AAAA,QAC/C;AAAA,MAAA;AAGF,UAAI,CAAC,cAAc;AACV,eAAA;AAAA,MACT;AAGM,YAAA,SACH,aAAa,CAAC,IAAI,YAAY,OAAO,WAAW,QAAQ,QAAQ;AAC7D,YAAA,SACH,aAAa,CAAC,IAAI,YAAY,MAAM,WAAW,OAAO,QAAQ;AACjE,YAAM,YAAY,aAAa,CAAC,IAAI,QAAQ;AAC5C,YAAM,aAAa,aAAa,CAAC,IAAI,QAAQ;AAEtC,aAAA,2BAA2B,KAAK,UAAU,KAAK,IAAI,KAAK,UAAU,KAAK,MAAM,SAAS,KAAK,KAAK,UAAU,KAAK,MAAM,SAAS,UAAU,KAAK,MAAM,UAAU,MAAM,KAAK,SAAS,KAAK,MAAM,UAAU,MAAM,KAAK;AAAA,IAC1N;AAEO,WAAA;AAAA,EAAA,GA/CiB;AAqD1B,QAAM,iBAAiB,wBACrB,SACA,eACA,YACA,iBAQG;AACG,UAAA,cAAc,QAAQ;AACtB,UAAA,aAAa,cAAc;AAEjC,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAGF,UAAM,QAAQ;AAAA,MACZ,UAAU,YAAY;AAAA,MACtB,YAAY;AAAA,IAAA;AAAA,EACd,GA1BqB;AA6BhB,SAAA;AAAA,IACL;AAAA,IACA;AAAA,EAAA;AAEJ,GA7F8B;;;;;;;;;ACIxB,UAAA,SAAS,QAAW,YAAC;AAE3B,UAAM,OAAO;AAIb,UAAM,gBAAgB;AAOhB,UAAA,QAAQ,IAAmB,CAAA,CAAE;AACnC,UAAM,EAAE,OAAO,eAAe,eAAA,IAAmB,oBAAoB;AAAA,MACnE,cAAc;AAAA,IAAA,CACf;AACD,UAAM,EAAE,OAAO,eAAe,mBAAmB,eAAe;AAEhE,UAAM,cAAc;AACpB,UAAM,eAAe;AACrB,UAAM,oBAAoB;AAAA,MAAS,MACjC,aAAa,IAAI,0BAA0B;AAAA,IAAA;AAG7C,UAAM,oBAAoB,6BAAM;AAC9B,YAAM,WAAW,YAAY;AAC7B,UAAI,CAAC,YAAY,CAAC,cAAc,MAAO;AAEjC,YAAA,eAAe,OAAO,OAAO,SAAS,kBAAkB,CAAE,CAAA,EAAE,CAAC;AACnE,UAAI,CAAC,aAAc;AAEnB,YAAM,OAAO,OAAO;AACpB,YAAM,aAAa,iBAAiB;AACpC,YAAM,aAAa,cAAc;AAC3B,YAAA,SAAS,SAAS,GAAG;AACrB,YAAA,QAAQ,SAAS,GAAG;AAC1B,YAAM,qBAAqB,aACvB;AAAA,QACE,GAAG,WAAW,CAAC;AAAA,QACf,GAAG,WAAW,CAAC;AAAA,QACf,OAAO,WAAW,CAAC;AAAA,QACnB,QAAQ,WAAW,CAAC;AAAA,QACpB;AAAA,QACA,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MAE/B,IAAA;AAEJ;AAAA,QACE,cAAc;AAAA,QACd,SAAS;AAAA,QACT;AAAA,QACA;AAAA,MAAA;AAAA,IACF,GA5BwB;AAoCpB,UAAA,EAAE,MAAM,QAAQ,mBAAmB,YAAY,UAAA,EAAY,MAAM;AACvE;AAAA,MACE,CAAC,MAAM,QAAW,aAAE,MAAM,GAAG;AAAA,MAC7B,CAAC,CAAC,aAAaA,IAAG,EAAE,MAAM;AACxB,uBAAe,WAAW;AAC1B,YAAI,kBAAkB,OAAO;AACT;QACpB;AAEA,cAAM,QAAQ;AAAA,UACZ,GAAG,cAAc;AAAA,UACjB,GAAI,kBAAkB,QAAQ,cAAc,QAAQ,CAAC;AAAA,UACrD,QAAQ,YAAY;AAAA,UACpB,eACE,YAAY,YAAY,OAAO,mBAAmB,SAAS;AAAA,UAC7D,SAAS,OAAO,mBAAmB,MAAM;AAAA,QAAA;AAAA,MAE7C;AAAA,MACA,EAAE,MAAM,KAAK;AAAA,IAAA;AAGf;AAAA,MACE,MAAM,QAAW,YAAC;AAAA,MAClB,CAAC,YAAY,eAAe;AACtB,YAAA,CAAC,cAAc,YAAY;AACtB,iBAAA,QAAQ,SAAS,MAAM;AAAA,QAChC;AAAA,MACF;AAAA,IAAA;AAGE,QAAA,YAAY,MAAM,GAAG;AACnB,UAAA,OAAO,QAAQ,MAAM;AACN,yBAAA,UAAU,aAAa,CAAC,UAAU;AACjD,cAAI,CAAC,OAAO,QAAQ,SAAS,MAAM,MAAqB,GAAG;AACzD,mBAAO,QAAQ;UACjB;AAAA,QAAA,CACD;AAAA,MACH;AAEA,iBAAW,OAAO,OAAO,QAAQ,YAAY,CAAC,SAAS,OAAO,GAAG;AAC9C,yBAAA,OAAO,SAAS,KAAK,MAAM;AAC1C,gBAAM,WAAW,YAAY;AACnB,oBAAA,WAAW,OAAO,IAAI;AACtB,oBAAA,aAAa,OAAO,IAAI;AAAA,QAAA,CACnC;AAAA,MACH;AAAA,IACF;AAEM,UAAA,YAAY,OAAO,KAAK,YAAY;AAC1C,UAAM,UAAU,WAAW,SAAS,OAAO,IAAI,GAAG;AAElD,cAAU,MAAM;AACd,UAAI,YAAY,MAAM,KAAK,cAAc,OAAO;AAChC,sBAAA,MAAM,YAAY,OAAO,OAAO;AAAA,MAChD;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/HD,UAAM,iBAAiB;AACvB,UAAM,eAAe;AAAA,MAAS,MAC5B,MAAM,KAAK,eAAe,aAAa,QAAQ;AAAA,IAAA;AAGjD,UAAM,gBAAgB,6BAAM;AAC1B,YAAM,WAAW,YAAY;AAC7B,UAAI,CAAC,SAAU;AAEf,YAAM,aAAa,SAAS;AAC5B,iBAAW,eAAe,eAAe,aAAa,OAAA,GAAU;AAC9D,cAAM,SAAS,YAAY;AAC3B,cAAM,OAAO,OAAO;AAEd,cAAA,UACJ,SAAS,cAAc,IAAI,KAC3B,EAAE,OAAO,QAAQ,cAAc,eAC/B,OAAO,UAAU;AAEnB,oBAAY,UAAU;AACtB,YAAI,SAAS;AACX,gBAAM,SAAS,OAAO;AACtB,sBAAY,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,QAAQ,KAAK,IAAI,CAAC,IAAI,SAAS,OAAO,CAAC;AACxE,sBAAY,OAAO;AAAA,aAChB,OAAO,SAAS,KAAK,SAAS,SAAS;AAAA,aACvC,OAAO,kBAAkB,MAAM,SAAS;AAAA,UAAA;AAG3C,sBAAY,SAAS,SAAS,OAAO,MAAM,QAAQ,IAAI,KAAK;AAC5D,sBAAY,WAAW,SAAS;AAAA,QAClC;AAAA,MACF;AAAA,IAAA,GA1BoB;AA6BtB,UAAM,cAAc;AACpB;AAAA,MACE,MAAM,YAAY;AAAA,MAClB,CAAC,WACE,OAAO,mBAAmB;AAAA,QACzB,OAAO;AAAA,QACP;AAAA,MACF;AAAA,MACF,EAAE,WAAW,KAAK;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACQd,UAAA,EAAE,GAAAC,OAAM;AACd,UAAM,eAAe;AACrB,UAAM,cAAc;AACpB,UAAM,eAAe;AAErB,UAAM,aAAa;AAAA,MACjB,MAAM,aAAa,IAAI,sBAAsB,MAAM,UAAU;AAAA,IAAA;AAG/D,QAAI,WAA0B;AACxB,UAAA,SAAS,8BAAO,YAAoB;AACxC,UAAI,SAAU;AACd,YAAM,MAAM,6BAAM,aAAa,QAAQ,OAAO,GAAlC;AACZ,YAAM,IAAI;AACC,iBAAA,OAAO,YAAY,KAAK,GAAG;AAAA,IAAA,GAJzB;AAMf,UAAM,aAAa,6BAAM;AACvB,UAAI,UAAU;AACZ,sBAAc,QAAQ;AACX,mBAAA;AAAA,MACb;AAAA,IAAA,GAJiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7Df,QAAA;AACJ,UAAM,eAAe;AACrB,UAAM,eAAe;AACrB,UAAM,aAAa;AACb,UAAA,cAAc,IAAI,EAAE;AAC1B,UAAM,OAAO;AACb,UAAM,MAAM;AAEN,UAAA,cAAc,6BAAO,YAAY,QAAQ,IAA3B;AAEd,UAAA,cAAc,8BAAO,YAAuC;AAChE,UAAI,CAAC,QAAS;AAEd,WAAK,QAAQC,IAAS,OAAO,MAAM,CAAC,IAAI;AACxC,UAAI,QAAQA,IAAS,OAAO,MAAM,CAAC,IAAI;AACvC,kBAAY,QAAQ;AAEpB,YAAM,SAAS;AAET,YAAA,OAAO,WAAW,OAAO,sBAAsB;AACrD,UAAI,CAAC,KAAM;AAEP,UAAA,KAAK,QAAQ,OAAO,YAAY;AAClC,aAAK,QAAQA,IAAS,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ;AAAA,MACvD;AAEI,UAAA,KAAK,MAAM,GAAG;AAChB,YAAI,QAAQA,IAAS,OAAO,MAAM,CAAC,IAAI,KAAK,SAAS;AAAA,MACvD;AAAA,IAAA,GAlBkB;AAqBpB,UAAM,SAAS,6BAAM;AACb,YAAA,EAAE,OAAW,IAAAA;AACnB,YAAM,OAAO,OAAO;AACpB,UAAI,CAAC,KAAM;AAEX,YAAM,OAAO,KAAK;AAClB,YAAM,UAAU,aAAa,eAAe,KAAK,QAAQ,EAAE;AAGzD,UAAA,KAAK,eAAe,UAAU,YAC9B,OAAO,YAAY,CAAC,IAAI,KAAK,IAAI,CAAC,GAClC;AACO,eAAA,YAAY,QAAQ,WAAW;AAAA,MACxC;AAEI,UAAA,KAAK,OAAO,UAAW;AAE3B,YAAM,YAAY;AAAA,QAChB;AAAA,QACA,OAAO,YAAY,CAAC;AAAA,QACpB,OAAO,YAAY,CAAC;AAAA,QACpB,CAAC,GAAG,CAAC;AAAA,MAAA;AAEP,UAAI,cAAc,IAAI;AACpB,cAAM,YAAY,KAAK,OAAO,SAAS,EAAE;AACzC,cAAM,oBAAoB;AAAA,UACxB,YAAY,iBAAiB,KAAK,QAAQ,EAAE,CAAC,WAAW,iBAAiB,SAAS,CAAC;AAAA,UACnF,QAAQ,OAAO,SAAS,GAAG,WAAW;AAAA,QAAA;AAExC,eAAO,YAAY,iBAAiB;AAAA,MACtC;AAEA,YAAM,aAAa;AAAA,QACjB;AAAA,QACA,OAAO,YAAY,CAAC;AAAA,QACpB,OAAO,YAAY,CAAC;AAAA,QACpB,CAAC,GAAG,CAAC;AAAA,MAAA;AAEP,UAAI,eAAe,IAAI;AACrB,cAAM,oBAAoB;AAAA,UACxB,YAAY,iBAAiB,KAAK,QAAQ,EAAE,CAAC,YAAY,UAAU;AAAA,UACnE,QAAQ,QAAQ,UAAU,GAAG,WAAW;AAAA,QAAA;AAE1C,eAAO,YAAY,iBAAiB;AAAA,MACtC;AAEM,YAAA,SAASA,IAAS,OAAO,kBAAkB;AAEjD,UAAI,UAAU,CAAC,YAAY,MAAM,GAAG;AAClC,cAAM,oBAAoB;AAAA,UACxB,YAAY,iBAAiB,KAAK,QAAQ,EAAE,CAAC,WAAW,iBAAiB,OAAO,IAAI,CAAC;AAAA,UACrF,QAAQ,OAAO,OAAO,IAAI,GAAG,WAAW;AAAA,QAAA;AAGnC,eAAA,YAAY,OAAO,WAAW,iBAAiB;AAAA,MACxD;AAAA,IAAA,GAvDa;AA0DT,UAAA,cAAc,wBAAC,MAAkB;AACzB;AACZ,mBAAa,WAAW;AAEnB,UAAA,EAAE,OAAgB,aAAa,SAAU;AAC9C,oBAAc,OAAO;AAAA,QACnB;AAAA,QACA,aAAa,IAAI,6BAA6B;AAAA,MAAA;AAAA,IAChD,GARkB;AAWH,qBAAA,QAAQ,aAAa,WAAW;AAChC,qBAAA,QAAQ,SAAS,WAAW;;;;;;;;;;;;;;;;AC1G7C,UAAM,cAAc;AACpB,UAAM,EAAE,OAAO,eAAe,IAAI,oBAAoB;AAEhD,UAAA,UAAU,IAAI,KAAK;AACnB,UAAA,aAAa,IAAI,KAAK;AAE5B,UAAM,2BAA2B,6BAAM;AACrC,YAAM,EAAE,cAAA,IAAkB,YAAY,UAAU;AACrC,iBAAA,QAAQ,cAAc,OAAO;AAEpC,UAAA,CAAC,cAAc,MAAM;AACvB,gBAAQ,QAAQ;AAChB;AAAA,MACF;AAEA,cAAQ,QAAQ;AACV,YAAA,SAAS,aAAa,aAAa;AACzC,UAAI,QAAQ;AACK,uBAAA;AAAA,UACb,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,UAC1B,MAAM,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,QAAA,CAC5B;AAAA,MACH;AAAA,IAAA,GAhB+B;AAoBjC;AAAA,MACE,MAAM,YAAY,YAAY,MAAM;AAAA,MACpC,MAAM;AACJ,8BAAsB,MAAM;AACD;AACb,sBAAA,UAAY,EAAA,MAAM,mBAAmB;AAAA,QAAA,CAClD;AAAA,MACH;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IAAA;AAGR,gBAAA,UAAY,EAAA,GAAG,YAAY;AAEvC;AAAA,MACE,MAAM,YAAY,QAAQ,OAAO;AAAA,MACjC,CAAC,kBAAkB;AAKjB,YAAI,kBAAkB,OAAO;AAC3B,gCAAsB,MAAM;AAC1B,oBAAQ,QAAQ;AACS;UAAA,CAC1B;AAAA,QAAA,OACI;AAIL,gCAAsB,MAAM;AAC1B,oBAAQ,QAAQ;AAAA,UAAA,CACjB;AAAA,QACH;AAAA,MACF;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtDI,UAAA,EAAE,GAAAD,OAAM;AACd,UAAM,eAAe;AACrB,UAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0Bd,UAAA,EAAE,GAAAA,OAAM;AACd,UAAM,cAAc;AACpB,UAAM,oBAAoB;AAC1B,UAAM,gBAAgB;AACtB,UAAM,eAAe;AAAA,MACnB,MAAM,kBAAkB,uBAAuB;AAAA,IAAA;AAE3C,UAAA,oBAAoB,wBAAC,UACzB,YAAY,OAAO,EAAE,WAAW,KAAK,GADb;AAGpB,UAAA,kBAAkB,IAAI,KAAK;AAWjC,UAAM,kBAA+B;AAAA,MACnC,MAAM;AAAA,MACN,eAAeA,GAAE,eAAe;AAAA,MAChC,OAAO;AAAA,QACL,MAAM,UAAU;AAAA,QAChB,OAAO,kBAAkB,UAAU,oBAAoB;AAAA,MACzD;AAAA,IAAA;AAEF,UAAM,eAA8B;AAAA,MAClC;AAAA,MACA,GAAG,OAAO,QAAQ,aAAa,WAAW,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,OAAO;AAAA,QAClE;AAAA,QACA,eAAeA,GAAE,SAAS,IAAI,EAAE;AAAA,QAChC,OAAO;AAAA,UACL,MAAM,MAAM;AAAA,UACZ,OAAO,kBAAkB,MAAM,OAAO;AAAA,QACxC;AAAA,MAAA,EACA;AAAA,IAAA;AAGE,UAAA,sBAAsB,IAAwB,IAAI;AAClD,UAAA,aAAa,wBAAC,gBAAoC;AAChD,YAAA,YAAY,aAAa,QAAQ,gBAAgB;AACvD,YAAM,oBACJ,cAAc,gBAAgB,OAC1B,OACA,aAAa,YAAY,SAAS;AAE7B,iBAAA,QAAQ,YAAY,eAAe;AACxC,YAAA,YAAY,IAAI,GAAG;AACrB,eAAK,eAAe,iBAAiB;AAAA,QACvC;AAAA,MACF;AAEY,kBAAA,QAAQ,SAAS,MAAM,IAAI;AACvC,yBAAmB,QAAQ;AAC3B,sBAAgB,QAAQ;AACV,oBAAA,gBAAgB,cAAc;IAAW,GAhBtC;AAmBb,UAAA,qBAAqB,IAA8B,IAAI;AAC7D,UAAM,eAAe;AAAA,MAAS,MAC5B,mBAAmB,QACf,aAAa,QACX,kBAAkB,mBAAmB,OAAO,OAAO,IACnD,mBAAmB,OAAO,UAC5B;AAAA,IAAA;AAGN;AAAA,MACE,MAAM,YAAY;AAAA,MAClB,CAAC,qBAAqB;AACpB,wBAAgB,QAAQ;AACxB,4BAAoB,QAAQ;AACT,2BAAA,QAAQ,oBAAoB,gBAAgB;AAAA,MACjE;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChHI,UAAA,EAAE,GAAAA,OAAM;AACd,UAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACUf,UAAA,EAAE,GAAAA,OAAM;AACd,UAAM,cAAc;AACpB,UAAM,eAAe;AAEf,UAAA,SAAS,YAAY;AACrB,UAAA,gBAAgB,IAAI,KAAK;AAC/B,UAAM,sBAAsB;AAAA,MAC1B,MACE,YAAY,cAAc;AAAA,QACxB,CAAC,SAAS,aAAa,IAAI,KAAK,KAAK,YAAY,UAAU;AAAA,MAC7D;AAAA,IAAA;AAGJ,UAAM,aAAa,SAAS,MAAM,oBAAoB,MAAM,WAAW,CAAC;AAExE,aAAS,uBAAuC;AAC9C,UACE,KAAK,YACL,KAAK,YAAY,UAAU,eAC3B,cAAc,OACd;AACA,eAAO,EAAE,OAAO,UAAU,WAAW,GAAG,SAAS;MACnD;AAAA,IACF;AARS;AAUT,UAAM,mBAAmB,6BAAM;AAC7B,oBAAc,QAAQ;AACX,iBAAA,QAAQ,oBAAoB,OAAO;AACvC,aAAA,aAAa,YAAY,IAAI;AAAA,MACpC;AACA,aAAO,SAAS,IAAI;AAAA,IAAA,GALG;AAQzB,UAAM,mBAAmB,6BAAM;AAC7B,oBAAc,QAAQ;AACtB,aAAO,SAAS,IAAI;AAAA,IAAA,GAFG;AAKzB,UAAM,cAAc,mCAAY;AACxB,YAAA,aAAa,QAAQ,gCAAgC;AAAA,IAAA,GADzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3CpB,UAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;ACFrB,UAAM,eAAe;AACrB,UAAM,cAAc;AAEd,UAAA,oBAAoB,SAAS,MAAM;AACvC,YAAM,QAAQ,YAAY,cAAc,OAAO,YAAY;AAC3D,aAAO,MAAM,WAAW,KAAK,MAAM,KAAK,WAAW;AAAA,IAAA,CACpD;AAED,UAAM,iBAAiB,6BAAM;AACtB,WAAA,aAAa,QAAQ,iCAAiC;AAAA,IAAA,GADtC;;;;;;;;;;;;;;;;;;;;;;;;;;ACVjB,UAAA,EAAE,GAAAA,OAAM;AACd,UAAM,eAAe;AACrB,UAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;ACVpB,MAAM,sBAAsB,wBAC1B,WAEA,aAAa,UAAU,OAAO,OAAO,YAAY,YAHvB;AAQrB,MAAM,0BAA0B,6BAAM;AAC3C,QAAM,aAAa;AACb,QAAA,gBAAgB,IAAkB,CAAA,CAAE;AAE1C,cAAY,MAAM;AAChB,kBAAc,QAAQ,WAAW,cAAc,OAAO,YAAY;AAAA,EAAA,CACnE;AAED,QAAM,qBAAqB;AAAA,IAAS,MAClC,cAAc,MAAM;AAAA,MAClB,CAAC,SAAS,KAAK,SAAS,OAAO,mBAAmB,KAAK,CAAC;AAAA,IAC1D;AAAA,EAAA;AAGF,QAAM,gBAAgB,SAAS,MAAM,mBAAmB,MAAM,SAAS,CAAC;AAExE,iBAAe,kBAAkB;AAC3B,QAAA,CAAC,cAAc,MAAO;AAEpB,UAAA,QAAQ,IAAI,mBAAmB,MAAM,IAAI,CAAC,SAAS,KAAK,QAAS,CAAA,CAAC;AAAA,EAC1E;AAJe;AAMR,SAAA;AAAA,IACL;AAAA,IACA;AAAA,EAAA;AAEJ,GA1BuC;;;;ACNvC,UAAM,EAAE,eAAe,gBAAgB,IAAI,wBAAwB;;;;;;;;;;;;;;;;ACwBnE,UAAM,eAAe;AACrB,UAAM,cAAc;AACpB,UAAM,mBAAmB;AAEnB,UAAA,2BAA2B,SAA6B,MAAM;AAClE,YAAM,aAAa,IAAI;AAAA,QACrB,YAAY,cACT;AAAA,UACC,CAAC,SACC,iBACG,iBAAiB,+BAA+B,IAAI,EACpD,KAAK;AAAA,UAEX,KAAK;AAAA,MAAA;AAEV,aAAO,MAAM,KAAK,UAAU,EACzB,IAAI,CAAC,cAAc,aAAa,WAAW,SAAS,CAAC,EACrD,OAAO,CAAC,YAAyC,YAAY,MAAS;AAAA,IAAA,CAC1E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/BD,UAAM,eAAe;AAEf,UAAA,YAAY,IAAI,KAAK;AACrB,UAAA,cAAc,IAAI,EAAE;AAC1B,UAAM,EAAE,OAAO,oBAAoB,mBAAmB,oBAAoB;AACpE,UAAA,iBAAiB,IAAmB,CAAA,CAAE;AACtC,UAAA,aAAa,SAAwB,OAAO;AAAA,MAChD,GAAG,mBAAmB;AAAA,MACtB,GAAG,eAAe;AAAA,IAClB,EAAA;AAEF,UAAM,mBAAmB;AACzB,UAAM,cAAc;AACd,UAAA,0BAA0B,IAAI,IAAI;AAElC,UAAA,SAAS,wBAAC,aAAqB;AACnC,UAAI,iBAAiB,qBAAqB,SAAS,KAAA,MAAW,IAAI;AAC/C,yBAAA,kBAAkB,QAAQ,SAAS,KAAK;AACrD,YAAA,MAAM,eAAe,MAAM,IAAI;AAAA,MACrC;AACA,gBAAU,QAAQ;AAClB,uBAAiB,oBAAoB;AACzB,kBAAA,OAAQ,mBAAmB,wBAAwB;AAAA,IAAA,GAPlD;AAUf;AAAA,MACE,MAAM,iBAAiB;AAAA,MACvB,CAAC,WAAW;AACV,YAAI,WAAW,MAAM;AACnB;AAAA,QACF;AACA,oBAAY,QAAQ,OAAO;AAC3B,kBAAU,QAAQ;AAClB,cAAM,SAAS,YAAY;AAC3B,gCAAwB,QAAQ,OAAO;AACvC,eAAO,mBAAmB;AACpB,cAAA,QAAQ,OAAO,GAAG;AAExB,YAAI,kBAAkB,aAAa;AACjC,gBAAM,QAAQ;AACC,yBAAA;AAAA,YACb,KAAK,MAAM;AAAA,YACX,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,MAAM,WAAW;AAAA,UAAA,CACxC;AACD,yBAAe,QAAQ,EAAE,UAAU,GAAG,MAAM,YAAY,KAAK;QAAK,WACzD,kBAAkB,YAAY;AACvC,gBAAM,OAAO;AACb,gBAAM,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY;AACjB,yBAAA;AAAA,YACb,KAAK,CAAC,GAAG,CAAC;AAAA,YACV,MAAM,CAAC,KAAK,OAAO,UAAU,iBAAiB;AAAA,UAAA,CAC/C;AACD,yBAAe,QAAQ,EAAE,UAAU,GAAG,KAAK,KAAK;QAClD;AAAA,MACF;AAAA,IAAA;AAGI,UAAA,qBAAqB,wBAAC,UAAgC;AACtD,UAAA,MAAM,OAAO,YAAY,sBAAsB;AACjD,YAAI,CAAC,aAAa,IAAI,oCAAoC,GAAG;AAC3D;AAAA,QACF;AAEM,cAAA,QAAqB,MAAM,OAAO;AACxC,cAAM,CAACD,IAAG,CAAC,IAAI,MAAM;AAEf,cAAA,IAAI,MAAM,OAAO;AACjB,cAAA,YAAY,EAAE,UAAU;AAE1B,YAAA,aAAa,MAAM,aAAa;AAClC,2BAAiB,oBAAoB;AAAA,QACvC;AAAA,MACS,WAAA,MAAM,OAAO,YAAY,qBAAqB;AACvD,YAAI,CAAC,aAAa,IAAI,mCAAmC,GAAG;AAC1D;AAAA,QACF;AAEM,cAAA,OAAmB,MAAM,OAAO;AACtC,cAAM,CAACA,IAAG,CAAC,IAAI,KAAK;AAEd,cAAA,IAAI,MAAM,OAAO;AACjB,cAAA,YAAY,EAAE,UAAU;AAE9B,YAAI,aAAa,GAAG;AAClB,2BAAiB,oBAAoB;AAAA,QACvC;AAAA,MACF;AAAA,IAAA,GA7ByB;AAgCV,qBAAA,UAAU,oBAAoB,kBAAkB;;;;;;;;;;;;;;;;;AChHpD,MAAA,oBAAoB,YAAY,aAAa,MAAM;AACxD,QAAA,UAAU,IAAI,KAAK;AACzB,WAAS,gBAAgB;AACf,YAAA,QAAQ,CAAC,QAAQ;AAAA,EAC3B;AAFS;AAIF,SAAA;AAAA,IACL;AAAA,IACA;AAAA,EAAA;AAEJ,CAAC;ACbW,IAAA,2CAAAG,4BAAL;AACLA,0BAAA,QAAS,IAAA;AACTA,0BAAA,YAAa,IAAA;AACbA,0BAAA,gBAAiB,IAAA;AAHPA,SAAAA;AAAA,GAAA,0BAAA,CAAA,CAAA;AAMA,IAAA,6CAAAC,8BAAL;AACLA,4BAAA,cAAe,IAAA;AACfA,4BAAA,YAAa,IAAA;AACbA,4BAAA,WAAY,IAAA;AAHFA,SAAAA;AAAA,GAAA,4BAAA,CAAA,CAAA;ACFZ,MAAKC,cAAU;AAAA,EACb,MAAM;AAAA,EACN,SAASC;AAAAA,EACT,OAAO,CAAC,wBAAwB;AAAA,EAChC,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,aAAa;AAAA,IACf;AAAA,EACD;AAAA,EACD,UAAU;AACR,QAAI,OAAOA,SAAa,YAAY,YAAY;AAC9CA,eAAa,QAAQ,KAAK,IAAI;AAAA,IAChC;AAGA,UAAM,UAAU,KAAK,IAAI,cAAc,OAAO;AAC9C,QAAI,SAAS;AACX,cAAQ,iBAAiB,oBAAoB,MAAM;AACjD,aAAK,cAAc;AAAA,OACpB;AACD,cAAQ,iBAAiB,kBAAkB,MAAM;AAC/C,aAAK,cAAc;AAAA,OACpB;AAAA,IACH;AAEA,SAAK;AAAA,MACH,MAAM,KAAK;AAAA,MACX,CAAC,QAAQ,WAAW;AAElB,aAAK,MAAM,0BAA0B,MAAM;AAAA,MAC7C;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,UAAU,OAAO;AACf,UAAI,MAAM,QAAQ,WAAW,KAAK,aAAa;AAC7C,cAAM,eAAe;AACrB,cAAM,gBAAgB;AACtB;AAAA,MACF;AAEAA,eAAa,QAAQ,UAAU,KAAK,MAAM,KAAK;AAAA,IACjD;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;ACUA,UAAM,eAAe;AACrB,UAAM,eAAe;AAAA,MAAS,MAC5B,aAAa,IAAI,sCAAsC;AAAA,IAAA;AAEzD,UAAM,aAAa;AAAA,MAAS,MAC1B,aAAa,IAAI,oCAAoC;AAAA,IAAA;AAEvD,UAAM,oBAAoB;AAAA,MAAS,MACjC,aAAa,IAAI,2CAA2C;AAAA,IAAA;AAE9D,UAAM,qBAAqB;AAC3B,UAAM,gBAAgB;AAAA,MAAS,MAC7B,mBAAmB,iBAAiB,MAAM,OAAO;AAAA,IAAA;AAGnD,UAAM,oBAAoB;AAC1B,UAAM,eAAe;AAAA,MAAS,MAC5B,kBAAkB,aAAa,MAAM,OAAO;AAAA,IAAA;AAG9C,UAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmBd,UAAM,eAAe;AACf,UAAA,EAAE,GAAAL,OAAM;AAEd,UAAM,oBAAoB;AAAA,MAAS,MACjC,aAAa,IAAI,qCAAqC;AAAA,IAAA;AAQlD,UAAA,0BAA0B,IAAI,KAAK;AACzC,UAAM,UAAU,mCAAmC,KAAK,OAAA,CAAQ;AAC1D,UAAA,cAAc,IAAwB,CAAA,CAAE;AACxC,UAAA,oBAAoB,IAA6B,IAAI;AACrD,UAAA,eAAe,IAAI,EAAE;AACrB,UAAA,cAAc,SAAS,MAAM;AACjC,aAAO,QAAO,QAAC,WAAW,IAAIA,GAAE,eAAe,IAAI,QAAQ;AAAA,IAAA,CAC5D;AAED,UAAM,eAAe;AACrB,UAAM,qBAAqB;AACrB,UAAA,SAAS,wBAAC,UAAkB;AAChC,YAAM,eAAe,UAAU,MAAM,gBAAQ,WAAW;AACxD,mBAAa,QAAQ;AACT,kBAAA,QAAQ,eAChB,mBAAmB,cACnB;AAAA,QACE,GAAG,aAAa,kBAAkB,WAAW,OAAO,QAAA,SAAS;AAAA,UAC3D,OAAO,QAAA;AAAA,QAAA,CACR;AAAA,MAAA;AAAA,IACH,GATS;AAYf,UAAM,OAAO;AAEb,QAAI,eAAwC;AAC5C,UAAM,eAAe,mCAAY;AACd,uBAAA,SAAS,eAAe,OAAO;AAChD,UAAI,cAAc;AAChB,qBAAa,KAAK;AAClB,cAAM,SAAS,MAAM,cAAc,MAAO,CAAA;AAAA,MAC5C;AAAA,IAAA,GALmB;AAQrB,cAAU,YAAY;AAChB,UAAA,cAAc,wBAClB,mBACG;AACH,8BAAwB,QAAQ;AAChC,WAAK,aAAa,cAAc;AAAA,IAAA,GAJd;AAMd,UAAA,iBAAiB,8BACrB,OACA,mBACG;AACH,YAAM,gBAAgB;AACtB,YAAM,eAAe;AACrB,WAAK,gBAAgB,cAAc;AACnC,YAAM,aAAa;AAAA,IAAA,GAPE;AASjB,UAAA,qBAAqB,wBAAC,UAAkB;AAC5C,UAAI,UAAU,IAAI;AAChB,0BAAkB,QAAQ;AAC1B;AAAA,MACF;AACM,YAAA,QAAQ,YAAY,MAAM,KAAK;AACrC,wBAAkB,QAAQ;AAAA,IAAA,GAND;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvG3B,QAAI,eAA0C;AAC9C,QAAI,qBAA6C;AACjD,QAAI,oBAAoB;AAExB,UAAM,eAAe;AACrB,UAAM,mBAAmB;AAEzB,UAAM,EAAE,QAAY,IAAA,YAAY,kBAAmB,CAAA;AAC7C,UAAA,cAAc,IAAI,IAAI;AAC5B,UAAM,qBAAqB,6BAAa;AAC/B,aAAA,eACH,CAAC,aAAa,SAAS,aAAa,OAAO,IAC3C,iBAAiB;IAAgB,GAHZ;AAKrB,UAAA,cAAc,IAAqD,CAAA,CAAE;AACrE,UAAA,YAAY,wBAAC,WAA0D;AAC/D,kBAAA,MAAM,KAAK,MAAM;AAAA,IAAA,GADb;AAGZ,UAAA,eAAe,wBACnB,WACG;AACS,kBAAA,QAAQ,YAAY,MAAM;AAAA,QACpC,CAAC,MAAM,MAAM,CAAC,MAAM,MAAM,MAAM;AAAA,MAAA;AAAA,IAClC,GALmB;AAOrB,UAAM,eAAe,6BAAM;AACzB,kBAAY,QAAQ;IAAC,GADF;AAGrB,UAAM,cAAc,6BAAM;AACxB,cAAQ,QAAQ;AAAA,IAAA,GADE;AAGpB,UAAM,cAAc;AAEd,UAAA,UAAU,wBAAC,YAA8B;AAC7C,UAAI,CAAC,cAAc;AACjB,gBAAQ,KAAK,0DAA0D;AACvE;AAAA,MACF;AAEoB,0BAAA;AACd,YAAA,OAAO,iBAAiB,eAAe,SAAS;AAAA,QACpD,KAAK,mBAAmB;AAAA,MAAA,CACzB;AAED,kBAAY,UAAU,EAAE,cAAc,cAAc,MAAM,YAAY;AAGrD,yBAAE,gBAAgB,eAAe,WAAW;AAC7D,aAAO,sBAAsB,WAAW;AAAA,IAAA,GAf1B;AAkBhB,UAAM,sBAAsB;AAAA,MAC1B,MAAM,aAAa,IAAI,yBAAyB,MAAM;AAAA,IAAA;AAElD,UAAA,gBAAgB,wBAAC,MAA0B;AAC/C,UAAI,oBAAoB,OAAO;AACzB,YAAA,EAAE,gBAAgB,SAAS;AAC7B,qBAAW,MAAM;AACf,6BAAiB,CAAC;AAAA,aACjB,GAAG;AAAA,QAAA,OACD;AACL,2BAAiB,CAAC;AAAA,QACpB;AAAA,MAAA,OACK;AACO,oBAAA,UAAA,EAAY,cAAc,CAAC;AAAA,MACzC;AAAA,IAAA,GAXoB;AAchB,UAAA,eAAe,6BACnB,YAAY,UAAA,EAAY,cAAc,YAAY,GAAG,CAAC,GADnC;AAGrB,UAAM,eAAe;AACf,UAAA,mBAAmB,wBAAC,MAA0B;AAClD,YAAM,YAAY;AAClB,UAAI,WAAW;AACP,cAAA,SACJ,UAAU,WAAW,UACjB,aAAa,kBAAkB,kBAC/B,aAAa,kBAAkB;AAErC,cAAM,WAAW,UAAU,SAAS,MAAM,SAAc,KAAA;AAC9C,kBAAA;AAAA,UACR,WAAW;AAAA,UACX,OAAO;AAAA,QAAA,CACR;AAAA,MACH;AAEA,cAAQ,QAAQ;AACD,qBAAA;AAGf,kBAAY,QAAQ;AACpB,iBAAW,MAAM;AACf,oBAAY,QAAQ;AAAA,SACnB,GAAG;AAAA,IAAA,GAtBiB;AAyBnB,UAAA,kBAAkB,wBAAC,MAA0B;AACjD,YAAM,YAAY;AAClB,UAAI,CAAC,UAAW;AAEhB,YAAM,EAAE,MAAM,UAAU,OAAA,IAAW;AACnC,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA,iBAAiB;AAAA,QACjB,eAAe,6BAAM;AACO;AAC1B,wBAAc,CAAC;AAAA,QACjB,GAHe;AAAA,MAGf;AAEF,YAAM,oBACJ,WAAW,UACP,EAAE,UAAU,MAAM,UAAU,SAAA,IAC5B,EAAE,QAAQ,MAAM,QAAQ,SAAS;AAEjC,YAAA,SAAS,YAAY;AACrB,YAAA,OAAO,OAAO,mBAAmB;AAAA,QACrC,GAAG;AAAA,QACH,GAAG;AAAA,MAAA,CACJ;AAED,UAAI,CAAC,MAAM;AACT,gBAAQ,KAAK,8CAA8C;AAC3D;AAAA,MACF;AAEe,qBAAA;AACf,2BAAqB,IAAI;AACnB,YAAA,EAAE,OAAW,IAAA;AACnB,YAAM,UAAU,EAAE,MAAM,MAAM,OAAO;AAGrC;AAAA,QACE,OAAO;AAAA,QACP;AAAA,QACA,CAAC,gBAAgB;AACf,cAAI,EAAE,uBAAuB;AACrB,kBAAA,IAAI,MAAM,eAAe;AAE3BM,gBAAAA,QAAgB,YAAY,QAAQ;AAC1C,cAAI,EAAEA,iBAAgB,YAAmB,OAAA,IAAI,MAAM,cAAc;AAE7C,8BAAA;AACpB,sBAAY,eAAe;AACpB,iBAAA,cAAc,cAAcA,OAAM,CAAC;AAAA,QAC5C;AAAA,QACA;AAAA,MAAA;AAIF,YAAM,4BAA4B;AAAA,QAChC,KAAK,WAAW;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF,GA1DsB;AA8DxB,gBAAY,MAAM;AACV,YAAA,EAAE,OAAW,IAAA;AACnB,UAAI,CAAC,OAAQ;AAEb,gBAAU,mCAAmC;AAC7C,aAAO,kBAAkB;AAEzB;AAAA,QACE,OAAO,cAAc;AAAA,QACrB;AAAA,QACA;AAAA,MAAA;AAAA,IACF,CACD;AAEK,UAAA,qBAAqB,wBAAC,MAA4B;AAClD,UAAA,EAAE,OAAO,YAAY,sBAAsB;AAC/B,sBAAA,EAAE,OAAO,aAAa;AAAA,MAC3B,WAAA,EAAE,OAAO,YAAY,sBAAsB;AAC9C,cAAA,QAAQ,EAAE,OAAO;AACvB,cAAM,CAACP,IAAG,CAAC,IAAI,MAAM;AACrB,cAAM,YAAY,EAAE,OAAO,cAAc,UAAU;AAE/C,YAAA,YAAY,MAAM,aAAa;AACnB,wBAAA,EAAE,OAAO,aAAa;AAAA,QACtC;AAAA,MACF;AAAA,IAAA,GAXyB;AAc3B,UAAM,oBAAoB;AAAA,MAAS,MACjC,aAAa,IAAI,0BAA0B;AAAA,IAAA;AAG7C,UAAM,yBAAyB;AAAA,MAAS,MACtC,aAAa,IAAI,+BAA+B;AAAA,IAAA;AAIlD,UAAM,iBAAiB,wBAAC,MAAa,EAAE,eAAe,GAA/B;AACjB,UAAA,kBAAkB,wBAAC,MAAuC;AAC9D,QAAE,eAAe;AAEX,YAAA,SAAS,YAAY;AACpB,aAAA,cAAc,MAAM,eAAe,CAAC,EAAE,OAAO,SAAS,EAAE,OAAO,OAAO;AAC7E,uBAAiB,OAAO,cAAc,QAAQ,SAAS,gBAAgB;AAAA,QACrE,MAAM;AAAA,MAAA,CACP;AAAA,IAAA,GAPqB;AAUlB,UAAA,wBAAwB,wBAAC,MAAuC;AAChD,0BAAA;AACpB,YAAM,SAAS,EAAE,OAAO,WACpB,uBAAuB,QACvB,kBAAkB;AACtB,cAAQ,QAAQ;AAAA,QACd,KAAK,yBAAyB;AAC5B,0BAAgB,CAAC;AACjB,wBAAc,EAAE,MAAM;AACtB;AAAA,QACF,KAAK,yBAAyB;AAC5B,0BAAgB,CAAC;AACjB,0BAAgB,EAAE,MAAM;AACxB;AAAA,QACF,KAAK,yBAAyB;AAAA,QAC9B;AACE;AAAA,MACJ;AAAA,IAAA,GAjB4B;AAqB9B,UAAM,QAAQ,6BAAM;AAClB,0BAAoB,MAAM;AACL,2BAAA;AACN,qBAAA;AAET,YAAA,SAAS,YAAY;AAC3B,aAAO,cAAc,OAAO,oBAAoB,SAAS,cAAc;AACnE,UAAA,kBAA0B,QAAA,cAAc,gBAAgB;AAE5D,aAAO,cAAc;AACd,aAAA,SAAS,MAAM,IAAI;AAAA,IAAA,GAVd;AAcd,UAAM,SAAS,MAAM;AACf,UAAA,CAAC,QAAQ,MAAa;IAAA,CAC3B;AAEgB,qBAAA,UAAU,oBAAoB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtQjE,UAAM,OAAO;AAGb,UAAM,eAAe;AAAA,MAAS,MAC5B,OAAO,QAAS,cAAK,aAAa,QAAS,UAAM,KAAA,KAAK,QAAA;AAAA,IAAA;AAExD,UAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,aAAa,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpCrD,UAAA,EAAE,GAAAC,OAAM;AACd,UAAM,YAAY;AAElB,UAAM,UAAU;AAAA,MACd,MAAM,GAAGA,GAAE,oBAAoB,CAAC,KAAK,UAAU,aAAa,QAAQ;AAAA,IAAA;AAEtE,UAAM,SAAS,mCAAY;AACzB,YAAM,UAAU;AAChB,aAAO,SAAS;IAAO,GAFV;;;;;;;;;;;;;ACFf,UAAM,cAAc;AACpB,UAAM,cAAc,6BAAM;AACxB,kBAAY,WAAW;AAAA,QACrB,KAAK;AAAA,QACL,iBAAiB;AAAA,QACjB,WAAW;AAAA,MAAA,CACZ;AAAA,IAAA,GALiB;;;;;;;;;;;;;;ACApB,UAAM,oBAAoB;AAC1B,UAAM,OAAO;AAAA,MAAS,MACpB,kBAAkB,uBAAuB,cACrC,cACA;AAAA,IAAA;AAGN,UAAM,eAAe;AACrB,UAAM,cAAc,mCAAY;AACxB,YAAA,aAAa,QAAQ,mBAAmB;AAAA,IAAA,GAD5B;;;;;;;;;;;;;;;;;;;ACkBpB,UAAM,iBAAiB;AACvB,UAAM,eAAe;AACrB,UAAM,YAAY;AAElB,UAAM,iBAAiB;AAAA,MAAS,MAC9B,aAAa,IAAI,wBAAwB,MAAM,SAC3C,uBACA;AAAA,IAAA;AAGN,UAAM,UAAU;AAAA,MACd,MAAM,aAAa,IAAI,oBAAoB,MAAM;AAAA,IAAA;AAGnD,UAAM,OAAO,SAAS,MAAM,eAAe,eAAgB,CAAA;AAC3D,UAAM,cAAc,SAAS,MAAM,eAAe,WAAW,gBAAgB;AACvE,UAAA,aAAa,wBAAC,SAA8B;AACjC,qBAAA,WAAW,iBAAiB,KAAK,EAAE;AAAA,IAAA,GADjC;AAGnB,UAAM,kBAAkB;AAClB,UAAA,sBAAsB,wBAAC,QAA6B;AACxD,YAAM,aAAa,gBAAgB;AAAA,QACjC,8BAA8B,IAAI,EAAE;AAAA,MAAA;AAEtC,aAAO,aAAa,KAAK,WAAW,MAAM,SAAU,CAAA,MAAM;AAAA,IAAA,GAJhC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrB5B,UAAM,QAAQ;AAKR,UAAA,EAAE,GAAAA,OAAM;AAEd,UAAM,iBAAiB;AACvB,UAAM,gBAAgB;AACtB,UAAM,eAAe;AACf,UAAA,iBAAiB,IAAwB,IAAI;AAGnD,UAAM,kBAAkB;AAAA,MAAS,MAC/B,aAAa,IAAI,yBAAyB;AAAA,IAAA;AAE5C,UAAM,gBAAgB;AAAA,MAAS,MAC7B,aAAa,IAAI,8BAA8B;AAAA,IAAA;AAG3C,UAAA,4BAA4B,SAAS,MAAM;AAC/C,UAAI,eAAe,WAAW;AAErB,eAAA;AAAA,MACT;AACA,UAAI,CAAC,MAAM,eAAe,SAAS,aAAa;AAEvC,eAAA;AAAA,MACT;AACI,UAAA,MAAM,eAAe,SAAS,YAAY;AAExC,YAAA,gBAAgB,UAAU,OAAO;AAE5B,iBAAA;AAAA,QACT;AACA,YAAI,gBAAgB,UAAU,iBAAiB,cAAc,QAAQ,KAAM;AAElE,iBAAA;AAAA,QACT;AAEO,eAAA;AAAA,MACT;AAEO,aAAA;AAAA,IAAA,CACR;AAEK,UAAA,iBAAiB,8BAAO,YAA8B;AAC1D,iBAAW,OAAO,SAAS;AACzB,YACE,CAAE,MAAM,mBAAA,EAAqB,cAAc,IAAI,UAAU;AAAA,UACvD,eAAe,CAAC,eAAe;AAAA,UAC/B,MAAMA,GAAE,wCAAwC;AAAA,QAAA,CACjD,GACD;AAEA;AAAA,QACF;AAAA,MACF;AAAA,IAAA,GAXqB;AAcjB,UAAA,kBAAkB,8BAAO,WAA2B;AAClD,YAAA,eAAe,CAAC,MAAM,CAAC;AAAA,IAAA,GADP;AAGlB,UAAA,YAAY,6BAAM,eAAe,OAArB;AAElB,0BAAsB,WAAW;AAAA,MAC/B,gBAAgB,6BAAM;AACb,eAAA;AAAA,UACL,aAAa,MAAM,eAAe,SAAS;AAAA,QAAA;AAAA,MAE/C,GAJgB;AAAA,IAIhB,CACD;AAED,0BAAsB,WAAW;AAAA,MAC/B,SAAS,6BAAM;AACN,eAAA;AAAA,UACL,aAAa,MAAM,eAAe,SAAS;AAAA,QAAA;AAAA,MAE/C,GAJS;AAAA,MAKT,QAAQ,wBAAC,MAAM;AACP,cAAA,YAAY,cAAc,cAAc;AAAA,UAC5C,CAAC,OAAO,GAAG,QAAQ,EAAE,OAAO,KAAK;AAAA,QAAA;AAE7B,cAAA,UAAU,cAAc,cAAc;AAAA,UAC1C,CAAC,OAAO,GAAG,QAAQ,EAAE,SAAS,QAAQ,YAAY,CAAC,GAAG,KAAK;AAAA,QAAA;AAE7D,YAAI,cAAc,SAAS;AACX,wBAAA,iBAAiB,WAAW,OAAO;AAAA,QACnD;AAAA,MACF,GAVQ;AAAA,IAUR,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtED,UAAM,QAAQ;AAIR,UAAA,EAAE,GAAAA,OAAM;AACd,UAAM,iBAAiB;AACvB,UAAM,gBAAgB;AACtB,UAAM,kBAAkB;AACxB,UAAM,wBAAwB;AAC9B,UAAM,kBAAkB;AACxB,UAAM,OAAO;AACb,UAAM,iBAAiB;AAEjB,UAAA,mBAAmB,wBAAC,cAA6C;AAAA,MACrE,OAAO,SAAS;AAAA,MAChB;AAAA,IAAA,IAFuB;AAKzB,UAAM,UAAU;AAAA,MAA2B,MACzC,cAAc,cAAc,IAAI,gBAAgB;AAAA,IAAA;AAElD,UAAM,mBAAmB;AAAA,MAAgC,MACvD,cAAc,iBACV,iBAAiB,cAAc,cAA+B,IAC9D;AAAA,IAAA;AAEA,UAAA,mBAAmB,8BAAO,WAA2B;AAEzD,UAAI,CAAC,QAAQ;AACX;AAAA,MACF;AAEA,UAAI,iBAAiB,OAAO,UAAU,OAAO,OAAO;AAClD;AAAA,MACF;AAEM,YAAA,gBAAgB,aAAa,OAAO,QAAQ;AAAA,IAAA,GAV3B;AAanB,UAAA,iBAAiB,8BAAOO,aAA8B;AAC1D,iBAAW,OAAOA,UAAS;AACzB,YACE,CAAE,MAAM,gBAAgB,cAAc,IAAI,UAAU;AAAA,UAClD,eAAe,CAAC,eAAe;AAAA,QAAA,CAChC,GACD;AAEA;AAAA,QACF;AAAA,MACF;AAAA,IAAA,GAVqB;AAajB,UAAA,kBAAkB,8BAAO,WAA2B;AAClD,YAAA,eAAe,CAAC,MAAM,CAAC;AAAA,IAAA,GADP;AAIlB,UAAA,kBAAkB,wBAAC,OAAmB,WAA2B;AACrE,sBAAgB,QAAQ;AACnB,WAAA,MAAM,KAAK,KAAK;AAAA,IAAA,GAFC;AAIlB,UAAA,mBAAmB,SAAS,MAAM;AACtC,YAAM,MAAM,gBAAgB;AACxB,UAAA,CAAC,IAAK,QAAO;AACX,YAAA,QAAQ,QAAQ,MAAM,UAAU,CAAC,MAAM,EAAE,aAAa,IAAI,QAAQ;AAEjE,aAAA;AAAA,QACL;AAAA,UACE,OAAOP,GAAE,sBAAsB;AAAA,UAC/B,SAAS,mCAAY;AACb,kBAAA,gBAAgB,kBAAkB,IAAI,QAAQ;AAAA,UACtD,GAFS;AAAA,QAGX;AAAA,QACA;AAAA,UACE,WAAW;AAAA,QACb;AAAA,QACA;AAAA,UACE,OAAOA,GAAE,kBAAkB;AAAA,UAC3B,SAAS,6BAAM,gBAAgB,GAAG,GAAzB;AAAA,QACX;AAAA,QACA;AAAA,UACE,OAAOA,GAAE,yBAAyB;AAAA,UAClC,SAAS,6BAAM,eAAe,QAAQ,MAAM,MAAM,GAAG,KAAK,CAAC,GAAlD;AAAA,UACT,UAAU,SAAS;AAAA,QACrB;AAAA,QACA;AAAA,UACE,OAAOA,GAAE,0BAA0B;AAAA,UACnC,SAAS,6BAAM,eAAe,QAAQ,MAAM,MAAM,QAAQ,CAAC,CAAC,GAAnD;AAAA,UACT,UAAU,UAAU,QAAQ,MAAM,SAAS;AAAA,QAC7C;AAAA,QACA;AAAA,UACE,OAAOA,GAAE,wBAAwB;AAAA,UACjC,SAAS,6BACP,eAAe;AAAA,YACb,GAAG,QAAQ,MAAM,MAAM,QAAQ,CAAC;AAAA,YAChC,GAAG,QAAQ,MAAM,MAAM,GAAG,KAAK;AAAA,UAAA,CAChC,GAJM;AAAA,UAKT,UAAU,QAAQ,MAAM,UAAU;AAAA,QACpC;AAAA,QACA;AAAA,UACE,OAAO,sBAAsB,aAAa,IAAI,SAAS,IAAI,IACvDA,GAAE,6BAA6B,IAC/BA,GAAE,wBAAwB;AAAA,UAC9B,SAAS,6BAAM,sBAAsB,iBAAiB,IAAI,SAAS,IAAI,GAA9D;AAAA,UACT,UAAU,IAAI,SAAS;AAAA,QACzB;AAAA,MAAA;AAAA,IACF,CACD;AACD,UAAM,eAAe;AAGf,UAAA,cAAc,wBAAC,UAAsB;AACzC,YAAM,gBAAgB,MAAM;AACtB,YAAA,eAAe,MAAM,UAAU,MAAM;AAC3C,oBAAc,OAAO;AAAA,QACnB,MAAM,cAAc,aAAa;AAAA,MAAA,CAClC;AAAA,IAAA,GALiB;AASpB;AAAA,MACE,MAAM,cAAc;AAAA,MACpB,YAAY;AACN,YAAA,CAAC,iBAAiB,MAAO;AAE7B,cAAM,SAAS;AAET,cAAA,mBAAmB,SAAS,cAAc,yBAAyB;AACzE,YAAI,CAAC,oBAAoB,CAAC,eAAe,MAAO;AAE1C,cAAA,YAAY,eAAe,MAAM,IAAI;AAAA,UACzC;AAAA,QAAA;AAEF,YAAI,CAAC,UAAW;AAEV,cAAA,UAAU,iBAAiB;AAC3B,cAAA,gBAAgB,UAAU;AAE1B,cAAA,aAAa,QAAQ,OAAO,cAAc;AAC1C,cAAA,cAAc,QAAQ,QAAQ,cAAc;AAElD,YAAI,cAAc,GAAG;AACnB,oBAAU,SAAS,EAAE,MAAM,YAAa,CAAA;AAAA,QAAA,WAC/B,aAAa,GAAG;AACzB,oBAAU,SAAS,EAAE,MAAM,WAAY,CAAA;AAAA,QACzC;AAAA,MACF;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5MpB,MAAM,eAAkC;AAAA,EACtC,kBAAkB;AAAA,IAChB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,kBAAkB;AAAA,IAChB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,mBAAmB;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,iBAAiB;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,uBAAuB;AAAA,IACrB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,2BAA2B;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,2BAA2B;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,mCAAmC;AAAA,IACjC,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,sBAAsB;AAAA,IACpB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,0BAA0B;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,8BAA8B;AAAA,IAC5B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,6BAA6B;AAAA,IAC3B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,iCAAiC;AAAA,IAC/B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,wBAAwB;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,sBAAsB;AAAA,IACpB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,sBAAsB;AAAA,IACpB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,uBAAuB;AAAA,IACrB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,sBAAsB;AAAA,IACpB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,eAAe;AAAA,IACb,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,yBAAyB;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,wBAAwB;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,cAAc;AAAA,IACZ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,cAAc;AAAA,IACZ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,iBAAiB;AAAA,IACf,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,yBAAyB;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,gBAAgB;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,0BAA0B;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,wBAAwB;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,cAAc;AAAA,IACZ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,IACV,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,WAAW;AAAA,IACT,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,0BAA0B;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,yBAAyB;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,6BAA6B;AAAA,IAC3B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,4BAA4B;AAAA,IAC1B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,yBAAyB;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,4BAA4B;AAAA,IAC1B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,yBAAyB;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,6BAA6B;AAAA,IAC3B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,8BAA8B;AAAA,IAC5B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,wBAAwB;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,yBAAyB;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,2BAA2B;AAAA,IACzB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,gCAAgC;AAAA,IAC9B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,+BAA+B;AAAA,IAC7B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,kCAAkC;AAAA,IAChC,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,8BAA8B;AAAA,IAC5B,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,0BAA0B;AAAA,IACxB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAChB;AAAA,EACA,wBAAwB;AAAA,IACtB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBACE;AAAA,IACF,cAAc;AAAA,EAChB;AAAA,EACA,qBAAqB;AAAA,IACnB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AAAA,EACA,sBAAsB;AAAA,IACpB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,EAChB;AACF;AAKO,MAAM,iBAAiB,6BAAM;AAClC,QAAM,eAAe,wBAAC,aACpB,aAAa,QAAQ,GAAG,gBAAgB,IADrB;AAMf,QAAA,sBAAsB,wBAAC,SAA6B;AACxD,QAAI,CAAC,KAAK,YAAY,UAAU,SAAiB,QAAA;AACjD,WAAO,aAAa,KAAK,YAAY,SAAS,IAAI;AAAA,EAAA,GAFxB;AAKrB,SAAA;AAAA,IACL;AAAA,EAAA;AAEJ,GAf8B;AChYvB,MAAM,eAAe,6BAAM;AAChC,QAAM,eAAe;AACrB,QAAM,iBAAiB;AACvB,QAAM,oBAAoB;AAE1B,QAAM,sBAAsB;AAAA,IAC1B,MACE,aAAa,IAAI,qCAAqC;AAAA,EAAA;AAE1D,QAAM,kBAAkB;AAAA,IACtB,MAAM,aAAa,IAAI,iCAAiC;AAAA,EAAA;AAE1D,QAAM,yBAAyB;AAAA,IAC7B,MACE,aAAa;AAAA,MACX;AAAA,IACF;AAAA,EAAA;AAGJ,QAAM,sBAAsB;AAAA,IAAS,MACnC,aAAa,IAAI,gCAAgC;AAAA,EAAA;AAGnD;AAAA,IACE;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,MAAM;AACA,UAAA,OAAO,eAAe,MAAM,IAAI;AAAA,IACtC;AAAA,EAAA;AAGF,QAAM,eAAe;AACZ,WAAA,iBACP,SACA,WACS;AACT,WAAO,EACL,cAAc,cAAc,QAC3B,SAAS,cAAc,cAAc,cAAc;AAAA,EAExD;AARS;AAUT,YAAU,MAAM;AACd,UAAM,cAAc;AAEpB,mBAAe,kBAAkB;AAAA,MAC/B,MAAM;AAAA,MACN,YAAY,MAAkB;AAC5B,aAAK,gBAAgB,cAAc;AAE7B,cAAA,QAAQ,SAAS,MAAM;AACrB,gBAAA,UAAU,aAAa,eAAe,IAAI;AAChD,iBAAO,IAAI,YAAY;AAAA,YACrB,MAAM,EAAE;AAAA,cACN;AAAA,gBACE,iBAAiB,SAAS,gBAAgB,KAAK,IAC3C,IAAI,KAAK,EAAE,KACX;AAAA,gBACJ,iBAAiB,SAAS,uBAAuB,KAAK,IAClD,SAAS,0BAA0B,KACnC;AAAA,gBACJ,iBAAiB,SAAS,oBAAoB,KAAK,IAC/C,SAAS,YAAY,aAAa,KAClC;AAAA,cAAA,EAEH,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,EAC1B,KAAK,GAAG;AAAA,cACX;AAAA,gBACE,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,YACA,SACE,kBAAkB,uBAAuB,OAAO,eAC7C;AAAA,YACL,SACE,kBAAkB,uBAAuB,OAAO,eAC7C;AAAA,UAAA,CACN;AAAA,QAAA,CACF;AAED,aAAK,OAAO,KAAK,MAAM,MAAM,KAAK;AAElC,YAAI,KAAK,YAAY,UAAU,YAAY,oBAAoB,OAAO;AAC9D,gBAAA,QAAQ,YAAY,oBAAoB,IAAI;AAE5C,gBAAA,eAAe,SAAS,MAAM;AAE5B,kBAAA,eACJ,kBAAkB,uBAAuB;AAC3C,mBAAO,IAAI,YAAY;AAAA,cACrB,MAAM;AAAA,cACN,aAAa;AAAA,gBACX,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO,eACH,YAAY,WAAW,EAAE,WAAW,IAAK,CAAA,IACzC;AAAA,gBACJ,SAAS,eACL,YAAY,WAAW,EAAE,WAAW,IAAK,CAAA,IACzC;AAAA,gBACJ,UAAU;AAAA,cACZ;AAAA,cACA,SACE,kBAAkB,uBAAuB,OAAO,eAC7C;AAAA,cACL,SAAS,eACL,YAAY,WAAW,EAAE,WAAW,IAAK,CAAA,IACzC;AAAA,YAAA,CACL;AAAA,UAAA,CACF;AAED,eAAK,OAAO,KAAK,MAAM,aAAa,KAAK;AAAA,QAC3C;AAAA,MACF;AAAA,IAAA,CACD;AAAA,EAAA,CACF;AACH,GAxH4B;ACXf,MAAA,gBAAgB,wBAAC,cAAsC;AAClE,QAAM,mBAAmB;AACzB,QAAM,mBAAmB;AACzB,QAAM,kBAAkB;AAEF,wBAAA,MAAM,UAAU,OAAO;AAAA,IAC3C,eAAe,wBAAC,SACd,KAAK,OAAO,KAAK,SAAS,uBAAuB,SAAS,QAD7C;AAAA,IAEf,QAAQ,8BAAO,UAAU;AACjB,YAAA,MAAM,MAAM,SAAS,QAAQ;AAC7B,YAAA,UAAU,MAAM,OAAO;AAEzB,UAAA,QAAQ,SAAS,sBAAsB;AACzC,cAAM,OAAO,QAAQ;AACjB,YAAA,KAAK,gBAAgB,kBAAkB;AACzC,gBAAM,UAAU,KAAK;AACf,gBAAA,MAAMC,IAAS,qBAAqB,CAAC,IAAI,SAAS,IAAI,OAAO,CAAC;AAGhE,cAAA,CAAC,KAAK,UAAU;AACpB,2BAAiB,eAAe,SAAS,EAAE,IAAK,CAAA;AAAA,QAAA,WACvC,KAAK,gBAAgB,eAAe;AAC7C,gBAAM,QAAQ,KAAK;AACb,gBAAA,MAAMA,IAAS,qBAAqB,CAAC,IAAI,SAAS,IAAI,OAAO,CAAC;AAC9D,gBAAA,YAAYA,IAAS,MAAM,aAAa,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAC5D,cAAI,iBAA2C;AAC/C,cAAI,kBAAqC;AACzC,cAAI,WAAW;AACb,kBAAM,YAAY,iBAAiB;AAAA,cACjC,MAAM;AAAA,YAAA;AAER,uBAAW,YAAY,WAAW;AAChC,kBAAI,SAAS,QAAQ,SAAS,UAAU,YAAY;AAChC,kCAAA;AACD,iCAAA;AAAA,cACnB;AAAA,YACF;AAAA,UACF;AACA,cAAI,CAAC,iBAAiB;AACpB,kBAAM,WAAW,iBAAiB,gBAAgB,MAAM,SAAS;AACjE,gBAAI,UAAU;AACZ,gCAAkB,iBAAiB;AAAA,gBACjC,SAAS;AAAA,gBACT;AAAA,kBACE;AAAA,gBACF;AAAA,cAAA;AAEe,+BAAA;AAAA,YACnB;AAAA,UACF;AACA,cAAI,iBAAiB;AACb,kBAAA,SAAS,gBAAgB,SAAS;AAAA,cACtC,CAACO,YAAWA,QAAO,SAAS,gBAAgB;AAAA,YAAA;AAE9C,gBAAI,QAAQ;AACV,qBAAO,QAAQ,MAAM;AAAA,YACvB;AAAA,UACF;AAAA,QAAA,WACS,KAAK,gBAAgB,eAAe;AAC7C,gBAAM,WAAW,KAAK;AAChB,gBAAA,WAAWP,IAAS,qBAAqB;AAAA,YAC7C,IAAI;AAAA,YACJ,IAAI;AAAA,UAAA,CACL;AACD,gBAAM,gBAAgB,eAAe,UAAU,EAAE,SAAU,CAAA;AAAA,QAC7D;AAAA,MACF;AAAA,IACF,GA3DQ;AAAA,EA2DR,CACD;AACH,GArE6B;ACDtB,MAAM,4BAA4B,6BAAM;AACvC,QAAA,IAAI,aAAa,UAAU;AAC3B,QAAA,6BAA6B,mCAE9B,MACH;AACA,UAAM,MAAM,EAAE,MAAM,MAAM,IAAI;AAC9B,eAAW,QAAQ,KAAK;AACtB,UAAI,MAAM,SAAS;AACjB,aAAK,UAAU,GAAG,eAAe,KAAK,OAAO,IAAI,KAAK,OAAO;AAAA,MAC/D;AAAA,IACF;AACO,WAAA;AAAA,EAAA,GAV0B;AAanC,eAAa,UAAU,uBAAuB;AAErC,WAAA,eACP,QACA,SACA;AACA,QAAI,CAAC,OAAQ;AACb,UAAM,UAAU;AAChB,UAAM,WAAW;AACX,UAAA,MAAM,GAAG,wBAAwB,UAAU;AAC3C,UAAA,OAAO,GAAG,yBAAyB,WAAW;AAC9C,UAAA,OAAO,GAAG,0BAA0B,YAAY;AACtD,eAAW,SAAS,QAAQ;AACtB,UAAA,OAAO,UAAU,SAAU;AAEhB,qBAAA,OAAO,SAAS,SAAS,OAAO;AAC3C,UAAA,CAAC,OAAO,SAAS;AACnB;AAAA,MACF;AACA,UAAI,GAAG,eAAe,MAAM,OAAO,EAAE,GAAG;AACtC,cAAM,UAAU,GAAG,eAAe,MAAM,OAAO,IAAI,MAAM,OAAO;AAAA,MAClE;AAGA,YAAM,YAAiB,QAAQ,SAAS,QAAQ,YAAY,SAAS;AAErE,YAAM,aAAa,MAAM,SAAS,MAAM,OAAO;AAC/C,UAAI,YAAY;AACV,YAAA,QAAQ,WAAW,CAAC;AACb,mBAAA,QAAQ,KAAK,CAAC,MAAsB;AACzC,cAAA,EAAE,QAAQ,MAAc,QAAA;AAC5B,kBAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAAA,QAAA,CAC/B;AACU,mBAAA,SAAS,KAAK,CAAC,MAAe;AACnC,cAAA,EAAE,QAAQ,MAAc,QAAA;AAC5B,kBAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAAA,QAAA,CAC/B;AACK,cAAA,UAAU,MAAM,QAAQ;AAC9B;AAAA,MACF;AACA,YAAM,cAAc,MAAM,SAAS,MAAM,QAAQ;AACjD,UAAI,aAAa;AACX,YAAA,QAAQ,YAAY,CAAC;AACd,mBAAA,QAAQ,KAAK,CAAC,MAAsB;AACzC,cAAA,EAAE,QAAQ,MAAc,QAAA;AAC5B,kBAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAAA,QAAA,CAC/B;AACU,mBAAA,SAAS,KAAK,CAAC,MAAe;AACnC,cAAA,EAAE,QAAQ,MAAc,QAAA;AAC5B,kBAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAAA,QAAA,CAC/B;AACK,cAAA,UAAU,MAAM,QAAQ;AAC9B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AArDS;AAuDT,QAAM,sBAAsB,UAAU;AAC7B,WAAAQ,aACP,QACA,SACA;AACA,QAAI,QAAQ,OAAO;AACjB,cAAQ,QAAQ;AAAA,QACd,YAAY,iBAAiB,QAAQ,KAAK,CAAC;AAAA,QAC3C,QAAQ;AAAA,MAAA;AAAA,IAEZ;AACA,mBAAe,QAAQ,OAAO;AAC9B,UAAM,MAAM,IAAI,oBAAoB,QAAQ,OAAO;AAC5C,WAAA;AAAA,EACT;AAbS,SAAAA,cAAA;AAeT,YAAU,cAAcA;AACd,YAAA,YAAY,YAAY,oBAAoB;AACxD,GA1FyC;ACPlC,MAAM,UAAU,6BAAM;AAC3B,QAAM,cAAc;AAEH,mBAAA,UAAU,QAAQ,CAAC,MAAM;AACpC,QAAA,EAAE,EAAE,kBAAkB,UAAU;AAClC;AAAA,IACF;AACA,QACG,EAAE,kBAAkB,uBACnB,EAAE,OAAO,SAAS,cACnB,EAAE,kBAAkB,oBAAoB,EAAE,OAAO,SAAS,QAC3D;AAEA;AAAA,IACF;AACA,UAAM,kBACJ,EAAE,OAAO,UAAU,SAAS,WAAW,KACvC,EAAE,OAAO,UAAU,SAAS,wBAAwB,KACpD,EAAE,OAAO,OAAO;AAGlB,UAAM,SAAS,YAAY;AACvB,QAAA,mBAAmB,QAAQ,eAAe;AAC5C,aAAO,gBAAgB;AAErB,QAAA,eAAe,QAAQ,QAAQ,GAAG;AACpC,QAAE,eAAe;AACjB,QAAE,yBAAyB;AACpB,aAAA;AAAA,IACT;AAAA,EAAA,CACD;AACH,GA/BuB;ACQhB,MAAM,qBAAqB,6BAAM;AAEtC,SAAO,WAAW,IAAI;AAEtB,SAAO,QAAQ,IAAI;AAEnB,SAAO,OAAO,IAAI;AAElB,SAAO,YAAY,IAAI;AAEvB,SAAO,aAAa,IAAI;AAExB,SAAO,cAAc,IAAI;AAEzB,SAAO,cAAc,IAAI;AAEzB,SAAO,aAAa,IAAI;AAExB,SAAO,aAAa,IAAI;AAC1B,GAnBkC;ACN3B,MAAM,uBAAuB,6BAAM;AACxC,QAAM,eAAe;AACrB,QAAM,cAAc;AAEpB,cAAY,MAAM;AACV,UAAA,oBAAoB,aAAa,IAAI,wBAAwB;AACnE,QAAI,YAAY,QAAQ;AACtB,kBAAY,OAAO,YAAY;AAAA,IACjC;AAAA,EAAA,CACD;AAED,cAAY,MAAM;AACV,UAAA,YAAY,aAAa,IAAI,uBAAuB;AAC1D,QAAI,YAAY,QAAQ;AACtB,kBAAY,OAAO,aAAa;AAAA,IAClC;AAAA,EAAA,CACD;AAED,cAAY,MAAM;AAChB,cAAU,kBAAkB,aAAa;AAAA,MACvC;AAAA,IAAA;AAAA,EACF,CACD;AAED,cAAY,MAAM;AAChB,cAAU,uBAAuB,aAAa;AAAA,MAC5C;AAAA,IAAA;AAAA,EACF,CACD;AAED,cAAY,MAAM;AAChB,eAAW,uBAAuB,aAAa;AAAA,MAC7C;AAAA,IAAA;AAAA,EACF,CACD;AAED,cAAY,MAAM;AAChB,cAAU,qCAAqC,aAAa;AAAA,MAC1D;AAAA,IAAA;AAAA,EACF,CACD;AAED,cAAY,MAAM;AACV,UAAA,iBAAiB,aAAa,IAAI,sBAAsB;AAC9D,QAAI,YAAY,QAAQ;AACtB,kBAAY,OAAO,oBAAoB;AACvC,kBAAY,OAAO;AAAA;AAAA,QAAkB;AAAA;AAAA,QAAgB;AAAA,MAAA;AAAA,IACvD;AAAA,EAAA,CACD;AAED,cAAY,MAAM;AAChB,UAAM,mCAAmC,aAAa;AAAA,MACpD;AAAA,IAAA;AAEF,QAAI,YAAY,QAAQ;AACtB,kBAAY,OAAO,6BACjB;AACF,kBAAY,OAAO;AAAA;AAAA,QAAkB;AAAA;AAAA,QAAe;AAAA,MAAA;AAAA,IACtD;AAAA,EAAA,CACD;AAED,cAAY,MAAM;AACV,UAAA,kBAAkB,aAAa,IAAI,yBAAyB;AAC5D,UAAA,EAAE,OAAW,IAAA;AACnB,QAAI,QAAQ;AACV,aAAO,kBAAkB;AAClB,aAAA,SAAS,OAAO,IAAI;AAAA,IAC7B;AAAA,EAAA,CACD;AAED,cAAY,MAAM;AACV,UAAA,aAAa,aAAa,IAAI,6BAA6B;AAC3D,UAAA,EAAE,OAAW,IAAA;AACf,QAAA,eAAe,aAAa;AAAA,EAAA,CACjC;AAED,cAAY,MAAM;AACV,UAAA,kBAAkB,aAAa,IAAI,2BAA2B;AAC9D,UAAA,EAAE,OAAW,IAAA;AACf,QAAA,eAAe,kBAAkB;AAAA,EAAA,CACtC;AAED,cAAY,MAAM;AAChB,kBAAc,kBAAkB,aAAa;AAAA,MAC3C;AAAA,IAAA;AAAA,EACF,CACD;AAED,cAAY,MAAM;AACF,kBAAA,aAAa,aAAa,IAAI,+BAA+B;AAAA,EAAA,CAC5E;AAED,cAAY,MAAM;AACF,kBAAA,gBAAgB,aAAa,IAAI,0BAA0B;AAAA,EAAA,CAC1E;AAED,cAAY,MAAM;AACN,cAAA,mBAAmB,aAAa,IAAI,2BAA2B;AAAA,EAAA,CAC1E;AAED,cAAY,MAAM;AACN,cAAA,mBAAmB,aAAa,IAAI,oBAAoB;AAAA,EAAA,CACnE;AAED,cAAY,MAAM;AAChB,cAAU,uBAAuB,aAAa;AAAA,MAC5C;AAAA,IAAA;AAAA,EACF,CACD;AAED,cAAY,MAAM;AACN,cAAA,QAAQ,kBAAkB,aAAa;AAAA,MAC/C;AAAA,IAAA;AAAA,EACF,CACD;AAED,cAAY,MAAM;AAChB,cAAU,sBAAsB,aAAa;AAAA,MAC3C;AAAA,IAAA;AAAA,EACF,CACD;AAED,cAAY,MAAM;AAChB,cAAU,wBAAwB,aAAa;AAAA,MAC7C;AAAA,IAAA;AAAA,EACF,CACD;AACH,GA/HoC;ACI7B,MAAM,WAAW,6BAAM;AAC5B,QAAM,iBAAiB;AACvB,QAAM,cAAc;AAEpB,QAAM,mBAAmB,wBACvB,OACA,MACA,gBACG;AACH,QAAI,CAAC,KAAM;AAEX,UAAM,gBAAgB,MAAM,KAAK,KAAK,EAAE;AAAA,MAAO,CAAC,SAC9C,KAAK,KAAK,WAAW,WAAW;AAAA,IAAA;AAGlC,UAAM,OAAO,cAAc,CAAC,GAAG,UAAU;AACzC,QAAI,CAAC,KAAM;AAEX,SAAK,YAAY,IAAI;AAChB,SAAA;AAAA,MACH,MAAM,KAAK,aAAa,EACrB,IAAI,CAAC,MAAM,EAAE,UAAA,CAAW,EACxB,OAAO,CAAC,MAAM,MAAM,IAAI;AAAA,IAAA;AAAA,EAC7B,GAnBuB;AAsBR,mBAAA,UAAU,SAAS,OAAO,MAAM;AAC/C,UAAM,kBACJ,EAAE,kBAAkB,YACnB,EAAE,OAAO,UAAU,SAAS,WAAW,KACtC,EAAE,OAAO,UAAU,SAAS,wBAAwB,KACpD,EAAE,OAAO,OAAO;AAGpB,QAAI,CAAC,gBAAiB;AAItB,QAAI,eAAe,UAAW;AAExB,UAAA,EAAE,OAAW,IAAA;AACnB,QAAI,CAAC,OAAQ;AAEP,UAAA,EAAE,MAAU,IAAA;AAClB,QAAI,OAAqC,EAAE;AAC3C,QAAI,CAAC,KAAY,OAAA,IAAI,MAAM,sCAAsC;AAE3D,UAAA,EAAE,MAAU,IAAA;AAElB,UAAM,cAAc,OAAO;AAC3B,UAAM,iBAAiB,aAAa;AAE9B,UAAA,sBAAsB,kBAAkB,YAAY,WAAW;AAC/D,UAAA,sBAAsB,kBAAkB,YAAY,WAAW;AAC/D,UAAA,sBAAsB,kBAAkB,YAAY,WAAW;AAEjE,QAAA,YAA+B,sBAAsB,cAAc;AACnE,QAAA,YAA+B,sBAAsB,cAAc;AACjE,UAAA,YAA+B,sBACjC,cACA;AAGJ,eAAW,QAAQ,OAAO;AACxB,UAAI,KAAK,KAAK,WAAW,QAAQ,GAAG;AAClC,YAAI,CAAC,WAAW;AAER,gBAAA,UAAU,UAAU,WAAW,WAAW;AAChD,cAAI,SAAS;AACH,oBAAA,MAAM,CAAC,OAAO,YAAY,CAAC,GAAG,OAAO,YAAY,CAAC,CAAC;AAC/C,wBAAA,OAAO,IAAI,OAAO,KAAK;AAAA,UACrC;AACA,iBAAO,OAAO;AAAA,QAChB;AACiB,yBAAA,OAAO,WAAW,OAAO;AAC1C;AAAA,MACS,WAAA,KAAK,KAAK,WAAW,QAAQ,GAAG;AACzC,YAAI,CAAC,WAAW;AAAA,QAAA,OAGT;AACY,2BAAA,OAAO,WAAW,OAAO;AAC1C;AAAA,QACF;AAAA,MACS,WAAA,KAAK,KAAK,WAAW,QAAQ,GAAG;AACzC,YAAI,CAAC,WAAW;AAER,gBAAA,UAAU,UAAU,WAAW,WAAW;AAChD,cAAI,SAAS;AACH,oBAAA,MAAM,CAAC,OAAO,YAAY,CAAC,GAAG,OAAO,YAAY,CAAC,CAAC;AAC/C,wBAAA,OAAO,IAAI,OAAO,KAAK;AAAA,UACrC;AACA,iBAAO,OAAO;AAAA,QAChB;AACiB,yBAAA,OAAO,WAAW,OAAO;AAC1C;AAAA,MACF;AAAA,IACF;AAGO,WAAA,KAAK,QAAQ,YAAY;AAChC,QAAI,WAAqC;AACrC,QAAA;AACF,aAAO,KAAK,MAAM,KAAK,QAAQ,GAAG,CAAC;AACxB,iBAAA,KAAK,MAAM,IAAI;AAAA,aACnB,KAAK;AACR,UAAA;AACF,eAAO,KAAK,MAAM,KAAK,QAAQ,YAAY,CAAC;AAC5C,eAAO,KAAK,MAAM,KAAK,QAAQ,GAAG,CAAC;AACxB,mBAAA,KAAK,MAAM,IAAI;AAAA,eACnB,OAAO;AACH,mBAAA;AAAA,MACb;AAAA,IACF;AAEA,QAAI,YAAY,SAAS,WAAW,SAAS,SAAS,SAAS,OAAO;AAC9D,YAAA,IAAI,cAAc,QAAQ;AAAA,IAAA,OAC3B;AACL,UACG,EAAE,kBAAkB,uBACnB,EAAE,OAAO,SAAS,cACnB,EAAE,kBAAkB,oBAAoB,EAAE,OAAO,SAAS,QAC3D;AACA;AAAA,MACF;AAGA,aAAO,mBAAmB;AAAA,IAC5B;AAAA,EAAA,CACD;AACH,GAlIwB;ACNjB,SAAS,sBAAsB;AACpC,QAAM,gBAAgB;AACtB,QAAM,eAAe;AACrB,QAAM,kBAAkB;AAGxB,QAAM,kBAAkB;AAAA,IAAS,MAC/B,aAAa,IAAI,yBAAyB;AAAA,EAAA;AAE5C,QAAM,gBAAgB;AAAA,IAAS,MAC7B,aAAa,IAAI,8BAA8B;AAAA,EAAA;AAGjD,MAAI,kBAAyC;AAC7C,MAAI,WAAW;AACf,MAAI,gBAAgB;AAEpB,QAAM,mBAAmB,6BAAM;AAE7B,QAAI,iBAAiB;AACnB,mBAAa,eAAe;AACV,wBAAA;AAAA,IACpB;AAGI,QAAA,gBAAgB,UAAU,eAAe;AAE3C,UAAI,UAAU;AACI,wBAAA;AAChB;AAAA,MACF;AACA,YAAM,QAAQ,cAAc;AAC5B,wBAAkB,WAAW,YAAY;AACvC,cAAM,iBAAiB,cAAc;AACjC,YAAA,gBAAgB,cAAc,eAAe,aAAa;AACxD,cAAA;AACS,uBAAA;AACL,kBAAA,gBAAgB,aAAa,cAAc;AAAA,mBAC1C,KAAK;AACJ,oBAAA,MAAM,qBAAqB,GAAG;AAAA,UAAA,UACtC;AACW,uBAAA;AACX,gBAAI,eAAe;AACD,8BAAA;AACC;YACnB;AAAA,UACF;AAAA,QACF;AAAA,SACC,KAAK;AAAA,IACV;AAAA,EAAA,GAhCuB;AAoCzB;AAAA,IACE;AAAA,IACA,CAAC,eAAe;AAEd,UAAI,iBAAiB;AACnB,qBAAa,eAAe;AACV,0BAAA;AAAA,MACpB;AAGA,UACE,eAAe,iBACf,cAAc,gBAAgB,YAC9B;AACiB;MACnB;AAAA,IACF;AAAA,IACA,EAAE,WAAW,KAAK;AAAA,EAAA;AAIpB,QAAM,iBAAiB,6BAAM;AACV;EAAA,GADI;AAInB,MAAA,iBAAiB,gBAAgB,cAAc;AAEnD,cAAY,MAAM;AAChB,QAAI,iBAAiB;AACnB,mBAAa,eAAe;AACV,wBAAA;AAAA,IACpB;AACI,QAAA,oBAAoB,gBAAgB,cAAc;AAAA,EAAA,CACvD;AACH;AAvFgB;ACGT,SAAS,yBAAyB;AACvC,QAAM,gBAAgB;AACtB,QAAM,eAAe;AAErB,QAAM,6BAA6B;AAAA,IAAS,MAC1C,aAAa,IAAI,wBAAwB;AAAA,EAAA;AAG3C,QAAM,yBAAyB,6BAAM;AAC/B,QAAA,CAAC,2BAA2B,MAAO;AACvC,UAAM,WAAW,KAAK,UAAUR,IAAS,MAAM,WAAW;AAC7C,iBAAA,QAAQ,YAAY,QAAQ;AACzC,QAAI,IAAI,UAAU;AAChB,qBAAe,QAAQ,YAAY,IAAI,QAAQ,IAAI,QAAQ;AAAA,IAC7D;AAAA,EAAA,GAN6B;AASzB,QAAA,0BAA0B,8BAC9B,MACA,iBACG;AACC,QAAA,CAAC,KAAa,QAAA;AACZ,UAAA,WAAW,KAAK,MAAM,IAAI;AAChC,UAAMA,IAAS,cAAc,UAAU,MAAM,MAAM,YAAY;AACxD,WAAA;AAAA,EAAA,GAPuB;AAUhC,QAAM,kCAAkC,mCAAY;AAC5C,UAAA,eAAe,gBAAgB,wBAAwB;AACvD,UAAA,WAAW,IAAI,mBAAmB,IAAI;AAG5C,QAAI,UAAU;AACZ,YAAM,kBAAkB,eAAe,QAAQ,YAAY,QAAQ,EAAE;AACrE,UAAI,MAAM,wBAAwB,iBAAiB,YAAY,GAAG;AACzD,eAAA;AAAA,MACT;AAAA,IACF;AAGM,UAAA,gBAAgB,aAAa,QAAQ,UAAU;AAC9C,WAAA,MAAM,wBAAwB,eAAe,YAAY;AAAA,EAAA,GAd1B;AAiBxC,QAAM,sBAAsB,mCAAY;AACtC,QAAI,CAAC,aAAa,IAAI,yBAAyB,GAAG;AAC1C,YAAA,aAAa,IAAI,2BAA2B,IAAI;AAChD,YAAA,mBAAA,EAAqB;AACrB,YAAA,gBAAkB,EAAA,QAAQ,uBAAuB;AAAA,IAAA,OAClD;AACL,YAAMA,IAAS;IACjB;AAAA,EAAA,GAP0B;AAU5B,QAAM,0BAA0B,mCAAY;AACtC,QAAA,CAAC,2BAA2B,MAAO;AACnC,QAAA;AACI,YAAA,WAAW,MAAM;AACvB,UAAI,CAAC,UAAU;AACb,cAAM,oBAAoB;AAAA,MAC5B;AAAA,aACO,KAAK;AACJ,cAAA,MAAM,mCAAmC,GAAG;AACpD,YAAM,oBAAoB;AAAA,IAC5B;AAAA,EAAA,GAV8B;AAchC;AAAA,IACE,MAAM,cAAc,gBAAgB;AAAA,IACpC,CAAC,sBAAsB;AACrB,UAAI,CAAC,kBAAmB;AACxB,sBAAgB,0BAA0B,iBAAiB;AAGpC;IACzB;AAAA,EAAA;AAEE,MAAA,iBAAiB,gBAAgB,sBAAsB;AAG3D,QAAM,gBAAgB,SAAS,MAAM,cAAc,aAAa;AAChE,QAAM,iBAAiB,SAAS,MAAM,cAAc,cAAc;AAClE,QAAM,eAAe;AAAA,IACnB,MAAM;AACJ,UAAI,CAAC,cAAc,SAAS,CAAC,eAAe,OAAO;AACjD,eAAO,EAAE,OAAO,CAAA,GAAI,aAAa,GAAG;AAAA,MACtC;AAEA,YAAM,QAAQ,cAAc,MACzB,OAAO,CAAC,aAAa,UAAU,eAAe,CAAC,SAAS,UAAU,EAClE,IAAI,CAAC,aAAa,SAAS,IAAI;AAC5B,YAAA,cAAc,cAAc,MAAM;AAAA,QACtC,CAAC,aAAa,SAAS,SAAS,eAAe,OAAO;AAAA,MAAA;AAGjD,aAAA,EAAE,OAAO;IAClB;AAAA,EAAA;AAIF,QAAM,kBAAkB,KAAK;AAAA,IAC3B,gBAAgB,0BAA0B,KAAK;AAAA,EAAA;AAEjD,QAAM,oBAAoB,KAAK;AAAA,IAC7B,gBAAgB,2BAA2B,KAAK;AAAA,EAAA;AAGlD,QAAM,cAAc,CAAC,EAAE,OAAO,kBAAkB;AAC9C,QAAI,2BAA2B,OAAO;AACpC,sBAAgB,4BAA4B,KAAK,UAAU,KAAK,CAAC;AACjE,sBAAgB,6BAA6B,KAAK,UAAU,WAAW,CAAC;AAAA,IAC1E;AAAA,EAAA,CACD;AAED,QAAM,2BAA2B,6BAAM;AACjC,QAAA,CAAC,2BAA2B,MAAO;AACvC,UAAM,eAAe,iBAAiB,SAAS,KAAK,qBAAqB;AACzE,QAAI,cAAc;AAChB,oBAAc,0BAA0B;AAAA,QACtC,MAAM,gBAAgB,MAAM,GAAG,iBAAiB;AAAA,QAChD,OAAO,gBAAgB,MAAM,iBAAiB;AAAA,MAAA,CAC/C;AAAA,IACH;AAAA,EAAA,GAR+B;AAW1B,SAAA;AAAA,IACL;AAAA,IACA;AAAA,EAAA;AAEJ;AAlIgB;ACKT,MAAM,gBAAiC;AAAA,EAC5C;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,mBAAmB,gBAAgB;AAAA,IACvD,cAAc;AAAA,IACd,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,WAAW,oBAAoB;AAAA,IACzC,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,eAAe,QAAQ;AAAA,IAC/C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,wBAAwB;AAAA,IAC/C,cAAc,yBAAyB;AAAA,EACzC;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,eAAe,aAAa;AAAA,IACpD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,wBAAwB;AAAA,IAC/C,cAAc,yBAAyB;AAAA,EACzC;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,mBAAmB,aAAa;AAAA,IACpD,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,mBAAmB,cAAc;AAAA,IACrD,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,mBAAmB,YAAY;AAAA,IACnD,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,mBAAmB,mBAAmB;AAAA,IAC1D,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,cAAc,WAAW,UAAU;AAAA,IAC9C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,QAAQ,OAAO;AAAA,IACzB,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,cAAc,WAAW,MAAM;AAAA,IAC1C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,UAAU,OAAO;AAAA;AAAA,IAE3B,cAAc,6BAAO,OAAO,aAAa,OAAO,UAAU,UAA5C;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,cAAc,WAAW,cAAc;AAAA,IAClD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,cAAc,eAAe,kBAAkB,UAAU;AAAA,IACpE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,eAAe,kBAAkB,YAAY;AAAA,IACjE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,UAAU,YAAY;AAAA,IAC9C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,cAAc,QAAQ,SAAS;AAAA,IAC1C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,UAAU,WAAW;AAAA,IAC7C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,IACf,YAAY;AAAA,EACd;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,EACjB;AAAA;AAAA,EAEA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,EACjB;AAAA;AAAA,EAEA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,SAAS,SAAS;AAAA,IAC1C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,wBAAwB;AAAA,IACxD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,eAAe,oBAAoB;AAAA,IAC3D,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,SAAS,wBAAwB;AAAA,IACzD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,cAAc,iBAAiB,aAAa;AAAA,IACvD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,SAAS,CAAC,YAAY,OAAO;AAAA,IAC7B,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,MACP,EAAE,OAAO,MAAM,MAAM,UAAU;AAAA,MAC/B,EAAE,OAAO,MAAM,MAAM,KAAK;AAAA,MAC1B,EAAE,OAAO,MAAM,MAAM,UAAU;AAAA,MAC/B,EAAE,OAAO,MAAM,MAAM,MAAM;AAAA,MAC3B,EAAE,OAAO,MAAM,MAAM,MAAM;AAAA,MAC3B,EAAE,OAAO,MAAM,MAAM,WAAW;AAAA,MAChC,EAAE,OAAO,MAAM,MAAM,UAAU;AAAA,IACjC;AAAA,IACA,cAAc,6BAAM,UAAU,SAAS,MAAM,GAAG,EAAE,CAAC,KAAK,MAA1C;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,qBAAqB;AAAA,IACrD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,aAAa;AAAA,IACpC,cAAc,cAAc;AAAA,EAC9B;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,iBAAiB;AAAA,IACjD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,cAAc,MAAM,cAAc,OAAO;AAAA,IACnD,cAAc,cAAc;AAAA,EAC9B;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,wBAAwB;AAAA,IACxD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,cAAc,MAAM,cAAc,OAAO;AAAA,IACnD,cAAc,cAAc;AAAA,EAC9B;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,WAAW;AAAA,IAC/B,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,YAAY,cAAc;AAAA,IAC9C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,YAAY,gBAAgB;AAAA,IAChD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,eAAe,eAAe;AAAA,IACtD,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,eAAe,gBAAgB;AAAA,IACvD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,eAAe,sBAAsB;AAAA,IAC7D,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,eAAe,wBAAwB;AAAA,IAC/D,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,gBAAgB;AAAA,IAChD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,UAAU,wBAAC,UAAU;AACb,YAAA,UAAU,SAAS,eAAe,2BAA2B;AACnE,UAAI,SAAS;AACH,gBAAA,MAAM,UAAU,QAAQ,SAAS;AAAA,MAC3C;AAAA,IACF,GALU;AAAA,EAMZ;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,QAAQ,YAAY;AAAA,IACxC,cAAc;AAAA,IACd,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,YAAY,OAAO,QAAQ;AAAA,IACrC,wBAAwB,wBAAC,UAAkB;AAEzC,UAAI,UAAU,YAAY;AACjB,eAAA;AAAA,MACT;AACO,aAAA;AAAA,IACT,GANwB;AAAA,EAO1B;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,WAAW,UAAU,kBAAkB;AAAA;AAAA,IAEjD,cAAc,6BACZ,OAAO,aAAa,OAAO,qBAAqB,UADpC;AAAA,EAEhB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,UAAU,YAAY;AAAA,IAC9C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,IACf,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,wBAAwB,wBAAC,UAAiB;AACjC,aAAA,MAAM,IAAI,CAAC,eAAe;AAC3B,YAAA,WAAW,gBAAgB,MAAM,iBAAiB;AACpD,qBAAW,iBAAiB,IAAI;AAAA,QAClC;AACO,eAAA;AAAA,MAAA,CACR;AAAA,IACH,GAPwB;AAAA,EAQ1B;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,IACf,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,IACf,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SACE;AAAA,IACF,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,SAAS,gBAAgB;AAAA,IACjD,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,SAAS;AAAA,MACP,EAAE,OAAO,UAAU,eAAe,MAAM,WAAW;AAAA,MACnD,EAAE,OAAO,UAAU,aAAa,MAAM,SAAS;AAAA,MAC/C,EAAE,OAAO,UAAU,aAAa,MAAM,SAAS;AAAA,MAC/C,EAAE,OAAO,UAAU,aAAa,MAAM,SAAS;AAAA,IACjD;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,oBAAoB;AAAA,IACpD,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,oBAAoB;AAAA,IACpD,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,wBAAwB;AAAA,IACxD,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,wBAAwB;AAAA,IACxD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,aAAa;AAAA,IAC7C,MAAM;AAAA,IACN,cAAc,gBAAgB;AAAA,IAC9B,MAAM;AAAA,IACN,SAAS;AAAA,MACP,EAAE,OAAO,gBAAgB,MAAM,MAAM,OAAO;AAAA,MAC5C,EAAE,OAAO,gBAAgB,QAAQ,MAAM,SAAS;AAAA,MAChD,EAAE,OAAO,gBAAgB,OAAO,MAAM,QAAQ;AAAA,IAChD;AAAA,IACA,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,QAAQ,oBAAoB;AAAA,IACpD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,UAAU,eAAe;AAAA,IACjD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,WAAW,YAAY;AAAA,IAC/C,MAAM;AAAA,IACN,SACE;AAAA,IACF,cAAc;AAAA,IACd,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,WAAW,iBAAiB;AAAA,IACpD,MAAM;AAAA,IACN,SACE;AAAA,IACF,cAAc;AAAA,IACd,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,WAAW,iBAAiB;AAAA,IACpD,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,UAAU,UAAU;AAAA,IAC5C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,SACE;AAAA,IACF,cAAc,UAAU;AAAA,EAC1B;AAAA;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,UAAU,kBAAkB;AAAA,IACpD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA;AAAA,IAEN,cAAc,CAAC;AAAA,IACf,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,IACf,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,YAAY,2BAA2B;AAAA,IAC3D,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,uBAAuB,OAAe;AAE7B,aAAA,MAAM,WAAW,SAAS,IAAI,MAAM,QAAQ,WAAW,EAAE,IAAI;AAAA,IACtE;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,IACf,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,SAAS,eAAe,mBAAmB;AAAA,IACtD,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,IACd,SAAS,CAAC,UAAU,OAAO;AAAA,IAC3B,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,UAAU,kBAAkB;AAAA,IACpD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,SAAS;AAAA,IACT,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,CAAC,OAAO,aAAa;AAAA;AAAA,IAC9B,cAAc;AAAA;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU,CAAC,aAAa,WAAW,mBAAmB;AAAA,IACtD,cAAc;AAAA,IACd,MAAM;AAAA,IACN,SACE;AAAA,IACF,MAAM;AAAA,IACN,cAAc;AAAA,IACd,cAAc;AAAA,EAChB;AACF;;;;;AC9uBA,UAAM,OAAO;AAGP,UAAA,YAAY,IAA8B,IAAI;AACpD,UAAM,eAAe;AACrB,UAAM,eAAe;AACrB,UAAM,iBAAiB;AACvB,UAAM,cAAc;AACpB,UAAM,iBAAiB;AACvB,UAAM,aAAa;AACnB,UAAM,kBAAkB;AAAA,MACtB,MAAM,aAAa,IAAI,kBAAkB,MAAM;AAAA,IAAA;AAEjD,UAAM,uBAAuB;AAAA,MAAS,MACpC,aAAa,IAAI,qCAAqC;AAAA,IAAA;AAExD,UAAM,oBAAoB;AAAA,MAAS,MACjC,aAAa,IAAI,wBAAwB;AAAA,IAAA;AAE3C,UAAM,iBAAiB,SAAS,MAAM,aAAa,IAAI,sBAAsB,CAAC;AAC9E,UAAM,0BAA0B;AAAA,MAAS,MACvC,aAAa,IAAI,+BAA+B;AAAA,IAAA;AAGlD,gBAAY,MAAM;AACH,mBAAA,iBAAiB,aAAa,IAAI,2BAA2B;AAAA,IAAA,CAC3E;AAED,gBAAY,MAAM;AAChB,mBAAa,mBAAmB,aAAa;AAAA,QAC3C;AAAA,MAAA;AAAA,IACF,CACD;AAED,gBAAY,MAAM;AACV,YAAA,oBAAoB,aAAa,IAAI,iCAAiC;AAC5E,YAAM,YAAY,SAAS;AAAA,QACzB;AAAA,MAAA;AAGQ,gBAAA,QAAQ,CAAC,aAAkC;AACnD,iBAAS,aAAa;AAEtB,iBAAS,MAAM;AACf,iBAAS,KAAK;AAAA,MAAA,CACf;AAAA,IAAA,CACF;AAED;AAAA,MACE,MAAM,aAAa,IAAI,yBAAyB;AAAA,MAChD,MAAM;AACA,YAAA,CAAC,YAAY,OAAQ;AAEd,mBAAA,KAAKA,IAAS,MAAM,OAAO;AAChC,cAAA,CAAC,EAAE,QAAS;AACL,qBAAA,KAAK,EAAE,SAAS;AAErB,gBAAA,EAAE,iBAAiB,GAAG;AACxB,uCAAyB,CAAC;AAC1B,kBAAI,EAAE,eAAe;AACR,2BAAA,KAAK,EAAE,eAAe;AAC/B,2CAAyB,CAAC;AAAA,gBAC5B;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACSA,YAAA,MAAM,eAAe,IAAI;AAAA,MACpC;AAAA,IAAA;AAGF,UAAM,sBAAsB;AAC5B,UAAM,oBAAoB;AAC1B;AAAA,MACE,CAAC,MAAM,YAAY,QAAQ,MAAM,aAAa,IAAI,oBAAoB,CAAC;AAAA,MACvE,OAAO,CAAC,QAAQ,gBAAgB,MAAM;AACpC,YAAI,CAAC,OAAQ;AAEP,cAAA,oBAAoB,iBAAiB,gBAAgB;AAAA,MAC7D;AAAA,IAAA;AAEF;AAAA,MACE,MAAM,kBAAkB;AAAA,MACxB,OAAO,aAAa;AACZ,cAAA,aAAa,IAAI,sBAAsB,QAAQ;AAAA,MACvD;AAAA,IAAA;AAIF;AAAA,MACE,MACE,CAAC,eAAe,iBAAiB,eAAe,qBAAqB;AAAA,MAIvE,CAAC,CAAC,iBAAiB,qBAAqB,MAAM;AACjC,mBAAA,QAAQA,IAAS,MAAM,OAAO;AACnC,cAAA,KAAK,MAAM,iBAAiB;AAC9B,iBAAK,WAAW,yBAAyB;AAAA,UAAA,OACpC;AACL,iBAAK,WAAW;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAAA,IAAA;AAIF;AAAA,MACE,MAAM,eAAe;AAAA,MACrB,CAAC,mBAAmB;AACZ,cAAA,kBAAkB,wBAAC,SAAqB;AACjC,qBAAA,QAAQ,KAAK,QAAQ;AAC9B,mBAAO,KAAK;AAAA,UACd;AACW,qBAAA,QAAQ,KAAK,SAAS;AAC/B,mBAAO,KAAK;AAAA,UACd;AAAA,QAAA,GANsB;AASb,mBAAA,QAAQA,IAAS,MAAM,OAAO;AACvC,0BAAgB,IAAI;AACd,gBAAA,aAAa,iBAAiB,KAAK,EAAE;AAC3C,cAAI,CAAC,WAAY;AACN,qBAAA,SAAS,WAAW,QAAQ;AACrC,gBAAI,MAAM,cAAc,MAAM,WAAW,YAAY;AACnD,oBAAM,aAAa,KAAK,cAAc,MAAM,WAAW,UAAU;AACjE,kBAAI,eAAe,IAAI;AAChB,qBAAA,OAAO,UAAU,EAAE,YAAY;AAAA,cACtC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAESA,YAAA,OAAO,KAAK,MAAM,IAAI;AAAA,MACjC;AAAA,IAAA;AAGF;AAAA,MACE;AAAA,MACA;AAAA,MACA,MAAM;AACJ,mBAAW,IAAI;AAAA,UACb,UAAU;AAAA,UACV,SAAS,EAAE,+BAA+B;AAAA,UAC1C,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,MACA,EAAE,SAAS,KAAK;AAAA,IAAA;AAGlB,UAAM,sBAAsB,mCAAY;AAClC,UAAA;AACI,cAAA,WAAW,MAAM,IAAI;AACpB,eAAA,QAAQ,QAAQ,EAAE,QAAQ,CAAC,CAAC,QAAQ,OAAO,MAAM;AACjD,eAAA,OAAO,mBAAmB,QAAQ,OAAO;AAAA,QAAA,CAC/C;AAAA,eACM,OAAO;AACN,gBAAA,MAAM,oCAAoC,KAAK;AAAA,MACzD;AAAA,IAAA,GAR0B;AAWtB,UAAA,gBAAgB,IAAI,KAAK;AAC/B,UAAM,sBAAsB;AAE5B,kBAAc,SAAS;AACF;AACR;AAEb,cAAU,YAAY;AACD;AACO;AAClB;AACC;AACW;AAEpBA,UAAS,cAAc;AAEvB,qBAAe,UAAU;AAGzB,oBAAc,KAAK;AACnB,YAAM,oBAAoB;AACtB,UAAA;AACF,cAAM,aAAa;eACZ,OAAO;AACd,YAAI,iBAAiB,mBAAmB;AAC9B,kBAAA;AAAA,YACN;AAAA,UAAA;AAEF,uBAAa,WAAW,cAAc;AACtC,uBAAa,WAAW,gBAAgB;AACxC,iBAAO,SAAS;QAAO,OAClB;AACC,gBAAA;AAAA,QACR;AAAA,MACF;AACc,oBAAA,QAAQ,CAAC,YAAY;AACjC,qBAAa,WAAW,OAAO;AAAA,MAAA,CAChC;AAEK,YAAAA,IAAS,MAAM,UAAU,KAAK;AACpC,kBAAY,SAASA,IAAS;AAC9B,kBAAY,OAAO,uBAAuB;AAC1C,qBAAe,UAAU;AAEzB,aAAO,MAAMA;AACb,aAAO,QAAQA,IAAS;AAExB,oBAAc,QAAQ;AAEtBA,UAAS,OAAO,oBAAoB;AAAA,QAClCA,IAAS,OAAO;AAAA,QAChB,MAAM,YAAY,oBAAoB;AAAA,MAAA;AAIxC,wBAAkB,iBAAiB,aAAa;AAAA,QAC9C;AAAA,MAAA;AAIF,YAAM,oBAAoB;AAC1B,0BAAoB,yBAAyB;AAG7C;AAAA,QACE,MAAM,aAAa,IAAI,cAAc;AAAA,QACrC,YAAY;AACJ,gBAAA,gBAAkB,EAAA,QAAQ,8BAA8B;AACxD,gBAAA,mBAAA,EAAqB;QAC7B;AAAA,MAAA;AAGF,WAAK,OAAO;AAAA,IAAA,CACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvTD,UAAM,QAAQ;AACd,UAAM,aAAa;AACnB,UAAM,eAAe;AAErB;AAAA,MACE,MAAM,WAAW;AAAA,MACjB,CAAC,gBAAgB;AACX,YAAA,YAAY,WAAW,GAAG;AAC5B;AAAA,QACF;AAEY,oBAAA,QAAQ,CAAC,YAAY;AAC/B,gBAAM,IAAI,OAAO;AAAA,QAAA,CAClB;AACD,mBAAW,gBAAgB;MAC7B;AAAA,MACA,EAAE,MAAM,KAAK;AAAA,IAAA;AAGf;AAAA,MACE,MAAM,WAAW;AAAA,MACjB,CAAC,qBAAqB;AAChB,YAAA,iBAAiB,WAAW,GAAG;AACjC;AAAA,QACF;AAEiB,yBAAA,QAAQ,CAAC,YAAY;AACpC,gBAAM,OAAO,OAAO;AAAA,QAAA,CACrB;AACD,mBAAW,mBAAmB;MAChC;AAAA,MACA,EAAE,MAAM,KAAK;AAAA,IAAA;AAGf;AAAA,MACE,MAAM,WAAW;AAAA,MACjB,CAAC,cAAc;AACb,YAAI,WAAW;AACb,gBAAM,gBAAgB;AACtB,qBAAW,qBAAqB;AAAA,QAClC;AAAA,MACF;AAAA,IAAA;AAGF,aAAS,sBAAsB;AAC7B,YAAM,eACJ,SAAS,eAAe,qBAAqB,KAAK,mBAAmB;AACvE,YAAM,OAAO,SACV,cAAc,yBAAyB,GACtC,sBAAsB;AAC1B,UAAI,CAAC,KAAM;AAEX,mBAAa,cAAc;AAAA;AAAA,aAEhB,KAAK,MAAM,EAAE;AAAA,eACX,OAAO,cAAc,KAAK,OAAO,KAAK,SAAS,EAAE;AAAA;AAAA;AAAA,IAGhE;AAdS;AAgBT,aAAS,qBAAqB;AACtB,YAAA,QAAQ,SAAS,cAAc,OAAO;AAC5C,YAAM,KAAK;AACF,eAAA,KAAK,YAAY,KAAK;AACxB,aAAA;AAAA,IACT;AALS;AAOT;AAAA,MACE,MAAM,aAAa,IAAI,kBAAkB;AAAA,MACzC,MAAM,SAAS,mBAAmB;AAAA,MAClC,EAAE,WAAW,KAAK;AAAA,IAAA;AAEpB;AAAA,MACE,MAAM,aAAa,IAAI,wBAAwB;AAAA,MAC/C,MAAM,SAAS,mBAAmB;AAAA,MAClC,EAAE,WAAW,KAAK;AAAA,IAAA;;;;;;;;;;;ACzDd,UAAA,EAAE,GAAAD,OAAM;AACd,UAAM,QAAQ;AAEd,UAAM,gBAAgB;AACtB,UAAM,4BAA4B,mCAAY;AACtC,YAAA,eAAe,IAAI,MAAM,UAAU;AACnC,YAAA,uBAAuB,0BAA0B,YAAY;AACnE,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc;AAAA,MAAA;AAEhB,YAAM,YAAY,mBAAmB;AAAA,IAAA,GATL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACUlC,MAAM,gBAAgB;;;;AAFtB,UAAM,qBAAqB;AAC3B,UAAM,EAAE,WAAA,IAAe,YAAY,kBAAkB;AAGrD,UAAM,eAAe;AACrB,UAAM,gBAAgB;AAAA,MAAS,MAC7B,aAAa,IAAI,mCAAmC;AAAA,IAAA;AAGhD,UAAA,cAAc,wBAAC,cAAuB;AACtC,UAAA;AACJ,UAAI,WAAW;AACP,cAAA,gBAAgB,WAAW,QAAQ;AACzC,mBAAW,KAAK,IAAI,gBAAgB,GAAG,cAAc,KAAK;AAAA,MAAA,OACrD;AACC,cAAA,gBAAgB,WAAW,QAAQ;AAC9B,mBAAA,KAAK,MAAM,gBAAgB,CAAC;AAAA,MACzC;AAEA,iBAAW,QAAQ;AAAA,IAAA,GAVD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwCpB,UAAM,iBAAiB;AACjB,UAAA,kBAAkB,YAAY,8BAAA,CAA+B;AACnE,UAAM,EAAE,MAAM,UAAA,IAAc,YAAY,sBAAuB,CAAA;AAEzD,UAAA,EAAE,GAAAA,OAAM;AACR,UAAA,0BAA0B,SAAS,OAAO;AAAA,MAC9C,UAAU;AAAA,QACR,KAAK;AAAA,QACL,OAAOA,GAAE,UAAU;AAAA,QACnB,SAASA,GAAE,sBAAsB;AAAA,QACjC,SAAS,6BAAM;AACb,oBAAU,QAAQ;AAAA,QACpB,GAFS;AAAA,MAGX;AAAA,MACA,SAAS;AAAA,QACP,KAAK;AAAA,QACL,OAAO,GAAGA,GAAE,UAAU,CAAC,KAAKA,GAAE,cAAc,CAAC;AAAA,QAC7C,SAASA,GAAE,qBAAqB;AAAA,QAChC,SAAS,6BAAM;AACb,oBAAU,QAAQ;AAAA,QACpB,GAFS;AAAA,MAGX;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,OAAO,GAAGA,GAAE,UAAU,CAAC,KAAKA,GAAE,eAAe,CAAC;AAAA,QAC9C,SAASA,GAAE,sBAAsB;AAAA,QACjC,SAAS,6BAAM;AACb,oBAAU,QAAQ;AAAA,QACpB,GAFS;AAAA,MAGX;AAAA,IACA,EAAA;AAEF,UAAM,0BAA0B;AAAA,MAC9B,MAAM,wBAAwB,MAAM,UAAU,KAAK;AAAA,IAAA;AAErD,UAAM,qBAAqB;AAAA,MAAS,MAClC,OAAO,OAAO,wBAAwB,KAAK;AAAA,IAAA;AAG7C,UAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,gBAAgB,MAAM,KAAK;AACpE,UAAM,kBAAkB;AAAA,MACtB,MAAM,gBAAgB,MAAM,QAAQ,KAAK,UAAU,UAAU;AAAA,IAAA;AAG/D,UAAM,eAAe;AACf,UAAA,cAAc,8BAAO,MAAa;AACtC,YAAM,YACJ,cAAc,KAAK,EAAE,WACjB,2BACA;AACA,YAAA,aAAa,QAAQ,SAAS;AAAA,IAAA,GALlB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkDpB,MAAM,mBAAmB;;;;AA5JzB,UAAM,gBAAgB;AAEtB,UAAM,UAAU;AAAA,MACd,MAAM,cAAc,IAAI,kBAAkB,MAAM;AAAA,IAAA;AAG5C,UAAA,WAAW,IAAwB,IAAI;AACvC,UAAA,gBAAgB,IAAwB,IAAI;AAC5C,UAAA,WAAW,gBAAgB,6BAA6B,KAAK;AAC7D,UAAA,iBAAiB,gBAAgB,+BAA+B;AAAA,MACpE,GAAG;AAAA,MACH,GAAG;AAAA,IAAA,CACJ;AACK,UAAA;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA,IACE,aAAa,UAAU;AAAA,MACzB,cAAc,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,MAC3B,QAAQ;AAAA,MACR,kBAAkB,SAAS;AAAA,IAAA,CAC5B;AAGD;AAAA,MACE,CAAC,GAAG,CAAC;AAAA,MACL,CAAC,CAAC,MAAM,IAAI,MAAM;AAChB,uBAAe,QAAQ,EAAE,GAAG,MAAM,GAAG;MACvC;AAAA,MACA,EAAE,UAAU,IAAI;AAAA,IAAA;AAIlB,UAAM,qBAAqB,6BAAM;AAC/B,UAAI,SAAS,OAAO;AAClB,cAAM,cAAc,OAAO;AAC3B,cAAM,eAAe,OAAO;AACtB,cAAA,YAAY,SAAS,MAAM;AAC3B,cAAA,aAAa,SAAS,MAAM;AAE9B,YAAA,cAAc,KAAK,eAAe,GAAG;AACvC;AAAA,QACF;AAGA,YAAI,eAAe,MAAM,MAAM,KAAK,eAAe,MAAM,MAAM,GAAG;AAEhE,YAAE,QAAQU,cAAM,MAAA,eAAe,MAAM,GAAG,GAAG,cAAc,SAAS;AAClE,YAAE,QAAQA,cAAM,MAAA,eAAe,MAAM,GAAG,GAAG,eAAe,UAAU;AAC/C;AACrB;AAAA,QACF;AAGA,YAAI,EAAE,UAAU,KAAK,EAAE,UAAU,GAAG;AAClC,YAAE,QAAQA,cAAO,OAAA,cAAc,aAAa,GAAG,GAAG,cAAc,SAAS;AACzE,YAAE,QAAQA,cAAA;AAAA,YACR,eAAe,aAAa;AAAA,YAC5B;AAAA,YACA,eAAe;AAAA,UAAA;AAEI;QACvB;AAAA,MACF;AAAA,IAAA,GA9ByB;AAgC3B,cAAU,kBAAkB;AACtB,UAAA,SAAS,OAAO,eAAe;AACnC,UAAI,YAAY;AACd,cAAM,SAAS,kBAAkB;AAAA,MACnC;AAAA,IAAA,CACD;AAED,UAAM,gBAAgB,IAAI;AAAA,MACxB,GAAG,EAAE;AAAA,MACL,GAAG,EAAE;AAAA,MACL,aAAa,OAAO;AAAA,MACpB,cAAc,OAAO;AAAA,IAAA,CACtB;AACD,UAAM,uBAAuB,6BAAM;AACjC,oBAAc,QAAQ;AAAA,QACpB,GAAG,EAAE;AAAA,QACL,GAAG,EAAE;AAAA,QACL,aAAa,OAAO;AAAA,QACpB,cAAc,OAAO;AAAA,MAAA;AAAA,IACvB,GAN2B;AAQ7B;AAAA,MACE;AAAA,MACA,CAAC,kBAAkB;AACjB,YAAI,CAAC,eAAe;AAEG;QACvB;AAAA,MACF;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IAAA;AAGpB,UAAM,qBAAqB,6BAAM;AAC/B,UAAI,SAAS,OAAO;AAClB,cAAM,cAAc,OAAO;AAC3B,cAAM,eAAe,OAAO;AACtB,cAAA,YAAY,SAAS,MAAM;AAC3B,cAAA,aAAa,SAAS,MAAM;AAG5B,cAAA,eAAe,cAAc,MAAM;AACzC,cAAM,gBACJ,cAAc,MAAM,eAAe,cAAc,MAAM,IAAI;AACvD,cAAA,cAAc,cAAc,MAAM;AACxC,cAAM,iBACJ,cAAc,MAAM,gBAAgB,cAAc,MAAM,IAAI;AAG9D,cAAM,YAAY;AAAA,UAChB,EAAE,MAAM,QAAQ,UAAU,aAAa;AAAA,UACvC,EAAE,MAAM,SAAS,UAAU,cAAc;AAAA,UACzC,EAAE,MAAM,OAAO,UAAU,YAAY;AAAA,UACrC,EAAE,MAAM,UAAU,UAAU,eAAe;AAAA,QAAA;AAE7C,cAAM,cAAc,UAAU;AAAA,UAAO,CAAC,KAAK,SACzC,KAAK,WAAW,IAAI,WAAW,OAAO;AAAA,QAAA;AAIxC,cAAM,gBACJ,cAAc,MAAM,IAAI,cAAc,MAAM;AAC9C,cAAM,kBACJ,cAAc,MAAM,IAAI,cAAc,MAAM;AAG1C,YAAA,YAAY,SAAS,QAAQ;AAC/B,YAAE,QAAQ,YAAY;AACtB,YAAE,QAAQ,gBAAgB;AAAA,QAAA,WACjB,YAAY,SAAS,SAAS;AACrC,YAAA,QAAQ,cAAc,YAAY,YAAY;AAChD,YAAE,QAAQ,gBAAgB;AAAA,QAAA,WACjB,YAAY,SAAS,OAAO;AACrC,YAAE,QAAQ,kBAAkB;AAC5B,YAAE,QAAQ,YAAY;AAAA,QAAA,OACjB;AAEL,YAAE,QAAQ,kBAAkB;AAC1B,YAAA,QAAQ,eAAe,aAAa,YAAY;AAAA,QACpD;AAGA,UAAE,QAAQA,cAAAA,MAAM,EAAE,OAAO,GAAG,cAAc,SAAS;AACnD,UAAE,QAAQA,cAAAA,MAAM,EAAE,OAAO,GAAG,eAAe,UAAU;AAAA,MACvD;AAAA,IAAA,GAnDyB;AAsDV,qBAAA,QAAQ,UAAU,kBAAkB;AAE/C,UAAA,aAAa,OAAmC,YAAY;AAC5D,UAAA,gBAAgB,mBAAmB,UAAU;AAE7C,UAAA,2BAA2B,SAAS,MAAM;AAC1C,UAAA,CAAC,SAAS,OAAO;AACZ,eAAA;AAAA,MACT;AACA,YAAM,EAAE,OAAW,IAAA,SAAS,MAAM,sBAAsB;AAClD,YAAA,kBAAkB,EAAE,QAAQ;AAC5B,YAAA,gBAAgB,cAAc,OAAO;AAE3C,YAAM,gBACJ,KAAK,IAAI,iBAAiB,aAAa,IACvC,KAAK,IAAI,EAAE,OAAO,cAAc,IAAI,KAAK;AAC3C,aAAO,gBAAgB;AAAA,IAAA,CACxB;AAEK,UAAA,YAAY,CAAC,kBAAkB;AACnC,UAAI,CAAC,eAAe;AAElB,iBAAS,QAAQ,yBAAyB;AAAA,MAAA,OACrC;AAEL,iBAAS,QAAQ;AAAA,MACnB;AAAA,IAAA,CACD;AAEK,UAAA,WAAW,YAAoB,SAAS;AACxC,UAAA,CAAC,YAAY,wBAAwB,GAAG,CAAC,CAAC,UAAU,WAAW,MAAM;AACzE,eAAS,KAAK,mBAAmB;AAAA,QAC/B,YAAY;AAAA,QACZ,eAAe;AAAA,MAAA,CAChB;AAAA,IAAA,CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClMD,UAAM,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkBzB,UAAM,eAAe;AACrB,UAAM,oBAAoB;AAAA,MAAS,MACjC,aAAa,IAAI,kBAAkB,MAAM,QAAQ,SAAS;AAAA,IAAA;AAG5D,UAAM,iBAAiB;AACjB,UAAA,EAAE,GAAAV,OAAM;AACR,UAAA,oBAAoB,wBAAC,SAA6B;AAChD,YAAA,QAAQ,OAAO,KAAK,UAAU,aAAa,KAAK,MAAA,IAAU,KAAK;AAC/D,YAAA,kBAAkB,QACpBA,GAAE,cAAc,iBAAiB,KAAK,CAAC,IAAI,KAAK,IAChD;AAEG,aAAA;AAAA,QACL,GAAG;AAAA,QACH,OAAO;AAAA,QACP,OAAO,KAAK,OAAO,IAAI,iBAAiB;AAAA,MAAA;AAAA,IAC1C,GAVwB;AAa1B,UAAM,kBAAkB;AAAA,MAAS,MAC/B,eAAe,UAAU,IAAI,iBAAiB;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACYhD,UAAM,OAAO;AAIb,UAAM,EAAE,iBAAiB,WAAW,iBAAiB,eAAe;AACpE,UAAM,cAAc;AACpB,UAAM,gBAAgB;AAEtB,UAAM,yBAAyB,6BAAM;AACnC,oBAAc,mBAAmB,MAAM;AACvC,WAAK,OAAO;AAAA,IAAA,GAFiB;AAK/B,UAAM,cAAc,6BAAM;AACxB,oBAAc,uBAAuB;AACrC,WAAK,OAAO;AAAA,IAAA,GAFM;AAKpB,UAAM,uBAAuB,6BAAM;AAC1B,aAAA,KAAK,sDAAsD,QAAQ;AAC1E,WAAK,OAAO;AAAA,IAAA,GAFe;AAK7B,cAAU,MAAM;AACd,WAAK,YAAY;IAAa,CAC/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/DD,UAAM,EAAE,YAAY,aAAa,IAAI,eAAe;AAE9C,UAAA,UAAU,IAAyC,IAAI;AAC7D,UAAM,WAAW;AAAA,MACf,MAAM,aAAa,SAAS;AAAA,IAAA;AAG9B,UAAM,eAAe,6BAAM;AACzB,cAAQ,OAAO;IAAK,GADD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoBrB,UAAM,iBAAiB;AACvB,UAAM,eAAe;AAErB,UAAM,uBAAuB;AAAA,MAAS,MACpC,aAAa,IAAI,qCAAqC;AAAA,IAAA;AAExD,UAAM,cAAc,SAAS,MAAM,aAAa,IAAI,kBAAkB,CAAC;AACvE,UAAM,kBAAkB,SAAS,MAAM,YAAY,UAAU,UAAU;AACvE,UAAM,cAAc;AAAA,MAClB,MAAM,gBAAgB,SAAS,CAAC,eAAe;AAAA,IAAA;AAG3C,UAAA,YAAY,IAA2B,IAAI;AAEjD,cAAU,MAAM;AACd,UAAI,UAAU,OAAO;AACnB,kBAAU,MAAM,YAAY,IAAI,KAAK,OAAO;AAAA,MAC9C;AAAA,IAAA,CACD;AAEK,UAAA,aAAa,IAA2B,IAAI;AAClD,YAAQ,cAAc,UAAU;AAC1B,UAAA,WAAW,YAAoB,SAAS;AACxC,UAAA,aAAa,IAAI,KAAK;AACtB,UAAA,cAAc,IAAI,KAAK;AACpB,aAAA,GAAG,CAAC,OAAe,YAAiB;AAC3C,UAAI,UAAU,mBAAmB;AAC/B,mBAAW,QAAQ,QAAQ;AACf,oBAAA,QAAQ,QAAQ,iBAAiB,QAAQ;AAAA,MACvD;AAAA,IAAA,CACD;AAED,cAAU,MAAM;AACd,UAAI,cAAc;AAChB,oBAAA,EAAc,YAAY;AAAA,UACxB,QAAQ,WAAW,OAAO,wBAAwB,UAAU;AAAA,QAAA,CAC7D;AAAA,MACH;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9FD,MAAM,gBAAgB;AACtB,MAAM,eAAe;AAEd,MAAM,qBAAqB,6BAAM;AACtC,QAAM,iBAAiB;AACvB,QAAM,eAAe;AACrB,QAAM,gBAAgB;AAEtB,QAAM,gBAAgB;AAAA,IAAS,MAC7B,eAAe,SACX,KACA,IAAI,KAAK,MAAM,eAAe,oBAAoB,GAAG,CAAC;AAAA,EAAA;AAG5D,QAAM,iBAAiB;AAAA,IACrB,MAAM,aAAa,IAAI,kBAAkB,MAAM;AAAA,EAAA;AAGjD,QAAM,gBAAgB;AAAA,IAAS,MAC7B,cAAc,gBAAgB,cAC9B,CAAC,cAAc,gBAAgB,cAC3B,OACA;AAAA,EAAA;AAEA,QAAA,mBAAmB,SAAS,MAAM;AAChC,UAAA,eAAe,cAAc,gBAAgB;AACnD,WAAO,eACH,cAAc,QAAQ,eAAe,eACrC;AAAA,EAAA,CACL;AAED,QAAM,qBAAqB;AAAA,IAAS,MAClC,eAAe,iBAAiB,eAAe,wBAC3C,GAAG,cAAc,KAAK,IAAI,KAAK,MAAM,eAAe,wBAAwB,GAAG,CAAC,MAAM,eAAe,cAAc,IAAI,KACvH;AAAA,EAAA;AAGN,QAAM,gBAAgB;AAAA,IACpB,MACE,cAAc,SACb,eAAe,QAAQ,iBAAiB,QAAQ;AAAA,EAAA;AAGrD,QAAM,QAAQ,SAAS,MAAM,mBAAmB,SAAS,cAAc,KAAK;AAC5E,WAAS,KAAK;AAChB,GA1CkC;ACmB3B,SAAS,kBAAkC;AAChD,QAAM,kBAAkB;AACxB,QAAM,gBAAgB;AACtB,QAAM,gBAAgB;AACtB,QAAM,oBAAoB;AAC1B,QAAM,sBAAsB;AAC5B,QAAM,aAAa;AACb,QAAA,aAAa,6BAAM,cAAc,gBAAgB,eAApC;AAEnB,QAAM,mBAAmB,6BAAoB;AACrC,UAAA,gBAAgB,IAAI,OAAO;AACjC,UAAM,SAAuB,CAAA;AAC7B,QAAI,eAAe;AACjB,iBAAW,KAAK,eAAe;AACvB,cAAA,OAAO,cAAc,CAAC;AAC5B,eAAO,KAAK,IAAI;AAAA,MAClB;AAAA,IACF;AACO,WAAA;AAAA,EAAA,GATgB;AAYnB,QAAA,0BAA0B,wBAAC,SAA0B;AACxC,qBAAA,EAAE,QAAQ,CAAC,SAAS;AAC/B,UAAA,KAAK,SAAS,MAAM;AACtB,aAAK,OAAO,gBAAgB;AAAA,MAAA,OACvB;AACL,aAAK,OAAO;AAAA,MACd;AAAA,IAAA,CACD;AAAA,EAAA,GAP6B;AAUhC,QAAM,WAAW;AAAA,IACf;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM,gBAAgB,kBAAkB,GAAxC;AAAA,IACZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,YAAI,GAAG;MACT,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM,gBAAgB,oBAAoB,GAA1C;AAAA,IACZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACd,cAAA,WAAW,iBAAmB,EAAA;AACpC,YAAI,CAAC,SAAU;AAET,cAAA,gBAAgB,aAAa,QAAQ;AAAA,MAC7C,GALU;AAAA,IAMZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACd,cAAA,WAAW,iBAAmB,EAAA;AACpC,YAAI,CAAC,SAAU;AAET,cAAA,gBAAgB,eAAe,QAAQ;AAAA,MAC/C,GALU;AAAA,IAMZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACd,cAAA,gBAAgB,eAAe,YAAY,UAAU;AAAA,MAC7D,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACd,cAAA,gBAAgB,eAAe,gBAAgB,QAAQ;AAAA,MAC/D,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,mCAAY;AACd,cAAA,WAAA,GAAc;MACtB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,mCAAY;AACd,cAAA,WAAA,GAAc;MACtB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACd,cAAM,eAAe;AACrB,YACE,CAAC,aAAa,IAAI,oBAAoB,KACtC,QAAQ,iBAAiB,GACzB;AACA,cAAI,MAAM;AACV,cAAI,MAAM;AACV,cAAI,oBAAoB,cAAc;AAAA,QACxC;AAAA,MACF,GAVU;AAAA,IAWZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACd,4BAAA,EAAsB;MACxB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACd,YAAI,cAAc;AAAA,MACpB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,mCAAY;AACpB,cAAM,IAAI;MACZ,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,mCAAY;AACpB,cAAM,IAAI;AACV,mBAAW,IAAI;AAAA,UACb,UAAU;AAAA,UACV,SAAS,EAAE,eAAe;AAAA,UAC1B,QAAQ,EAAE,2BAA2B;AAAA,UACrC,MAAM;AAAA,QAAA,CACP;AAAA,MACH,GARU;AAAA,IASZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,mCAAY;AACpB,cAAM,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC;AACrC,mBAAW,IAAI;AAAA,UACb,UAAU;AAAA,UACV,SAAS,EAAE,aAAa;AAAA,UACxB,QAAQ,EAAE,mCAAmC;AAAA,UAC7C,MAAM;AAAA,QAAA,CACP;AAAA,MACH,GARU;AAAA,IASZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACd,sBAAc,4BAA4B;AAAA,MAC5C,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACR,cAAA,KAAK,IAAI,OAAO;AACnB,WAAA;AAAA,UACD,GAAG,QAAQ;AAAA,UACX,GAAG,UAAU,CAAC,GAAG,QAAQ,QAAQ,GAAG,GAAG,QAAQ,SAAS,CAAC,IAAI;AAAA,QAAA;AAE3D,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GAPU;AAAA,IAQZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACR,cAAA,KAAK,IAAI,OAAO;AACnB,WAAA;AAAA,UACD,GAAG,QAAQ;AAAA,UACX,GAAG,UAAU,CAAC,GAAG,QAAQ,QAAQ,GAAG,GAAG,QAAQ,SAAS,CAAC,IAAI;AAAA,QAAA;AAE3D,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GAPU;AAAA,IAQZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACV,YAAA,IAAI,OAAO,OAAO;AACpB,qBAAW,IAAI;AAAA,YACb,UAAU;AAAA,YACV,SAAS,EAAE,2BAA2B;AAAA,YACtC,MAAM;AAAA,UAAA,CACP;AACD;AAAA,QACF;AACA,YAAI,OAAO;MACb,GAVU;AAAA,IAWZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU,6BAAM;AACd,YAAI,OAAO,WAAW,IAAI,CAAC,IAAI,OAAO,WAAW;AAAA,MACnD,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MAEd,WAAW,MAAM;AACf,cAAM,eAAe;AACrB,YAAI,sBAAsB,UAAU;AAEpC,eAAO,YAAY;AACX,gBAAA,cAAc,aAAa,IAAI,sBAAsB;AAEvD,cAAA,gBAAgB,UAAU,aAAa;AAEnC,kBAAA,aAAa,IAAI,wBAAwB,mBAAmB;AAAA,UAAA,OAC7D;AAEiB,kCAAA;AACtB,kBAAM,aAAa;AAAA,cACjB;AAAA,cACA,UAAU;AAAA,YAAA;AAAA,UAEd;AAAA,QAAA;AAAA,MACF,GACC;AAAA,IACL;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACd,cAAA,aAAa,sBAAwB,EAAA;AACrC,cAAA,IAAI,YAAY,GAAG,UAAU;AAAA,MACrC,GAHU;AAAA,IAIZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACd,cAAA,aAAa,sBAAwB,EAAA;AACrC,cAAA,IAAI,YAAY,IAAI,UAAU;AAAA,MACtC,GAHU;AAAA,IAIZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACd,cAAA,aAAa,sBAAwB,EAAA;AAC3C,cAAM,eAAe,iBAClB,EAAA,OAAO,CAAC,SAAS,KAAK,YAAY,UAAU,WAAW,EACvD,IAAI,CAAC,SAAS,KAAK,EAAE;AACpB,YAAA,aAAa,WAAW,GAAG;AAC7B,qBAAW,IAAI;AAAA,YACb,UAAU;AAAA,YACV,SAAS,EAAE,8BAA8B;AAAA,YACzC,QAAQ,EAAE,uCAAuC;AAAA,YACjD,MAAM;AAAA,UAAA,CACP;AACD;AAAA,QACF;AACA,cAAM,IAAI,YAAY,GAAG,YAAY,YAAY;AAAA,MACnD,GAfU;AAAA,IAgBZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,sBAAc,mBAAmB;AAAA,MACnC,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACR,cAAA,EAAE,OAAW,IAAA;AACf,YAAA,CAAC,OAAO,eAAe,MAAM;AAC/B,qBAAW,IAAI;AAAA,YACb,UAAU;AAAA,YACV,SAAS,EAAE,8BAA8B;AAAA,YACzC,QAAQ,EAAE,wCAAwC;AAAA,YAClD,MAAM;AAAA,UAAA,CACP;AACD;AAAA,QACF;AACM,cAAA,QAAQ,IAAI;AACZ,cAAA,UAAU,kBAAkB;AAAA,UAChC;AAAA,QAAA;AAEI,cAAA,SAAS,OAAO,eAAe,OAAO;AACrC,eAAA,OAAO,IAAI,KAAK;AACvB,8BAAsB,oBAAoB;AAAA,MAC5C,GAlBU;AAAA,IAmBZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACpB,cAAM,gBAAgB;MACxB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACpB,cAAM,gBAAgB;MACxB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,gCAAwB,gBAAgB,KAAK;AACzC,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GAHU;AAAA,IAIZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,gCAAwB,gBAAgB,MAAM;AAC1C,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GAHU;AAAA,IAIZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACG,yBAAA,EAAE,QAAQ,CAAC,SAAS;AAC9B,eAAA,IAAI,CAAC,KAAK,MAAM;AAAA,QAAA,CACtB;AACG,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GALU;AAAA,IAMZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACH,mBAAA,QAAQ,IAAI,OAAO,eAAe;AACvC,cAAA,gBAAgB,cAAc,gBAAgB,aAAa;AACxD,iBAAA,IAAI,CAAC,KAAK,MAAM;AAAA,UACvB;AAAA,QACF;AACI,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GAPU;AAAA,IAQZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACG,yBAAA,EAAE,QAAQ,CAAC,SAAS;AAC7B,gBAAA,cAAc,KAAK;AACpB,eAAA,QAAQ,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;AAAA,QAAA,CAC9C;AACG,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GANU;AAAA,IAOZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACG,yBAAA,EAAE,QAAQ,CAAC,SAAS;AACnC,eAAK,SAAS;AAAA,QAAA,CACf;AACG,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GALU;AAAA,IAMZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,WAAW,MAAM;AACf,YAAI,oBAA4B,2BAA2B;AAC3D,YAAI,qBAA6B,4BAA4B;AAE7D,eAAO,YAAY;AACjB,gBAAM,eAAe;AACrB,gBAAM,QAAQ,kBAAkB;AAChC,cAAI,MAAM,aAAa;AACrB,iCAAqB,MAAM;AACrB,kBAAA,aAAa,IAAI,sBAAsB,iBAAiB;AAAA,UAAA,OACzD;AACL,gCAAoB,MAAM;AACpB,kBAAA,aAAa,IAAI,sBAAsB,kBAAkB;AAAA,UACjE;AAAA,QAAA;AAAA,MACF,GACC;AAAA,IACL;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,4BAAA,EAAsB;MACxB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,0BAAA,EAAoB;MACtB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACH,mBAAA,SAAS,IAAI,OAAO,eAAe;AAC5C,cAAI,iBAAiB,aAAa;AAChC,kBAAM,qBAAqB;AACrB,kBAAA,UAAU,kBAAkB;AAAA,cAChC;AAAA,YAAA;AAEI,kBAAA,SAAS,MAAM,UAAU,OAAO;AACtC,gBAAI,MAAM;UACZ;AAAA,QACF;AAAA,MACF,GAXU;AAAA,IAYZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU,6BAAM;AACP,eAAA;AAAA,UACL;AAAA,UACA;AAAA,QAAA;AAAA,MAEJ,GALU;AAAA,IAMZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU,6BAAM;AACP,eAAA,KAAK,2BAA2B,QAAQ;AAAA,MACjD,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU,6BAAM;AACP,eAAA,KAAK,iCAAiC,QAAQ;AAAA,MACvD,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,0BAAA,EAAoB;MACtB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,sBAAc,mBAAmB,OAAO;AAAA,MAC1C,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACd,cAAA,gBAAgB,kBAAkB,cAAc,cAAe;AAAA,MACvE,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACpB,YAAI,cAAc;AACV,gBAAA,gBAAgB,cAAc,cAAc,cAAc;AAAA,MACpE,GAHU;AAAA,IAIZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,sBAAc,sBAAsB;AAAA,UAClC,OAAO,EAAE,YAAY;AAAA,UACrB,UAAU,EAAE,2BAA2B;AAAA,UACvC,YAAY;AAAA,YACV,WAAW;AAAA,YACX,eAAe,CAAC,eAAe,UAAU;AAAA,UAC3C;AAAA,QAAA,CACD;AAAA,MACH,GATU;AAAA,IAUZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,sBAAc,sBAAsB;AAAA,UAClC,OAAO,EAAE,iCAAiC;AAAA,UAC1C,UAAU,EAAE,uCAAuC;AAAA,UACnD,YAAY;AAAA,YACV,WAAW;AAAA,YACX,eAAe,CAAC,YAAY,QAAQ,eAAe,UAAU;AAAA,UAC/D;AAAA,QAAA,CACD;AAAA,MACH,GATU;AAAA,IAUZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,cAAc;AAAA,MACd,UAAU,6BAAM;AACP,eAAA,KAAK,4BAA4B,QAAQ;AAAA,MAClD,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,YAAI,OAAO;AACP,YAAA,OAAO,SAAS,MAAM,IAAI;AAAA,MAChC,GAHU;AAAA,IAIZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,sBAAc,kBAAkB;AAAA,MAClC,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,6BAAM;AACd,sBAAc,0BAA0B;AAAA,MAC1C,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACpB,cAAM,cAAc;MACtB,GAFU;AAAA,IAGZ;AAAA,IACA;AAAA,MACE,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,cAAc;AAAA,MACd,UAAU,mCAAY;AACpB,cAAM,oBAAoB;MAC5B,GAFU;AAAA,IAGZ;AAAA,EAAA;AAGK,SAAA,SAAS,IAAI,CAAC,aAAa,EAAE,GAAG,SAAS,QAAQ,SAAW,EAAA;AACrE;AA1oBgB;ACxBT,MAAM,qBAAqB,6BAAM;AACtC,QAAM,iBAAiB;AACjB,QAAA,UAAU,WAAW,cAAc;AACzC,QAAM,iBAAiB;AACvB,QAAM,cAAc;AAEpB;AAAA,IACE,CAAC,MAAM,eAAe,mBAAmB,MAAM,eAAe,MAAM;AAAA,IACpE,CAAC,CAAC,UAAU,MAAM,MAAM;AACtB,UAAI,QAAQ;AACV,gBAAQ,QAAQ;AAAA,MAAA,OACX;AACL,cAAM,QAAQ,KAAK,MAAM,WAAW,WAAW;AACvC,gBAAA,QAAQ,+CAA+C,KAAK;AAAA,MACtE;AAAA,IACF;AAAA,EAAA;AAEJ,GAjBkC;ACLtB,IAAA,wCAAAW,yBAAL;AACLA,uBAAA,YAAa,IAAA;AACbA,uBAAA,MAAO,IAAA;AACPA,uBAAA,YAAa,IAAA;AACbA,uBAAA,OAAQ,IAAA;AAJEA,SAAAA;AAAA,GAAA,uBAAA,CAAA,CAAA;AAOA,IAAA,6BAAAC,cAAL;AACLA,YAAA,OAAQ,IAAA;AACRA,YAAA,MAAO,IAAA;AACPA,YAAA,SAAU,IAAA;AACVA,YAAA,OAAQ,IAAA;AACRA,YAAA,UAAW,IAAA;AALDA,SAAAA;AAAA,GAAA,YAAA,CAAA,CAAA;AAQA,IAAA,iCAAAC,kBAAL;AACLA,gBAAA,KAAM,IAAA;AACNA,gBAAA,MAAO,IAAA;AACPA,gBAAA,QAAS,IAAA;AACTA,gBAAA,QAAS,IAAA;AAJCA,SAAAA;AAAA,GAAA,gBAAA,CAAA,CAAA;AAOA,IAAA,+BAAAC,gBAAL;AAELA,cAAA,MAAO,IAAA;AAEPA,cAAA,SAAU,IAAA;AAEVA,cAAA,QAAS,IAAA;AANCA,SAAAA;AAAA,GAAA,cAAA,CAAA,CAAA;AASA,IAAA,+BAAAC,gBAAL;AAELA,cAAA,MAAO,IAAA;AAEPA,cAAA,SAAU,IAAA;AAEVA,cAAA,QAAS,IAAA;AANCA,SAAAA;AAAA,GAAA,cAAA,CAAA,CAAA;AASA,IAAA,2CAAAC,4BAAL;AACLA,0BAAA,MAAO,IAAA;AACPA,0BAAA,MAAO,IAAA;AACPA,0BAAA,MAAO,IAAA;AACPA,0BAAA,MAAO,IAAA;AACPA,0BAAA,MAAO,IAAA;AACPA,0BAAA,WAAY,IAAA;AACZA,0BAAA,SAAU,IAAA;AAPAA,SAAAA;AAAA,GAAA,0BAAA,CAAA,CAAA;AAUA,IAAA,yCAAAC,0BAAL;AACLA,wBAAA,MAAO,IAAA;AACPA,wBAAA,OAAQ,IAAA;AACRA,wBAAA,MAAO,IAAA;AACPA,wBAAA,SAAU,IAAA;AAJAA,SAAAA;AAAA,GAAA,wBAAA,CAAA,CAAA;AAOA,IAAA,mCAAAC,oBAAL;AACLA,kBAAA,MAAO,IAAA;AACPA,kBAAA,SAAU,IAAA;AACVA,kBAAA,UAAW,IAAA;AACXA,kBAAA,YAAa,IAAA;AACbA,kBAAA,SAAU,IAAA;AACVA,kBAAA,QAAS,IAAA;AACTA,kBAAA,KAAM,IAAA;AAPIA,SAAAA;AAAA,GAAA,kBAAA,CAAA,CAAA;ACnCL,MAAM,wBAA6C;AAAA;AAAA,EAExD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,UAAU;AAAA,IACjC,cAAc,WAAW;AAAA,IACzB,UAAU,wBAAC,UAAsB;AAC/B,cAAQ,OAAO;AAAA,QACb,KAAK,WAAW;AACd,iBAAO;QACT,KAAK,WAAW;AACP,iBAAA;AAAA,YACL,CAAC,aAAa,GAAG;AAAA,UAAA;AAAA,QAErB,KAAK,WAAW;AACP,iBAAA;AAAA,YACL,CAAC,qBAAqB,GAAG;AAAA,UAAA;AAAA,MAE/B;AAAA,IACF,GAbU;AAAA,EAcZ;AACF;AAEO,MAAM,sBAA2C;AAAA;AAAA,EAEtD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA;AAAA,IAEN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,MAAM;AAAA,IACjB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,MAAM;AAAA,IACjB,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,UAAU;AAAA,IACjC,cAAc,WAAW;AAAA,IACzB,UAAU,wBAAC,UAAsB;AAC/B,cAAQ,OAAO;AAAA,QACb,KAAK,WAAW;AACd,iBAAO;QACT,KAAK,WAAW;AACP,iBAAA;AAAA,YACL,CAAC,aAAa,GAAG;AAAA,UAAA;AAAA,QAErB,KAAK,WAAW;AACP,iBAAA;AAAA,YACL,CAAC,qBAAqB,GAAG;AAAA,UAAA;AAAA,MAE/B;AAAA,IACF,GAbU;AAAA,EAcZ;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,SAAS;AAAA,MACP,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,IACzB;AAAA,IACA,cAAc,uBAAuB;AAAA,IACrC,SAAS;AAAA,IACT,UAAU,wBAAC,UAAkC;AAC3C,cAAQ,OAAO;AAAA,QACb,KAAK,uBAAuB;AAC1B,iBAAO;QACT,KAAK,uBAAuB;AACnB,iBAAA;AAAA,YACL,CAAC,YAAY,GAAG;AAAA,UAAA;AAAA,QAEpB,KAAK,uBAAuB;AACnB,iBAAA;AAAA,YACL,CAAC,YAAY,GAAG;AAAA,UAAA;AAAA,QAEpB;AACE,iBAAO;MACX;AAAA,IACF,GAfU;AAAA,EAgBZ;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,SAAS;AAAA,MACP,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,IACzB;AAAA,IACA,cAAc,uBAAuB;AAAA,IACrC,SAAS;AAAA,IACT,UAAU,wBAAC,UAAkC;AAC3C,cAAQ,OAAO;AAAA,QACb,KAAK,uBAAuB;AAC1B,iBAAO;QACT;AACS,iBAAA;AAAA,YACL,CAAC,GAAG,MAAM,YAAY,CAAC,OAAO,GAAG;AAAA,UAAA;AAAA,MAEvC;AAAA,IACF,GATU;AAAA,EAUZ;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,SAAS;AAAA,MACP,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,IACzB;AAAA,IACA,cAAc,uBAAuB;AAAA,IACrC,SAAS;AAAA,IACT,UAAU,wBAAC,UAAkC;AAC3C,cAAQ,OAAO;AAAA,QACb,KAAK,uBAAuB;AAC1B,iBAAO;QACT;AACS,iBAAA;AAAA,YACL,CAAC,GAAG,MAAM,YAAY,CAAC,MAAM,GAAG;AAAA,UAAA;AAAA,MAEtC;AAAA,IACF,GATU;AAAA,EAUZ;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,SAAS;AAAA,MACP,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,IACzB;AAAA,IACA,cAAc,uBAAuB;AAAA,IACrC,SAAS;AAAA,IACT,UAAU,wBAAC,UAAkC;AAC3C,cAAQ,OAAO;AAAA,QACb,KAAK,uBAAuB;AAC1B,iBAAO;QACT;AACS,iBAAA;AAAA,YACL,CAAC,GAAG,MAAM,YAAY,CAAC,WAAW,GAAG;AAAA,UAAA;AAAA,MAE3C;AAAA,IACF,GATU;AAAA,EAUZ;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,mBAAmB;AAAA,IAC1C,cAAc,oBAAoB;AAAA,EACpC;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,SAAS;AAAA,IACpB,MAAM;AAAA,IACN,cAAc;AAAA,IACd,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,OAAO;AAAA,IAClB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,OAAO;AAAA,IAClB,MAAM;AAAA,IACN,cAAc;AAAA,IACd,SAAS;AAAA,EACX;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,oBAAoB;AAAA,IAC3C,cAAc,qBAAqB;AAAA,IACnC,UAAU,wBAAC,UAAgC;AACzC,cAAQ,OAAO;AAAA,QACb,KAAK,qBAAqB;AACxB,iBAAO;QACT;AACS,iBAAA;AAAA,YACL,CAAC,OAAO,MAAM,YAAY,CAAC,kBAAkB,GAAG;AAAA,UAAA;AAAA,MAEtD;AAAA,IACF,GATU;AAAA,EAUZ;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,cAAc;AAAA,IACrC,cAAc,eAAe;AAAA,IAC7B,UAAU,wBAAC,UAA0B;AACnC,cAAQ,OAAO;AAAA,QACb,KAAK,eAAe;AAClB,iBAAO;QACT;AACS,iBAAA;AAAA,YACL,CAAC,KAAK,GAAG;AAAA,UAAA;AAAA,MAEf;AAAA,IACF,GATU;AAAA,EAUZ;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,cAAc;AAAA,IACd,SACE;AAAA,EACJ;AAAA;AAAA,EAGA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,YAAY;AAAA,IACnC,cAAc,aAAa;AAAA,EAC7B;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SACE;AAAA,IACF,UAAU,CAAC,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,IACd,SAAS;AAAA,EACX;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS,OAAO,OAAO,QAAQ;AAAA,IAC/B,cAAc,SAAS;AAAA,IACvB,UAAU,wBAAC,UAAoB;AACtB,aAAA;AAAA,QACL,SAAS;AAAA,MAAA;AAAA,IAEb,GAJU;AAAA,EAKZ;AAAA;AAAA,EAEA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,aAAa;AAAA,IACxB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU,CAAC,aAAa;AAAA,IACxB,MAAM;AAAA,IACN,cAAc;AAAA,EAChB;AACF;AC5bO,SAAS,wBAAwB;AACtC,QAAM,kBAAkB;AACxB,QAAM,qBAAqB;AAE3B,MAAI,kBAAkB;AACtB,MAAI,gBAAgB;AAChB,MAAA,iBAAiB,gBAAgB,MAAM;AACrC,QAAA,mBAAmB,SAAS,UAAU;AACxC,UAAI,eAAe;AACC,0BAAA;AAAA,MAAA,OACb;AACa,0BAAA;AAElB,aAAK,IAAI,YAAY,GAAG,mBAAmB,UAAU;AACrD;AAAA,MACF;AAAA,IACF;AAAA,EAAA,CACD;AAEe,kBAAA;AAAA,IACd,YAAY;AACV,sBAAgB,gBAAgB;AAChC,UAAI,CAAC,iBAAiB,CAAC,IAAI,oBAAoB;AAC7C,YACE,mBAAmB,SAAS,aAC3B,mBAAmB,SAAS,YAAY,iBACzC;AACkB,4BAAA;AAClB,gBAAM,IAAI,YAAY,GAAG,mBAAmB,UAAU;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AAAA,IACA,EAAE,UAAU,KAAK;AAAA,EAAA;AAErB;AAlCgB;;;;;;;;;;;;;;;;;ACuDM;AACH;AACA;AAEb,UAAA,EAAE,GAAAlB,OAAM;AACd,UAAM,QAAQ;AACd,UAAM,eAAe;AACrB,UAAM,iBAAiB;AACvB,UAAM,oBAAoB;AAC1B,UAAM,aAAa;AAEnB;AAAA,MACE,MAAM,kBAAkB;AAAA,MACxB,CAAC,aAAa;AACZ,cAAM,mBAAmB;AACzB,YAAI,SAAS,aAAa;AACf,mBAAA,KAAK,UAAU,OAAO,gBAAgB;AAAA,QAAA,OAC1C;AACI,mBAAA,KAAK,UAAU,IAAI,gBAAgB;AAAA,QAC9C;AAEA,YAAI,cAAc;AAChB,sBAAA,EAAc,YAAY;AAAA,YACxB,OAAO;AAAA,YACP,aAAa,SAAS,OAAO,WAAW,YAAY;AAAA,UAAA,CACrD;AAAA,QACH;AAAA,MACF;AAAA,MACA,EAAE,WAAW,KAAK;AAAA,IAAA;AAGpB,QAAI,cAAc;AAChB;AAAA,QACE,MAAM,WAAW;AAAA,QACjB,CAAC,UAAU,aAAa;AAEtB,gBAAM,oBAAoB,IAAI;AAAA,YAC5B,SAAS,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,IAAI,CAAC,SAAS,KAAK,QAAQ;AAAA,UAAA;AAGpE,mBAAA;AAAA,YACC,CAAC,SAAS,kBAAkB,IAAI,KAAK,QAAQ,KAAK,KAAK;AAAA,UAAA,EAExD,QAAQ,CAAC,SAAS;AACjB,wBAAA,EAAc,OAAO;AAAA,cACnB,aAAa,KAAK,cAAc,YAAa,CAAA;AAAA,cAC7C;AAAA,YAAA;AAEU,0BAAE,OAAO,WAAW,aAAa;AAAA,cAC3C,QAAQ,KAAK,cAAc,YAAY;AAAA,YAAA,CACxC;AAAA,UAAA,CACF;AAAA,QACL;AAAA,QACA,EAAE,MAAM,KAAK;AAAA,MAAA;AAAA,IAEjB;AAEA,gBAAY,MAAM;AACV,YAAA,WAAW,aAAa,IAAI,+BAA+B;AACjE,eAAS,gBAAgB,MAAM;AAAA,QAC7B;AAAA,QACA,GAAG,QAAQ;AAAA,MAAA;AAAA,IACb,CACD;AAED,gBAAY,MAAM;AACV,YAAA,UAAU,aAAa,IAAI,gCAAgC;AACjE,eAAS,gBAAgB,MAAM;AAAA,QAC7B;AAAA,QACA,GAAG,OAAO;AAAA,MAAA;AAAA,IACZ,CACD;AAED,gBAAY,MAAM;AACV,YAAA,SAAS,aAAa,IAAI,cAAc;AAC9C,UAAI,QAAQ;AACL,aAAA,OAAO,OAAO,QAAQ;AAAA,MAC7B;AAAA,IAAA,CACD;AAEK,UAAA,aAAa,SAAS,MAAM;AACzB,aAAA,aAAa,IAAI,kBAAkB;AAAA,IAAA,CAC3C;AACD,gBAAY,MAAM;AACZ,UAAA,WAAW,UAAU,YAAY;AACnC,YAAI,GAAG,cAAc,MAAM,YAAY,WAAW,OAAO;AACzD,YAAI,GAAG;MAAoB,OACtB;AACL,YAAI,GAAG,cAAc,MAAM,YAAY,WAAW,MAAM;AAAA,MAC1D;AAAA,IAAA,CACD;AAED,gBAAY,MAAM;AACL,iBAAA,kBAAkB,aAAa,IAAI,6BAA6B;AAAA,IAAA,CAC5E;AAED,UAAM,OAAO,6BAAM;AACjB,YAAM,eAAe;AACL,sBAAA,EAAE,iBAAiB,YAAY;AAC/C,uBAAA,EAAmB;AACnB,2BAAA,EAAuB;AACvB,yBAAA,EAAqB;AACrB,0BAAA,EAAsB;AACtB,UAAI,mBAAmB;IAAkB,GAP9B;AAUb,UAAM,6BAA6B;AAC7B,UAAA,WAAW,8BAAO,MAA0C;AAChE,iCAA2B,OAAO,CAAC;AACnC,YAAM,WAAW;IAAO,GAFT;AAKjB,UAAM,sBAA2C;AAAA,MAC/C,UAAU;AAAA,MACV,SAASA,GAAE,gBAAgB;AAAA,IAAA;AAG7B,UAAM,iBAAiB,6BAAM;AAC3B,UAAI,CAAC,aAAa,IAAI,sCAAsC,GAAG;AAC7D,cAAM,OAAO,mBAAmB;AAChC,cAAM,IAAI,mBAAmB;AAAA,MAC/B;AAAA,IAAA,GAJqB;AAOvB,UAAM,gBAAgB,6BAAM;AAC1B,UAAI,CAAC,aAAa,IAAI,sCAAsC,GAAG;AAC7D,cAAM,OAAO,mBAAmB;AAChC,cAAM,IAAI;AAAA,UACR,UAAU;AAAA,UACV,SAASA,GAAE,eAAe;AAAA,UAC1B,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,IAAA,GARoB;AAWtB,cAAU,MAAM;AACV,UAAA,iBAAiB,UAAU,QAAQ;AACnC,UAAA,iBAAiB,gBAAgB,cAAc;AAC/C,UAAA,iBAAiB,eAAe,aAAa;AACjD,qBAAe,oBAAoB;AAE/B,UAAA;AACG;eACE,GAAG;AACF,gBAAA,MAAM,mCAAmC,CAAC;AAAA,MACpD;AAAA,IAAA,CACD;AAED,oBAAgB,MAAM;AAChB,UAAA,oBAAoB,UAAU,QAAQ;AACtC,UAAA,oBAAoB,gBAAgB,cAAc;AAClD,UAAA,oBAAoB,eAAe,aAAa;AACpD,qBAAe,sBAAsB;AAAA,IAAA,CACtC;AAED,qBAAiB,QAAQ,WAAW,qBAAqB,EAAE,cAAc;AAEzE,UAAM,EAAE,uBAAuB,2BAA2B,IAAI,iBAAiB;AAC/E,UAAM,eAAe,6BAAM;AACzB;AAAA,QACE,MAAM;AAGkB,gCAAA,qBAAA,EAAuB,uBAAuB;AAG9C,gCAAA,uBAAuB,gBAAgB;AAAA,YAC3D;AAAA,YACA,aAAa,IAAI,iCAAiC;AAAA,UAAA;AAIpD,eAAK,2BAA2B,gBAAgB,gBAAgB,EAAE;AAG7D,eAAA;AAAA,YACH,sBAAwB,EAAA;AAAA,UAAA;AAMV,4BAAE,kBAAkB,WAAW,EAAE;AAAA,QACnD;AAAA,QACA,EAAE,SAAS,IAAK;AAAA,MAAA;AAAA,IAClB,GA3BmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}