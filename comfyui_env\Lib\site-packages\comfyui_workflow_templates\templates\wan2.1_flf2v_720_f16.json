{"last_node_id": 90, "last_link_id": 189, "nodes": [{"id": 39, "type": "VAELoader", "pos": [30, 330], "size": [350, 60], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "slot_index": 0, "links": [76, 175]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "VAELoader", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true", "hash": "2fc39d31359a4b0a64f55876d8ff7fa8d780956ae2cb13463b0223e15148976b", "hash_type": "SHA256", "directory": "vae"}]}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 87, "type": "CLIPVisionEncode", "pos": [560, 570], "size": [253.60000610351562, 78], "flags": {"collapsed": true}, "order": 7, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 182}, {"name": "image", "type": "IMAGE", "link": 180}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [181]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 28, "type": "SaveAnimatedWEBP", "pos": [800, 490], "size": [530, 480], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 56}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29"}, "widgets_values": ["ComfyUI", 16, false, 90, "default"]}, {"id": 49, "type": "CLIPVisionLoader", "pos": [30, 510], "size": [340, 60], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "slot_index": 0, "links": [94, 182]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPVisionLoader", "models": [{"name": "clip_vision_h.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/clip_vision/clip_vision_h.safetensors?download=true", "hash": "64a7ef761bfccbadbaa3da77366aac4185a6c58fa5de5f589b42a65bcc21f161", "hash_type": "SHA256", "directory": "clip_vision"}]}, "widgets_values": ["clip_vision_h.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 51, "type": "CLIPVisionEncode", "pos": [530, 500], "size": [253.60000610351562, 78], "flags": {"collapsed": true}, "order": 8, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 94}, {"name": "image", "type": "IMAGE", "link": 109}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [178]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 79, "type": "ModelSamplingSD3", "pos": [810, 70], "size": [270, 60], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 162}], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [185]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "ModelSamplingSD3"}, "widgets_values": [8]}, {"id": 8, "type": "VAEDecode", "pos": [1120, 390], "size": [210, 266], "flags": {"collapsed": true}, "order": 14, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 188}, {"name": "vae", "type": "VAE", "link": 76}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [56]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 83, "type": "WanFirstLastFrameToVideo", "pos": [810, 170], "size": [270.3999938964844, 250], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 176}, {"name": "negative", "type": "CONDITIONING", "link": 177}, {"name": "vae", "type": "VAE", "link": 175}, {"name": "clip_vision_start_image", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 178}, {"name": "clip_vision_end_image", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 181}, {"name": "start_image", "shape": 7, "type": "IMAGE", "link": 183}, {"name": "end_image", "shape": 7, "type": "IMAGE", "link": 184}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [186]}, {"name": "negative", "type": "CONDITIONING", "links": [187]}, {"name": "latent", "type": "LATENT", "links": [189]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "WanFirstLastFrameToVideo"}, "widgets_values": [720, 1280, 33, 1]}, {"id": 89, "type": "<PERSON>downNote", "pos": [-460, 40], "size": [450, 440], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tutorial here](https://docs.comfy.org/tutorials/video/wan/wan-flf) | [教程](https://docs.comfy.org/zh-CN/tutorials/video/wan/wan-flf)\n\nYou can find the following files in the [Wan_2.1_ComfyUI_repackaged](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/tree/main/split_files).\n\nDownload the following files, and put them in right directory\n\n**Diffusion models**\n\n- [wan2.1_flf2v_720p_14B_fp16.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_flf2v_720p_14B_fp16.safetensors?download=true)\n\n**Text encoders**\n- [umt5_xxl_fp8_e4m3fn_scaled.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true)\n\n**VAE**\n- [wan_2.1_vae.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true)\n\n**CLIP Vision**\n- [clip_vision_h.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/clip_vision/clip_vision_h.safetensors?download=true)\n\nFile Storage Location\n\n```\nComfyUI/\n├── models/\n│   ├── diffusion_models/\n│   │   └─── wan2.1_flf2v_720p_14B_fp16.safetensors  \n│   ├── text_encoders/\n│   │   └─── umt5_xxl_fp8_e4m3fn_scaled.safetensors  \n│   ├── vae/\n│   │   └──  wan_2.1_vae.safetensors\n│   └── clip_vision/\n│       └──  clip_vision_h.safetensors   \n```\n"], "color": "#432", "bgcolor": "#653"}, {"id": 90, "type": "K<PERSON><PERSON><PERSON>", "pos": [1120, 80], "size": [315, 262], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 185}, {"name": "positive", "type": "CONDITIONING", "link": 186}, {"name": "negative", "type": "CONDITIONING", "link": 187}, {"name": "latent_image", "type": "LATENT", "link": 189}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [188]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [1012535162155917, "randomize", 20, 3, "uni_pc", "simple", 1]}, {"id": 7, "type": "CLIPTextEncode", "pos": [420, 260], "size": [340, 140], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 75}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [177]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走,过曝，"], "color": "#323", "bgcolor": "#535"}, {"id": 72, "type": "LoadImage", "pos": [410, 630], "size": [320, 320], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [180, 184]}, {"name": "MASK", "type": "MASK", "links": null}], "title": "End_image", "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "LoadImage"}, "widgets_values": ["end_image.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 52, "type": "LoadImage", "pos": [30, 624], "size": [340, 326], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [109, 183]}, {"name": "MASK", "type": "MASK", "slot_index": 1, "links": null}], "title": "Start_image", "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "LoadImage"}, "widgets_values": ["start_image.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 37, "type": "UNETLoader", "pos": [30, 70], "size": [346.7470703125, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [162]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "UNETLoader", "models": [{"name": "wan2.1_flf2v_720p_14B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_flf2v_720p_14B_fp16.safetensors?download=true", "hash": "bf4ac25667d00f53f49df02c5771f5aa7801c1dcb9b3ccade1407687c426d030", "hash_type": "SHA256", "directory": "diffusion_models"}]}, "widgets_values": ["wan2.1_flf2v_720p_14B_fp16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 38, "type": "CLIPLoader", "pos": [30, 190], "size": [350, 106], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [74, 75]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPLoader", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true", "hash": "c3355d30191f1f066b26d93fba017ae9809dce6c627dda5f6a66eaa651204f68", "hash_type": "SHA256", "directory": "text_encoders"}]}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "CLIPTextEncode", "pos": [420, 70], "size": [330, 140], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 74}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [176]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["glass flower blossom"], "color": "#232", "bgcolor": "#353"}], "links": [[56, 8, 0, 28, 0, "IMAGE"], [74, 38, 0, 6, 0, "CLIP"], [75, 38, 0, 7, 0, "CLIP"], [76, 39, 0, 8, 1, "VAE"], [94, 49, 0, 51, 0, "CLIP_VISION"], [109, 52, 0, 51, 1, "IMAGE"], [162, 37, 0, 79, 0, "MODEL"], [175, 39, 0, 83, 2, "VAE"], [176, 6, 0, 83, 0, "CONDITIONING"], [177, 7, 0, 83, 1, "CONDITIONING"], [178, 51, 0, 83, 3, "CLIP_VISION_OUTPUT"], [180, 72, 0, 87, 1, "IMAGE"], [181, 87, 0, 83, 4, "CLIP_VISION_OUTPUT"], [182, 49, 0, 87, 0, "CLIP_VISION"], [183, 52, 0, 83, 5, "IMAGE"], [184, 72, 0, 83, 6, "IMAGE"], [185, 79, 0, 90, 0, "MODEL"], [186, 83, 0, 90, 1, "CONDITIONING"], [187, 83, 1, 90, 2, "CONDITIONING"], [188, 90, 0, 8, 0, "LATENT"], [189, 83, 2, 90, 3, "LATENT"]], "groups": [{"id": 1, "title": "Load Models Here", "bounding": [20, 0, 370, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Start & End frames", "bounding": [20, 430, 750, 540], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Prompt", "bounding": [410, 0, 360, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "Sampling & Video Generation", "bounding": [790, 0, 700, 440], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.876922695000016, "offset": [535.6405655273688, 65.45675761409491]}, "frontendVersion": "1.16.8", "node_versions": {"comfy-core": "0.3.29"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}