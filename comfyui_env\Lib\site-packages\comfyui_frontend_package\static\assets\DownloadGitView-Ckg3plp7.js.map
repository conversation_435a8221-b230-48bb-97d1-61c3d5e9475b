{"version": 3, "file": "DownloadGitView-Ckg3plp7.js", "sources": ["../../src/views/DownloadGitView.vue"], "sourcesContent": ["<template>\n  <BaseViewTemplate>\n    <div\n      class=\"max-w-screen-sm flex flex-col gap-8 p-8 bg-[url('/assets/images/Git-Logo-White.svg')] bg-no-repeat bg-right-top bg-origin-padding\"\n    >\n      <!-- Header -->\n      <h1 class=\"mt-24 text-4xl font-bold text-red-500\">\n        {{ $t('downloadGit.title') }}\n      </h1>\n\n      <!-- Message -->\n      <div class=\"space-y-4\">\n        <p class=\"text-xl\">\n          {{ $t('downloadGit.message') }}\n        </p>\n        <p class=\"text-xl\">\n          {{ $t('downloadGit.instructions') }}\n        </p>\n        <p class=\"text-m\">\n          {{ $t('downloadGit.warning') }}\n        </p>\n      </div>\n\n      <!-- Actions -->\n      <div class=\"flex gap-4 flex-row-reverse\">\n        <Button\n          :label=\"$t('downloadGit.gitWebsite')\"\n          icon=\"pi pi-external-link\"\n          icon-pos=\"right\"\n          severity=\"primary\"\n          @click=\"openGitDownloads\"\n        />\n        <Button\n          :label=\"$t('downloadGit.skip')\"\n          icon=\"pi pi-exclamation-triangle\"\n          severity=\"secondary\"\n          @click=\"skipGit\"\n        />\n      </div>\n    </div>\n  </BaseViewTemplate>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport { useRouter } from 'vue-router'\n\nimport BaseViewTemplate from '@/views/templates/BaseViewTemplate.vue'\n\nconst openGitDownloads = () => {\n  window.open('https://git-scm.com/downloads/', '_blank')\n}\n\nconst skipGit = async () => {\n  console.warn('pushing')\n  const router = useRouter()\n  await router.push('install')\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAiDA,UAAM,mBAAmB,6BAAM;AACtB,aAAA,KAAK,kCAAkC,QAAQ;AAAA,IAAA,GAD/B;AAIzB,UAAM,UAAU,mCAAY;AAC1B,cAAQ,KAAK,SAAS;AACtB,YAAM,SAAS;AACT,YAAA,OAAO,KAAK,SAAS;AAAA,IAAA,GAHb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}