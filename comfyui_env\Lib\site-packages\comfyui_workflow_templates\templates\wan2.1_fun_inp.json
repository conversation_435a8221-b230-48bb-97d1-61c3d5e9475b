{"id": "e7533930-2792-43a9-b4b5-ded4617d8a43", "revision": 0, "last_node_id": 78, "last_link_id": 157, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [1170, 530], "size": [280, 262], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 135}, {"name": "positive", "type": "CONDITIONING", "link": 149}, {"name": "negative", "type": "CONDITIONING", "link": 150}, {"name": "latent_image", "type": "LATENT", "link": 151}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [35]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [622093119444720, "randomize", 20, 6, "uni_pc", "simple", 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [420, 70], "size": [420, 140], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 74}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [152]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Fashion photography, a inflated man wearing Balenciaga clothes, fashion magazine, Balenciaga style, studio, photographer by <PERSON>, On-Camera Flash"], "color": "#232", "bgcolor": "#353"}, {"id": 7, "type": "CLIPTextEncode", "pos": [420, 260], "size": [420, 130], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 75}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [153]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走"], "color": "#323", "bgcolor": "#535"}, {"id": 8, "type": "VAEDecode", "pos": [1310, 850], "size": [210, 266], "flags": {"collapsed": true}, "order": 16, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 35}, {"name": "vae", "type": "VAE", "link": 76}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [56]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 28, "type": "SaveAnimatedWEBP", "pos": [1500, 40], "size": [470, 570], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 56}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI", 16, false, 90, "default"]}, {"id": 37, "type": "UNETLoader", "pos": [30, 70], "size": [346.7470703125, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [146]}], "properties": {"Node name for S&R": "UNETLoader", "models": [{"name": "wan2.1_fun_inp_1.3B_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_fun_inp_1.3B_bf16.safetensors?download=true", "directory": "diffusion_models"}]}, "widgets_values": ["wan2.1_fun_inp_1.3B_bf16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 38, "type": "CLIPLoader", "pos": [30, 190], "size": [350, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [74, 75]}], "properties": {"Node name for S&R": "CLIPLoader", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true", "directory": "text_encoders"}]}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 39, "type": "VAELoader", "pos": [30, 330], "size": [350, 60], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "slot_index": 0, "links": [76, 154]}], "properties": {"Node name for S&R": "VAELoader", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true", "directory": "vae"}]}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 49, "type": "CLIPVisionLoader", "pos": [30, 500], "size": [340, 60], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "slot_index": 0, "links": [94]}], "properties": {"Node name for S&R": "CLIPVisionLoader", "models": [{"name": "clip_vision_h.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/clip_vision/clip_vision_h.safetensors?download=true", "directory": "clip_vision"}]}, "widgets_values": ["clip_vision_h.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 51, "type": "CLIPVisionEncode", "pos": [440, 500], "size": [253.60000610351562, 78], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 94}, {"name": "image", "type": "IMAGE", "link": 109}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [155]}], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 52, "type": "LoadImage", "pos": [30, 600], "size": [340, 326], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [109, 156]}, {"name": "MASK", "type": "MASK", "slot_index": 1, "links": null}], "title": "Start_image", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["fashion_photography_start.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 65, "type": "SkipLayerGuidanceDiT", "pos": [890, 80], "size": [230, 180], "flags": {"collapsed": false}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 146}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [132]}], "properties": {"Node name for S&R": "SkipLayerGuidanceDiT", "cnr_id": "comfy-core", "ver": "0.3.27", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["9,10", "9,10", 3, 0.01, 0.8000000000000002, 0]}, {"id": 66, "type": "CFGZeroStar", "pos": [890, 310], "size": [230, 30], "flags": {"collapsed": false}, "order": 14, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 131}], "outputs": [{"name": "patched_model", "type": "MODEL", "links": [135]}], "properties": {"Node name for S&R": "CFGZeroStar", "cnr_id": "comfy-core", "ver": "0.3.27", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": []}, {"id": 67, "type": "ModelSamplingSD3", "pos": [1200, 80], "size": [240, 60], "flags": {"collapsed": false}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 132}], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [133]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "cnr_id": "comfy-core", "ver": "0.3.26", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [5.000000000000001]}, {"id": 68, "type": "UNetTemporalAttentionMultiply", "pos": [1200, 190], "size": [243.60000610351562, 150], "flags": {"collapsed": false}, "order": 13, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 133}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [131]}], "properties": {"Node name for S&R": "UNetTemporalAttentionMultiply", "cnr_id": "comfy-core", "ver": "0.3.27", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [1, 1, 1.2, 1.3]}, {"id": 72, "type": "LoadImage", "pos": [440, 610], "size": [315, 314], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [157]}, {"name": "MASK", "type": "MASK", "links": null}], "title": "End_image", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["fashion_photography_end.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 76, "type": "WanFunInpaintToVideo", "pos": [900, 550], "size": [240, 230], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 152}, {"name": "negative", "type": "CONDITIONING", "link": 153}, {"name": "vae", "type": "VAE", "link": 154}, {"name": "clip_vision_output", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 155}, {"name": "start_image", "shape": 7, "type": "IMAGE", "link": 156}, {"name": "end_image", "shape": 7, "type": "IMAGE", "link": 157}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [149]}, {"name": "negative", "type": "CONDITIONING", "links": [150]}, {"name": "latent", "type": "LATENT", "links": [151]}], "properties": {"Node name for S&R": "WanFunInpaintToVideo"}, "widgets_values": [480, 768, 81, 1]}, {"id": 78, "type": "<PERSON>downNote", "pos": [24, 984], "size": [336, 96], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [ComfyUI Wan2.1 Fun InP Video - docs.comfy](https://docs.comfy.org/tutorials/video/wan/fun-inp)"], "color": "#432", "bgcolor": "#653"}], "links": [[35, 3, 0, 8, 0, "LATENT"], [56, 8, 0, 28, 0, "IMAGE"], [74, 38, 0, 6, 0, "CLIP"], [75, 38, 0, 7, 0, "CLIP"], [76, 39, 0, 8, 1, "VAE"], [94, 49, 0, 51, 0, "CLIP_VISION"], [109, 52, 0, 51, 1, "IMAGE"], [131, 68, 0, 66, 0, "MODEL"], [132, 65, 0, 67, 0, "MODEL"], [133, 67, 0, 68, 0, "MODEL"], [135, 66, 0, 3, 0, "MODEL"], [146, 37, 0, 65, 0, "MODEL"], [149, 76, 0, 3, 1, "CONDITIONING"], [150, 76, 1, 3, 2, "CONDITIONING"], [151, 76, 2, 3, 3, "LATENT"], [152, 6, 0, 76, 0, "CONDITIONING"], [153, 7, 0, 76, 1, "CONDITIONING"], [154, 39, 0, 76, 2, "VAE"], [155, 51, 0, 76, 3, "CLIP_VISION_OUTPUT"], [156, 52, 0, 76, 4, "IMAGE"], [157, 72, 0, 76, 5, "IMAGE"]], "groups": [{"id": 1, "title": "Load Models", "bounding": [24, 0, 370, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Start & End Images", "bounding": [24, 432, 830, 510], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Attention Booster", "bounding": [872, 0, 600, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Prompt", "bounding": [408, 0, 440, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "Sampling & Decode", "bounding": [872, 432, 600, 510], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"frontendVersion": "1.16.7"}, "version": 0.4}