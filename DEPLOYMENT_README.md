# ComfyUI 本地部署环境使用指南

## 环境信息

- **Python 版本**: 3.11.9
- **PyTorch 版本**: 2.6.0+cu124 (支持 CUDA 12.4)
- **GPU**: NVIDIA GeForce RTX 4070 Laptop GPU (8GB VRAM)
- **ComfyUI 版本**: 0.3.39
- **虚拟环境**: comfyui_env

## 快速启动

### 方法 1: 使用批处理文件（推荐）
双击 `start_comfyui.bat` 文件即可启动 ComfyUI。

### 方法 2: 手动启动
```bash
# 激活虚拟环境
.\comfyui_env\Scripts\Activate.ps1

# 启动 ComfyUI
python main.py --auto-launch
```

## 访问地址

启动成功后，在浏览器中访问：
- **本地访问**: http://127.0.0.1:8188
- **局域网访问**: http://[你的IP地址]:8188

## 常用启动参数

```bash
# 基本启动
python main.py

# 自动打开浏览器
python main.py --auto-launch

# 监听所有网络接口（允许局域网访问）
python main.py --listen 0.0.0.0

# 指定端口
python main.py --port 8189

# 低显存模式（如果遇到显存不足）
python main.py --lowvram

# 极低显存模式
python main.py --novram

# CPU 模式（如果 GPU 有问题）
python main.py --cpu
```

## 目录结构

```
ComfyUI-master/
├── models/                 # 模型文件目录
│   ├── checkpoints/       # 主模型文件 (.ckpt, .safetensors)
│   ├── vae/              # VAE 模型
│   ├── loras/            # LoRA 模型
│   ├── controlnet/       # ControlNet 模型
│   ├── clip/             # CLIP 模型
│   ├── upscale_models/   # 超分辨率模型
│   └── embeddings/       # 文本嵌入
├── input/                 # 输入图片目录
├── output/               # 输出图片目录
├── custom_nodes/         # 自定义节点
├── comfyui_env/          # Python 虚拟环境
└── start_comfyui.bat     # 启动脚本
```

## 模型下载

### 推荐的基础模型
1. **Stable Diffusion 1.5**: 
   - 下载地址: https://huggingface.co/runwayml/stable-diffusion-v1-5
   - 放置位置: `models/checkpoints/`

2. **Stable Diffusion XL**:
   - 下载地址: https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0
   - 放置位置: `models/checkpoints/`

### 模型下载工具
- 使用 `git lfs` 下载 Hugging Face 模型
- 使用 ComfyUI Manager 插件管理模型

## 性能优化建议

### 针对 RTX 4070 (8GB VRAM) 的优化
1. **正常使用**: 默认设置即可
2. **显存不足时**: 添加 `--lowvram` 参数
3. **极端情况**: 使用 `--novram` 参数

### 启动参数组合建议
```bash
# 平衡性能和稳定性
python main.py --auto-launch --preview-method auto

# 最大性能（如果显存充足）
python main.py --auto-launch --highvram --fast

# 节省显存
python main.py --auto-launch --lowvram --cpu-vae
```

## 常见问题解决

### 1. 显存不足 (CUDA out of memory)
```bash
# 解决方案 1: 低显存模式
python main.py --lowvram

# 解决方案 2: 极低显存模式
python main.py --novram

# 解决方案 3: VAE 使用 CPU
python main.py --cpu-vae
```

### 2. 启动缓慢
```bash
# 禁用某些功能加速启动
python main.py --disable-auto-launch --dont-print-server
```

### 3. 网络访问问题
```bash
# 允许局域网访问
python main.py --listen 0.0.0.0 --port 8188
```

## 自定义节点安装

1. 将自定义节点放入 `custom_nodes/` 目录
2. 重启 ComfyUI
3. 推荐使用 ComfyUI Manager 管理插件

## 更新 ComfyUI

```bash
# 激活虚拟环境
.\comfyui_env\Scripts\Activate.ps1

# 更新依赖
pip install -r requirements.txt --upgrade

# 如果有新的依赖，重新安装
pip install -r requirements.txt
```

## 技术支持

- **官方文档**: https://docs.comfy.org/
- **GitHub**: https://github.com/comfyanonymous/ComfyUI
- **社区**: ComfyUI Discord 服务器

## 注意事项

1. 首次启动可能需要下载一些模型，请耐心等待
2. 确保有足够的磁盘空间存储模型文件
3. 定期备份重要的工作流程和自定义设置
4. 建议定期更新 ComfyUI 以获得最新功能和修复
