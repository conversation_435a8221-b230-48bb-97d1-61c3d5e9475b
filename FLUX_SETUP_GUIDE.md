# Flux 工作流设置指南

## 🚨 当前问题

您遇到的错误：`vae_name: 'ae.sft' not in []`

**原因**：
1. Flux 工作流需要专用的 VAE 文件：`ae.safetensors`
2. 您的 `models/vae/` 目录中缺少这个文件

## 🔧 解决方案

### 方案 1: 下载 Flux VAE（推荐，完整功能）

#### 自动下载（推荐）
```bash
# 激活虚拟环境
.\comfyui_env\Scripts\Activate.ps1

# 安装 requests（如果没有）
pip install requests

# 运行下载脚本
python download_flux_vae.py
```

#### 手动下载
1. **下载地址**: https://huggingface.co/black-forest-labs/FLUX.1-dev/resolve/main/ae.safetensors
2. **文件大小**: 约 335MB
3. **保存位置**: `models/vae/ae.safetensors`

### 方案 2: 使用简化工作流（立即可用）

我已经创建了一个使用 SD1.5 VAE 的简化版本：

**文件**: `workflows/flux_simple.json`

**特点**:
- ✅ 使用您现有的 SD1.5 VAE
- ✅ 无需额外下载
- ✅ 立即可用
- ⚠️ 可能质量略低于原生 Flux VAE

## 🚀 立即使用（推荐）

### 使用简化 Flux 工作流

1. **导入工作流**:
   ```
   拖拽 workflows/flux_simple.json 到 ComfyUI 界面
   ```

2. **检查设置**:
   - UNet: `flux1-dev-fp8.safetensors` ✅
   - CLIP: `clip_l.safetensors` + `t5xxl_fp8_e4m3fn.safetensors` ✅
   - VAE: 使用 SD1.5 的 VAE ✅

3. **开始生成**:
   点击 "Queue Prompt"

## 📊 工作流对比

| 工作流 | VAE 来源 | 下载需求 | 质量 | 可用性 |
|--------|----------|----------|------|--------|
| `flux_basic.json` | Flux 专用 VAE | 需要下载 335MB | 最高 | 需要下载 |
| `flux_simple.json` | SD1.5 VAE | 无需下载 | 高 | ✅ 立即可用 |

## 🎯 推荐使用流程

### 第一步：测试简化版本
```
1. 使用 flux_simple.json 测试 Flux 功能
2. 确认 Flux UNet 和 CLIP 工作正常
3. 生成几张图片验证效果
```

### 第二步：升级到完整版本（可选）
```
1. 下载 Flux VAE (ae.safetensors)
2. 使用 flux_basic.json 获得最佳质量
3. 对比两个版本的效果差异
```

## ⚙️ Flux 工作流参数说明

### 推荐设置
- **分辨率**: 1024x1024 (Flux 最佳)
- **步数**: 20-30
- **采样器**: euler 或 dpmpp_2m
- **调度器**: simple 或 normal

### 显存优化
如果遇到显存不足（RTX 4070 8GB）：

```bash
# 启动时使用低显存模式
python main.py --lowvram

# 或使用 CPU VAE
python main.py --cpu-vae
```

## 🎨 Flux 提示词技巧

### Flux 擅长的内容
- 真实感人物和场景
- 复杂的构图和细节
- 自然光照和材质
- 高分辨率图像

### 推荐提示词格式
```
"a [主题], [风格描述], [质量词汇], [技术参数]"

示例:
"a beautiful woman portrait, natural lighting, photorealistic, highly detailed, 8k resolution, professional photography"
```

## 🔍 故障排除

### 1. UNet 加载失败
**检查**: `models/unet/flux1-dev-fp8.safetensors` 是否存在

### 2. CLIP 加载失败
**检查**: 
- `models/clip/clip_l.safetensors`
- `models/clip/t5xxl_fp8_e4m3fn.safetensors`

### 3. 显存不足
**解决**:
```bash
# 使用 FP8 量化减少显存占用
python main.py --lowvram --fp8_e4m3fn-unet
```

### 4. 生成速度慢
**优化**:
- 减少步数到 15-20
- 使用较小分辨率 (768x768)
- 启用 GPU 优化

## 📁 文件检查清单

确认以下文件存在：

```
models/
├── unet/
│   └── flux1-dev-fp8.safetensors ✅
├── clip/
│   ├── clip_l.safetensors ✅
│   └── t5xxl_fp8_e4m3fn.safetensors ✅
├── checkpoints/
│   └── v1-5-pruned-emaonly.safetensors ✅
└── vae/
    └── ae.safetensors ❓ (可选，用于最佳质量)
```

## 🎉 开始使用

现在您可以：

1. ✅ **立即使用**: `flux_simple.json` (无需下载)
2. 🔄 **可选升级**: 下载 Flux VAE 使用 `flux_basic.json`
3. 🎨 **开始创作**: 体验 Flux 的强大功能

选择适合您的方案开始创作吧！🚀✨
