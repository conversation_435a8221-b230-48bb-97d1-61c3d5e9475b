#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>



#include <ATen/ops/_to_cpu_ops.h>

namespace at {


// aten::_to_cpu(Tensor[] tensors) -> Tensor[]
inline ::std::vector<at::Tensor> _to_cpu(at::TensorList tensors) {
    return at::_ops::_to_cpu::call(tensors);
}

}
