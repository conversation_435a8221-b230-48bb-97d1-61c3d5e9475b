@echo off
echo Starting ComfyUI with GPU Optimization...
echo.

REM 激活虚拟环境
call comfyui_env\Scripts\activate.bat

REM 显示 GPU 信息
echo Virtual environment activated.
echo Checking GPU status...
python -c "import torch; print('GPU Available:', torch.cuda.is_available()); print('GPU Name:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'No GPU')"
echo.

REM 启动 ComfyUI (GPU 优化模式)
echo Starting ComfyUI server with GPU optimization...
echo Using --lowvram for RTX 4070 (8GB) optimization
echo.
echo ComfyUI will be available at: http://127.0.0.1:8188
echo Press Ctrl+C to stop the server.
echo.

python main.py --lowvram --auto-launch

pause
