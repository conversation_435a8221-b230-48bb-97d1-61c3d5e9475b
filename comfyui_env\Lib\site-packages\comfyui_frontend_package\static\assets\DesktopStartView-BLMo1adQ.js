import { defineComponent, openBlock, createBlock, withCtx, createVNode, unref } from "./vendor-vue-B7YUw5vA.js";
import { script$29 as script } from "./vendor-primevue-CBB09Bln.js";
import { _sfc_main as _sfc_main$1 } from "./BaseViewTemplate-C1giSX9N.js";
import "./index-DhLPvT6M.js";
import "./vendor-vue-i18n-CdFxvEOa.js";
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "DesktopStartView",
  setup(__props) {
    return (_ctx, _cache) => {
      return openBlock(), createBlock(_sfc_main$1, { dark: "" }, {
        default: withCtx(() => [
          createVNode(unref(script), { class: "m-8 w-48 h-48" })
        ]),
        _: 1
      });
    };
  }
});
export {
  _sfc_main as default
};
//# sourceMappingURL=DesktopStartView-BLMo1adQ.js.map
