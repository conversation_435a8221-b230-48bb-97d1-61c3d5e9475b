{"last_node_id": 26, "last_link_id": 35, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [552.780029296875, 143.33999633789062], "size": [315, 474], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 34}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7, 20]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [785648477219150, "randomize", 12, 8, "dpmpp_sde", "normal", 1, ""]}, {"id": 5, "type": "EmptyLatentImage", "pos": [142.77999877929688, 571.3400268554688], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [768, 768, 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [81.77999877929688, 142.33999633789062], "size": [422.8500061035156, 164.30999755859375], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 28}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4, 12]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["masterpiece HDR victorian portrait painting of woman, blonde hair, mountain nature, blue sky\n"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [84.77999877929688, 352.3399963378906], "size": [425.2799987792969, 180.61000061035156], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 29}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6, 13]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["bad hands, text, watermark\n"]}, {"id": 8, "type": "VAEDecode", "pos": [1185.5, 412.07000732421875], "size": [210, 46], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 30}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1446, 411], "size": [611.260009765625, 628.5999755859375], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI", ""]}, {"id": 11, "type": "K<PERSON><PERSON><PERSON>", "pos": [2811.9599609375, 176.22000122070312], "size": [315, 474], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 35}, {"name": "positive", "type": "CONDITIONING", "link": 12}, {"name": "negative", "type": "CONDITIONING", "link": 13}, {"name": "latent_image", "type": "LATENT", "link": 18}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [15]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [708627692855439, "randomize", 14, 8, "uni_pc_bh2", "normal", 0.5, ""]}, {"id": 12, "type": "SaveImage", "pos": [3463, 230], "size": [868.010009765625, 936.969970703125], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 17}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI", ""]}, {"id": 13, "type": "VAEDecode", "pos": [3221.219970703125, 232.3800048828125], "size": [210, 46], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 15}, {"name": "vae", "type": "VAE", "link": 33}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [17]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 20, "type": "VAEEncode", "pos": [2459.10009765625, 103.0199966430664], "size": [210, 46], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 26}, {"name": "vae", "type": "VAE", "link": 31}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [18]}], "properties": {"Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 21, "type": "VAEDecode", "pos": [988.1799926757812, 29.559999465942383], "size": [210, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 20}, {"name": "vae", "type": "VAE", "link": 32}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [23]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 22, "type": "ImageUpscaleWithModel", "pos": [1631.06005859375, 3.6600000858306885], "size": [226.8000030517578, 46], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "upscale_model", "type": "UPSCALE_MODEL", "link": 24}, {"name": "image", "type": "IMAGE", "link": 23}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [27]}], "properties": {"Node name for S&R": "ImageUpscaleWithModel"}, "widgets_values": []}, {"id": 23, "type": "UpscaleModelLoader", "pos": [1288.06005859375, -39.34000015258789], "size": [315, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "slot_index": 0, "links": [24]}], "properties": {"Node name for S&R": "UpscaleModelLoader", "models": [{"name": "RealESRGAN_x4plus.pth", "url": "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth", "directory": "upscale_models"}]}, "widgets_values": ["RealESRGAN_x4plus.pth"]}, {"id": 24, "type": "ImageScale", "pos": [1931, 10], "size": [315, 130], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 27}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [26]}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 1536, 1536, "disabled"]}, {"id": 25, "type": "CheckpointLoaderSimple", "pos": [-262, 284], "size": [315, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [34, 35]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [28, 29]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [30, 31, 32, 33]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "v2-1_768-ema-pruned.safetensors", "url": "https://huggingface.co/stabilityai/stable-diffusion-2-1/resolve/main/v2-1_768-ema-pruned.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["v2-1_768-ema-pruned.safetensors"]}, {"id": 26, "type": "<PERSON>downNote", "pos": [-300, 750], "size": [320, 136], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [Non-latent Upscaling - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/2_pass_txt2img/#non-latent-upscaling) — Overview\n> \n> [ComfyUI Image Upscale - docs.comfy.org](https://docs.comfy.org/tutorials/basic/upscale) — Upscaling step-by-step tutorial"], "color": "#432", "bgcolor": "#653"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [9, 8, 0, 9, 0, "IMAGE"], [12, 6, 0, 11, 1, "CONDITIONING"], [13, 7, 0, 11, 2, "CONDITIONING"], [15, 11, 0, 13, 0, "LATENT"], [17, 13, 0, 12, 0, "IMAGE"], [18, 20, 0, 11, 3, "LATENT"], [20, 3, 0, 21, 0, "LATENT"], [23, 21, 0, 22, 1, "IMAGE"], [24, 23, 0, 22, 0, "UPSCALE_MODEL"], [26, 24, 0, 20, 0, "IMAGE"], [27, 22, 0, 24, 0, "IMAGE"], [28, 25, 1, 6, 0, "CLIP"], [29, 25, 1, 7, 0, "CLIP"], [30, 25, 2, 8, 1, "VAE"], [31, 25, 2, 20, 1, "VAE"], [32, 25, 2, 21, 1, "VAE"], [33, 25, 2, 13, 1, "VAE"], [34, 25, 0, 3, 0, "MODEL"], [35, 25, 0, 11, 0, "MODEL"]], "groups": [{"id": 1, "title": "Txt2Img", "bounding": [-296, 0, 1211, 708], "color": "#a1309b", "font_size": 24, "flags": {}}, {"id": 2, "title": "Save Intermediate Image", "bounding": [1168, 328, 516, 196], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Second pass", "bounding": [2776, 88, 379, 429], "color": "#444", "font_size": 24, "flags": {}}, {"id": 4, "title": "Save Final Image", "bounding": [3208, 136, 483, 199], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "ESRGAN upscale with 4x model", "bounding": [1264, -120, 578, 184], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Decode to Pixel space", "bounding": [960, -48, 285, 142], "color": "#A88", "font_size": 24, "flags": {}}, {"id": 7, "title": "Encode back to latent space", "bounding": [2400, 16, 312, 157], "color": "#A88", "font_size": 24, "flags": {}}, {"id": 8, "title": "Downscale image to a more reasonable size", "bounding": [1848, -72, 483, 245], "color": "#8AA", "font_size": 24, "flags": {}}], "config": {}, "extra": {}, "version": 0.4}