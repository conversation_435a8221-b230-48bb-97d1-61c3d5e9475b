{"version": 3, "file": "keybindingService-Cd7rYD8u.js", "sources": ["../../src/constants/coreKeybindings.ts", "../../src/services/keybindingService.ts"], "sourcesContent": ["import type { Keybinding } from '@/schemas/keyBindingSchema'\n\nexport const CORE_KEYBINDINGS: Keybinding[] = [\n  {\n    combo: {\n      ctrl: true,\n      key: 'Enter'\n    },\n    commandId: 'Comfy.QueuePrompt'\n  },\n  {\n    combo: {\n      ctrl: true,\n      shift: true,\n      key: 'Enter'\n    },\n    commandId: 'Comfy.QueuePromptFront'\n  },\n  {\n    combo: {\n      ctrl: true,\n      alt: true,\n      key: 'Enter'\n    },\n    commandId: 'Comfy.Interrupt'\n  },\n  {\n    combo: {\n      key: 'r'\n    },\n    commandId: 'Comfy.RefreshNodeDefinitions'\n  },\n  {\n    combo: {\n      key: 'q'\n    },\n    commandId: 'Workspace.ToggleSidebarTab.queue'\n  },\n  {\n    combo: {\n      key: 'w'\n    },\n    commandId: 'Workspace.ToggleSidebarTab.workflows'\n  },\n  {\n    combo: {\n      key: 'n'\n    },\n    commandId: 'Workspace.ToggleSidebarTab.node-library'\n  },\n  {\n    combo: {\n      key: 'm'\n    },\n    commandId: 'Workspace.ToggleSidebarTab.model-library'\n  },\n  {\n    combo: {\n      key: 's',\n      ctrl: true\n    },\n    commandId: 'Comfy.SaveWorkflow'\n  },\n  {\n    combo: {\n      key: 'o',\n      ctrl: true\n    },\n    commandId: 'Comfy.OpenWorkflow'\n  },\n  {\n    combo: {\n      key: 'Backspace'\n    },\n    commandId: 'Comfy.ClearWorkflow'\n  },\n  {\n    combo: {\n      key: 'g',\n      ctrl: true\n    },\n    commandId: 'Comfy.Graph.GroupSelectedNodes'\n  },\n  {\n    combo: {\n      key: ',',\n      ctrl: true\n    },\n    commandId: 'Comfy.ShowSettingsDialog'\n  },\n  // For '=' both holding shift and not holding shift\n  {\n    combo: {\n      key: '=',\n      alt: true\n    },\n    commandId: 'Comfy.Canvas.ZoomIn',\n    targetElementId: 'graph-canvas'\n  },\n  {\n    combo: {\n      key: '+',\n      alt: true,\n      shift: true\n    },\n    commandId: 'Comfy.Canvas.ZoomIn',\n    targetElementId: 'graph-canvas'\n  },\n  // For number pad '+'\n  {\n    combo: {\n      key: '+',\n      alt: true\n    },\n    commandId: 'Comfy.Canvas.ZoomIn',\n    targetElementId: 'graph-canvas'\n  },\n  {\n    combo: {\n      key: '-',\n      alt: true\n    },\n    commandId: 'Comfy.Canvas.ZoomOut',\n    targetElementId: 'graph-canvas'\n  },\n  {\n    combo: {\n      key: '.'\n    },\n    commandId: 'Comfy.Canvas.FitView',\n    targetElementId: 'graph-canvas'\n  },\n  {\n    combo: {\n      key: 'p'\n    },\n    commandId: 'Comfy.Canvas.ToggleSelected.Pin',\n    targetElementId: 'graph-canvas'\n  },\n  {\n    combo: {\n      key: 'c',\n      alt: true\n    },\n    commandId: 'Comfy.Canvas.ToggleSelectedNodes.Collapse',\n    targetElementId: 'graph-canvas'\n  },\n  {\n    combo: {\n      key: 'b',\n      ctrl: true\n    },\n    commandId: 'Comfy.Canvas.ToggleSelectedNodes.Bypass',\n    targetElementId: 'graph-canvas'\n  },\n  {\n    combo: {\n      key: 'm',\n      ctrl: true\n    },\n    commandId: 'Comfy.Canvas.ToggleSelectedNodes.Mute',\n    targetElementId: 'graph-canvas'\n  },\n  {\n    combo: {\n      key: '`',\n      ctrl: true\n    },\n    commandId: 'Workspace.ToggleBottomPanelTab.logs-terminal'\n  },\n  {\n    combo: {\n      key: 'f'\n    },\n    commandId: 'Workspace.ToggleFocusMode'\n  }\n]\n", "import { CORE_KEYBINDINGS } from '@/constants/coreKeybindings'\nimport { useCommandStore } from '@/stores/commandStore'\nimport {\n  KeyComboImpl,\n  KeybindingImpl,\n  useKeybindingStore\n} from '@/stores/keybindingStore'\nimport { useSettingStore } from '@/stores/settingStore'\n\nexport const useKeybindingService = () => {\n  const keybindingStore = useKeybindingStore()\n  const commandStore = useCommandStore()\n  const settingStore = useSettingStore()\n\n  const keybindHandler = async function (event: KeyboardEvent) {\n    const keyCombo = KeyComboImpl.fromEvent(event)\n    if (keyCombo.isModifier) {\n      return\n    }\n\n    // Ignore reserved or non-modifier keybindings if typing in input fields\n    const target = event.composedPath()[0] as HTMLElement\n    if (\n      keyCombo.isReservedByTextInput &&\n      (target.tagName === 'TEXTAREA' ||\n        target.tagName === 'INPUT' ||\n        (target.tagName === 'SPAN' &&\n          target.classList.contains('property_value')))\n    ) {\n      return\n    }\n\n    const keybinding = keybindingStore.getKeybinding(keyCombo)\n    if (keybinding && keybinding.targetElementId !== 'graph-canvas') {\n      // Prevent default browser behavior first, then execute the command\n      event.preventDefault()\n      await commandStore.execute(keybinding.commandId)\n      return\n    }\n\n    // Only clear dialogs if not using modifiers\n    if (event.ctrlKey || event.altKey || event.metaKey) {\n      return\n    }\n\n    // Escape key: close the first open modal found, and all dialogs\n    if (event.key === 'Escape') {\n      const modals = document.querySelectorAll<HTMLElement>('.comfy-modal')\n      for (const modal of modals) {\n        const modalDisplay = window\n          .getComputedStyle(modal)\n          .getPropertyValue('display')\n\n        if (modalDisplay !== 'none') {\n          modal.style.display = 'none'\n          break\n        }\n      }\n\n      for (const d of document.querySelectorAll('dialog')) d.close()\n    }\n  }\n\n  const registerCoreKeybindings = () => {\n    for (const keybinding of CORE_KEYBINDINGS) {\n      keybindingStore.addDefaultKeybinding(new KeybindingImpl(keybinding))\n    }\n  }\n\n  function registerUserKeybindings() {\n    // Unset bindings first as new bindings might conflict with default bindings.\n    const unsetBindings = settingStore.get('Comfy.Keybinding.UnsetBindings')\n    for (const keybinding of unsetBindings) {\n      keybindingStore.unsetKeybinding(new KeybindingImpl(keybinding))\n    }\n    const newBindings = settingStore.get('Comfy.Keybinding.NewBindings')\n    for (const keybinding of newBindings) {\n      keybindingStore.addUserKeybinding(new KeybindingImpl(keybinding))\n    }\n  }\n\n  async function persistUserKeybindings() {\n    // TODO(https://github.com/Comfy-Org/ComfyUI_frontend/issues/1079):\n    // Allow setting multiple values at once in settingStore\n    await settingStore.set(\n      'Comfy.Keybinding.NewBindings',\n      Object.values(keybindingStore.getUserKeybindings())\n    )\n    await settingStore.set(\n      'Comfy.Keybinding.UnsetBindings',\n      Object.values(keybindingStore.getUserUnsetKeybindings())\n    )\n  }\n\n  return {\n    keybindHandler,\n    registerCoreKeybindings,\n    registerUserKeybindings,\n    persistUserKeybindings\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM,mBAAiC;AAAA,EAC5C;AAAA,IACE,OAAO;AAAA,MACL,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,WAAW;AAAA,EACb;AAAA;AAAA,EAEA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AAAA;AAAA,EAEA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,WAAW;AAAA,IACX,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,WAAW;AAAA,EACb;AAAA,EACA;AAAA,IACE,OAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,IACA,WAAW;AAAA,EACb;AACF;ACvKO,MAAM,uBAAuB,6BAAM;AACxC,QAAM,kBAAkB;AACxB,QAAM,eAAe;AACrB,QAAM,eAAe;AAEf,QAAA,iBAAiB,sCAAgB,OAAsB;AACrD,UAAA,WAAW,aAAa,UAAU,KAAK;AAC7C,QAAI,SAAS,YAAY;AACvB;AAAA,IACF;AAGA,UAAM,SAAS,MAAM,aAAa,EAAE,CAAC;AACrC,QACE,SAAS,0BACR,OAAO,YAAY,cAClB,OAAO,YAAY,WAClB,OAAO,YAAY,UAClB,OAAO,UAAU,SAAS,gBAAgB,IAC9C;AACA;AAAA,IACF;AAEM,UAAA,aAAa,gBAAgB,cAAc,QAAQ;AACrD,QAAA,cAAc,WAAW,oBAAoB,gBAAgB;AAE/D,YAAM,eAAe;AACf,YAAA,aAAa,QAAQ,WAAW,SAAS;AAC/C;AAAA,IACF;AAGA,QAAI,MAAM,WAAW,MAAM,UAAU,MAAM,SAAS;AAClD;AAAA,IACF;AAGI,QAAA,MAAM,QAAQ,UAAU;AACpB,YAAA,SAAS,SAAS,iBAA8B,cAAc;AACpE,iBAAW,SAAS,QAAQ;AAC1B,cAAM,eAAe,OAClB,iBAAiB,KAAK,EACtB,iBAAiB,SAAS;AAE7B,YAAI,iBAAiB,QAAQ;AAC3B,gBAAM,MAAM,UAAU;AACtB;AAAA,QACF;AAAA,MACF;AAEA,iBAAW,KAAK,SAAS,iBAAiB,QAAQ,KAAK;IACzD;AAAA,EAAA,GA9CqB;AAiDvB,QAAM,0BAA0B,6BAAM;AACpC,eAAW,cAAc,kBAAkB;AACzC,sBAAgB,qBAAqB,IAAI,eAAe,UAAU,CAAC;AAAA,IACrE;AAAA,EAAA,GAH8B;AAMhC,WAAS,0BAA0B;AAE3B,UAAA,gBAAgB,aAAa,IAAI,gCAAgC;AACvE,eAAW,cAAc,eAAe;AACtC,sBAAgB,gBAAgB,IAAI,eAAe,UAAU,CAAC;AAAA,IAChE;AACM,UAAA,cAAc,aAAa,IAAI,8BAA8B;AACnE,eAAW,cAAc,aAAa;AACpC,sBAAgB,kBAAkB,IAAI,eAAe,UAAU,CAAC;AAAA,IAClE;AAAA,EACF;AAVS;AAYT,iBAAe,yBAAyB;AAGtC,UAAM,aAAa;AAAA,MACjB;AAAA,MACA,OAAO,OAAO,gBAAgB,oBAAoB;AAAA,IAAA;AAEpD,UAAM,aAAa;AAAA,MACjB;AAAA,MACA,OAAO,OAAO,gBAAgB,yBAAyB;AAAA,IAAA;AAAA,EAE3D;AAXe;AAaR,SAAA;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ,GA3FoC;"}