{"version": 3, "file": "ServerConfigPanel-Cq12PEQH.js", "sources": ["../../src/components/dialog/content/setting/ServerConfigPanel.vue"], "sourcesContent": ["<template>\n  <PanelTemplate value=\"Server-Config\" class=\"server-config-panel\">\n    <template #header>\n      <div class=\"flex flex-col gap-2\">\n        <Message\n          v-if=\"modifiedConfigs.length > 0\"\n          severity=\"info\"\n          pt:text=\"w-full\"\n        >\n          <p>\n            {{ $t('serverConfig.modifiedConfigs') }}\n          </p>\n          <ul>\n            <li v-for=\"config in modifiedConfigs\" :key=\"config.id\">\n              {{ config.name }}: {{ config.initialValue }} → {{ config.value }}\n            </li>\n          </ul>\n          <div class=\"flex justify-end gap-2\">\n            <Button\n              :label=\"$t('serverConfig.revertChanges')\"\n              outlined\n              @click=\"revertChanges\"\n            />\n            <Button\n              :label=\"$t('serverConfig.restart')\"\n              outlined\n              severity=\"danger\"\n              @click=\"restartApp\"\n            />\n          </div>\n        </Message>\n        <Message v-if=\"commandLineArgs\" severity=\"secondary\" pt:text=\"w-full\">\n          <template #icon>\n            <i-lucide:terminal class=\"text-xl font-bold\" />\n          </template>\n          <div class=\"flex items-center justify-between\">\n            <p>{{ commandLineArgs }}</p>\n            <Button\n              icon=\"pi pi-clipboard\"\n              severity=\"secondary\"\n              text\n              @click=\"copyCommandLineArgs\"\n            />\n          </div>\n        </Message>\n      </div>\n    </template>\n    <div\n      v-for=\"([label, items], i) in Object.entries(serverConfigsByCategory)\"\n      :key=\"label\"\n    >\n      <Divider v-if=\"i > 0\" />\n      <h3>{{ $t(`serverConfigCategories.${label}`, label) }}</h3>\n      <div v-for=\"item in items\" :key=\"item.name\" class=\"mb-4\">\n        <FormItem\n          :id=\"item.id\"\n          v-model:formValue=\"item.value\"\n          :item=\"translateItem(item)\"\n          :label-class=\"{\n            'text-highlight': item.initialValue !== item.value\n          }\"\n        />\n      </div>\n    </div>\n  </PanelTemplate>\n</template>\n\n<script setup lang=\"ts\">\nimport { storeToRefs } from 'pinia'\nimport Button from 'primevue/button'\nimport Divider from 'primevue/divider'\nimport Message from 'primevue/message'\nimport { watch } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport FormItem from '@/components/common/FormItem.vue'\nimport { useCopyToClipboard } from '@/composables/useCopyToClipboard'\nimport type { ServerConfig } from '@/constants/serverConfig'\nimport { useServerConfigStore } from '@/stores/serverConfigStore'\nimport { useSettingStore } from '@/stores/settingStore'\nimport type { FormItem as FormItemType } from '@/types/settingTypes'\nimport { electronAPI } from '@/utils/envUtil'\n\nimport PanelTemplate from './PanelTemplate.vue'\n\nconst settingStore = useSettingStore()\nconst serverConfigStore = useServerConfigStore()\nconst {\n  serverConfigsByCategory,\n  serverConfigValues,\n  launchArgs,\n  commandLineArgs,\n  modifiedConfigs\n} = storeToRefs(serverConfigStore)\n\nconst revertChanges = () => {\n  serverConfigStore.revertChanges()\n}\n\nconst restartApp = async () => {\n  await electronAPI().restartApp()\n}\n\nwatch(launchArgs, async (newVal) => {\n  await settingStore.set('Comfy.Server.LaunchArgs', newVal)\n})\n\nwatch(serverConfigValues, async (newVal) => {\n  await settingStore.set('Comfy.Server.ServerConfigValues', newVal)\n})\n\nconst { copyToClipboard } = useCopyToClipboard()\nconst copyCommandLineArgs = async () => {\n  await copyToClipboard(commandLineArgs.value)\n}\n\nconst { t } = useI18n()\nconst translateItem = (item: ServerConfig<any>): FormItemType => {\n  return {\n    ...item,\n    name: t(`serverConfigItems.${item.id}.name`, item.name),\n    tooltip: item.tooltip\n      ? t(`serverConfigItems.${item.id}.tooltip`, item.tooltip)\n      : undefined\n  }\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA,UAAM,eAAe;AACrB,UAAM,oBAAoB;AACpB,UAAA;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA,IACE,YAAY,iBAAiB;AAEjC,UAAM,gBAAgB,6BAAM;AAC1B,wBAAkB,cAAc;AAAA,IAAA,GADZ;AAItB,UAAM,aAAa,mCAAY;AACvB,YAAA,YAAA,EAAc;IAAW,GADd;AAIb,UAAA,YAAY,OAAO,WAAW;AAC5B,YAAA,aAAa,IAAI,2BAA2B,MAAM;AAAA,IAAA,CACzD;AAEK,UAAA,oBAAoB,OAAO,WAAW;AACpC,YAAA,aAAa,IAAI,mCAAmC,MAAM;AAAA,IAAA,CACjE;AAEK,UAAA,EAAE,oBAAoB;AAC5B,UAAM,sBAAsB,mCAAY;AAChC,YAAA,gBAAgB,gBAAgB,KAAK;AAAA,IAAA,GADjB;AAItB,UAAA,EAAE,MAAM;AACR,UAAA,gBAAgB,wBAAC,SAA0C;AACxD,aAAA;AAAA,QACL,GAAG;AAAA,QACH,MAAM,EAAE,qBAAqB,KAAK,EAAE,SAAS,KAAK,IAAI;AAAA,QACtD,SAAS,KAAK,UACV,EAAE,qBAAqB,KAAK,EAAE,YAAY,KAAK,OAAO,IACtD;AAAA,MAAA;AAAA,IACN,GAPoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}