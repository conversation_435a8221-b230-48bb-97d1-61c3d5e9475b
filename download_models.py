#!/usr/bin/env python3
"""
ComfyUI 模型下载脚本
下载基础的 Stable Diffusion 模型文件
"""

import os
import requests
from pathlib import Path
import sys

def download_file(url, filename, description=""):
    """下载文件并显示进度"""
    print(f"正在下载 {description}: {filename}")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\r进度: {percent:.1f}% ({downloaded}/{total_size} bytes)", end='')
        
        print(f"\n✅ 下载完成: {filename}")
        return True
        
    except Exception as e:
        print(f"\n❌ 下载失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 ComfyUI 模型下载器")
    print("=" * 50)
    
    # 确保目录存在
    models_dir = Path("models/checkpoints")
    models_dir.mkdir(parents=True, exist_ok=True)
    
    # 模型下载列表
    models = [
        {
            "name": "Stable Diffusion 1.5 (精简版)",
            "filename": "v1-5-pruned-emaonly.safetensors",
            "url": "https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/v1-5-pruned-emaonly.safetensors",
            "size": "约 4GB"
        }
    ]
    
    print("可下载的模型:")
    for i, model in enumerate(models, 1):
        print(f"{i}. {model['name']} ({model['size']})")
    
    print("\n选择要下载的模型 (输入数字，或 'all' 下载全部):")
    choice = input("请选择: ").strip().lower()
    
    if choice == 'all':
        selected_models = models
    else:
        try:
            index = int(choice) - 1
            if 0 <= index < len(models):
                selected_models = [models[index]]
            else:
                print("❌ 无效选择")
                return
        except ValueError:
            print("❌ 无效输入")
            return
    
    # 下载选中的模型
    for model in selected_models:
        filepath = models_dir / model["filename"]
        
        if filepath.exists():
            print(f"⚠️  文件已存在: {model['filename']}")
            overwrite = input("是否覆盖? (y/N): ").strip().lower()
            if overwrite != 'y':
                continue
        
        print(f"\n开始下载: {model['name']}")
        success = download_file(model["url"], filepath, model["name"])
        
        if not success:
            print(f"❌ {model['name']} 下载失败")
        else:
            print(f"✅ {model['name']} 下载成功")
    
    print("\n🎉 下载完成！")
    print("现在您可以在 ComfyUI 中使用这些模型了。")

if __name__ == "__main__":
    main()
