{"version": 3, "file": "serverConfigStore-CxYg11Rb.js", "sources": ["../../src/stores/serverConfigStore.ts"], "sourcesContent": ["import { defineStore } from 'pinia'\nimport { computed, ref } from 'vue'\n\nimport { ServerConfig, ServerConfigValue } from '@/constants/serverConfig'\n\nexport type ServerConfigWithValue<T> = ServerConfig<T> & {\n  /**\n   * Current value.\n   */\n  value: T\n  /**\n   * Initial value loaded from settings.\n   */\n  initialValue: T\n}\n\nexport const useServerConfigStore = defineStore('serverConfig', () => {\n  const serverConfigById = ref<\n    Record<string, ServerConfigWithValue<ServerConfigValue>>\n  >({})\n  const serverConfigs = computed(() => {\n    return Object.values(serverConfigById.value)\n  })\n  const modifiedConfigs = computed<ServerConfigWithValue<ServerConfigValue>[]>(\n    () => {\n      return serverConfigs.value.filter((config) => {\n        return config.initialValue !== config.value\n      })\n    }\n  )\n  const revertChanges = () => {\n    for (const config of modifiedConfigs.value) {\n      config.value = config.initialValue\n    }\n  }\n  const serverConfigsByCategory = computed<\n    Record<string, ServerConfigWithValue<ServerConfigValue>[]>\n  >(() => {\n    return serverConfigs.value.reduce(\n      (acc, config) => {\n        const category = config.category?.[0] ?? 'General'\n        acc[category] = acc[category] || []\n        acc[category].push(config)\n        return acc\n      },\n      {} as Record<string, ServerConfigWithValue<ServerConfigValue>[]>\n    )\n  })\n  const serverConfigValues = computed<Record<string, ServerConfigValue>>(() => {\n    return Object.fromEntries(\n      serverConfigs.value.map((config) => {\n        return [\n          config.id,\n          config.value === config.defaultValue ||\n          config.value === null ||\n          config.value === undefined\n            ? undefined\n            : config.value\n        ]\n      })\n    )\n  })\n  const launchArgs = computed<Record<string, string>>(() => {\n    const args: Record<\n      string,\n      Omit<ServerConfigValue, 'undefined' | 'null'>\n    > = Object.assign(\n      {},\n      ...serverConfigs.value.map((config) => {\n        // Filter out configs that have the default value or undefined | null value\n        if (\n          config.value === config.defaultValue ||\n          config.value === null ||\n          config.value === undefined\n        ) {\n          return {}\n        }\n        return config.getValue\n          ? config.getValue(config.value)\n          : { [config.id]: config.value }\n      })\n    )\n\n    // Convert true to empty string\n    // Convert number to string\n    return Object.fromEntries(\n      Object.entries(args).map(([key, value]) => {\n        if (value === true) {\n          return [key, '']\n        }\n        return [key, value.toString()]\n      })\n    ) as Record<string, string>\n  })\n  const commandLineArgs = computed<string>(() => {\n    return Object.entries(launchArgs.value)\n      .map(([key, value]) => [`--${key}`, value])\n      .flat()\n      .filter((arg: string) => arg !== '')\n      .join(' ')\n  })\n\n  function loadServerConfig(\n    configs: ServerConfig<ServerConfigValue>[],\n    values: Record<string, ServerConfigValue>\n  ) {\n    for (const config of configs) {\n      const value = values[config.id] ?? config.defaultValue\n      serverConfigById.value[config.id] = {\n        ...config,\n        value,\n        initialValue: value\n      }\n    }\n  }\n\n  return {\n    serverConfigById,\n    serverConfigs,\n    modifiedConfigs,\n    serverConfigsByCategory,\n    serverConfigValues,\n    launchArgs,\n    commandLineArgs,\n    revertChanges,\n    loadServerConfig\n  }\n})\n"], "names": [], "mappings": ";;;;AAgBa,MAAA,uBAAuB,YAAY,gBAAgB,MAAM;AAC9D,QAAA,mBAAmB,IAEvB,CAAA,CAAE;AACE,QAAA,gBAAgB,SAAS,MAAM;AAC5B,WAAA,OAAO,OAAO,iBAAiB,KAAK;AAAA,EAAA,CAC5C;AACD,QAAM,kBAAkB;AAAA,IACtB,MAAM;AACJ,aAAO,cAAc,MAAM,OAAO,CAAC,WAAW;AACrC,eAAA,OAAO,iBAAiB,OAAO;AAAA,MAAA,CACvC;AAAA,IACH;AAAA,EAAA;AAEF,QAAM,gBAAgB,6BAAM;AACf,eAAA,UAAU,gBAAgB,OAAO;AAC1C,aAAO,QAAQ,OAAO;AAAA,IACxB;AAAA,EAAA,GAHoB;AAKhB,QAAA,0BAA0B,SAE9B,MAAM;AACN,WAAO,cAAc,MAAM;AAAA,MACzB,CAAC,KAAK,WAAW;AACf,cAAM,WAAW,OAAO,WAAW,CAAC,KAAK;AACzC,YAAI,QAAQ,IAAI,IAAI,QAAQ,KAAK,CAAA;AAC7B,YAAA,QAAQ,EAAE,KAAK,MAAM;AAClB,eAAA;AAAA,MACT;AAAA,MACA,CAAC;AAAA,IAAA;AAAA,EACH,CACD;AACK,QAAA,qBAAqB,SAA4C,MAAM;AAC3E,WAAO,OAAO;AAAA,MACZ,cAAc,MAAM,IAAI,CAAC,WAAW;AAC3B,eAAA;AAAA,UACL,OAAO;AAAA,UACP,OAAO,UAAU,OAAO,gBACxB,OAAO,UAAU,QACjB,OAAO,UAAU,SACb,SACA,OAAO;AAAA,QAAA;AAAA,MACb,CACD;AAAA,IAAA;AAAA,EACH,CACD;AACK,QAAA,aAAa,SAAiC,MAAM;AACxD,UAAM,OAGF,OAAO;AAAA,MACT,CAAC;AAAA,MACD,GAAG,cAAc,MAAM,IAAI,CAAC,WAAW;AAGnC,YAAA,OAAO,UAAU,OAAO,gBACxB,OAAO,UAAU,QACjB,OAAO,UAAU,QACjB;AACA,iBAAO;QACT;AACA,eAAO,OAAO,WACV,OAAO,SAAS,OAAO,KAAK,IAC5B,EAAE,CAAC,OAAO,EAAE,GAAG,OAAO,MAAM;AAAA,MAAA,CACjC;AAAA,IAAA;AAKH,WAAO,OAAO;AAAA,MACZ,OAAO,QAAQ,IAAI,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AACzC,YAAI,UAAU,MAAM;AACX,iBAAA,CAAC,KAAK,EAAE;AAAA,QACjB;AACA,eAAO,CAAC,KAAK,MAAM,SAAU,CAAA;AAAA,MAAA,CAC9B;AAAA,IAAA;AAAA,EACH,CACD;AACK,QAAA,kBAAkB,SAAiB,MAAM;AACtC,WAAA,OAAO,QAAQ,WAAW,KAAK,EACnC,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,EACzC,KAAA,EACA,OAAO,CAAC,QAAgB,QAAQ,EAAE,EAClC,KAAK,GAAG;AAAA,EAAA,CACZ;AAEQ,WAAA,iBACP,SACA,QACA;AACA,eAAW,UAAU,SAAS;AAC5B,YAAM,QAAQ,OAAO,OAAO,EAAE,KAAK,OAAO;AACzB,uBAAA,MAAM,OAAO,EAAE,IAAI;AAAA,QAClC,GAAG;AAAA,QACH;AAAA,QACA,cAAc;AAAA,MAAA;AAAA,IAElB;AAAA,EACF;AAZS;AAcF,SAAA;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ,CAAC;"}