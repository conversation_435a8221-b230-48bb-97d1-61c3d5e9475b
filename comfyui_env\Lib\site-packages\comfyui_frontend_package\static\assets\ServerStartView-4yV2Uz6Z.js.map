{"version": 3, "file": "ServerStartView-4yV2Uz6Z.js", "sources": ["../../src/views/ServerStartView.vue"], "sourcesContent": ["<template>\n  <BaseViewTemplate dark class=\"flex-col\">\n    <div class=\"flex flex-col w-full h-full items-center\">\n      <h2 class=\"text-2xl font-bold\">\n        {{ t(`serverStart.process.${status}`) }}\n        <span v-if=\"status === ProgressStatus.ERROR\">\n          v{{ electronVersion }}\n        </span>\n      </h2>\n      <div\n        v-if=\"status === ProgressStatus.ERROR\"\n        class=\"flex flex-col items-center gap-4\"\n      >\n        <div class=\"flex items-center my-4 gap-2\">\n          <Button\n            icon=\"pi pi-flag\"\n            severity=\"secondary\"\n            :label=\"t('serverStart.reportIssue')\"\n            @click=\"reportIssue\"\n          />\n          <Button\n            icon=\"pi pi-file\"\n            severity=\"secondary\"\n            :label=\"t('serverStart.openLogs')\"\n            @click=\"openLogs\"\n          />\n          <Button\n            icon=\"pi pi-wrench\"\n            :label=\"t('serverStart.troubleshoot')\"\n            @click=\"troubleshoot\"\n          />\n        </div>\n        <Button\n          v-if=\"!terminalVisible\"\n          icon=\"pi pi-search\"\n          severity=\"secondary\"\n          :label=\"t('serverStart.showTerminal')\"\n          @click=\"terminalVisible = true\"\n        />\n      </div>\n      <BaseTerminal v-show=\"terminalVisible\" @created=\"terminalCreated\" />\n    </div>\n  </BaseViewTemplate>\n</template>\n\n<script setup lang=\"ts\">\nimport { ProgressStatus } from '@comfyorg/comfyui-electron-types'\nimport { Terminal } from '@xterm/xterm'\nimport Button from 'primevue/button'\nimport { Ref, onMounted, ref } from 'vue'\nimport { useI18n } from 'vue-i18n'\n\nimport BaseTerminal from '@/components/bottomPanel/tabs/terminal/BaseTerminal.vue'\nimport type { useTerminal } from '@/composables/bottomPanelTabs/useTerminal'\nimport { electronAPI } from '@/utils/envUtil'\nimport BaseViewTemplate from '@/views/templates/BaseViewTemplate.vue'\n\nconst electron = electronAPI()\nconst { t } = useI18n()\n\nconst status = ref<ProgressStatus>(ProgressStatus.INITIAL_STATE)\nconst electronVersion = ref<string>('')\nlet xterm: Terminal | undefined\n\nconst terminalVisible = ref(true)\n\nconst updateProgress = ({ status: newStatus }: { status: ProgressStatus }) => {\n  status.value = newStatus\n\n  // Make critical error screen more obvious.\n  if (newStatus === ProgressStatus.ERROR) terminalVisible.value = false\n  else xterm?.clear()\n}\n\nconst terminalCreated = (\n  { terminal, useAutoSize }: ReturnType<typeof useTerminal>,\n  root: Ref<HTMLElement | undefined>\n) => {\n  xterm = terminal\n\n  useAutoSize({ root, autoRows: true, autoCols: true })\n  electron.onLogMessage((message: string) => {\n    terminal.write(message)\n  })\n\n  terminal.options.cursorBlink = false\n  terminal.options.disableStdin = true\n  terminal.options.cursorInactiveStyle = 'block'\n}\n\nconst troubleshoot = () => electron.startTroubleshooting()\nconst reportIssue = () => {\n  window.open('https://forum.comfy.org/c/v1-feedback/', '_blank')\n}\nconst openLogs = () => electron.openLogsFolder()\n\nonMounted(async () => {\n  electron.sendReady()\n  electron.onProgressUpdate(updateProgress)\n  electronVersion.value = await electron.getElectronVersion()\n})\n</script>\n\n<style scoped>\n:deep(.xterm-helper-textarea) {\n  /* Hide this as it moves all over when uv is running */\n  display: none;\n}\n</style>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAyDA,UAAM,WAAW;AACX,UAAA,EAAE,MAAM;AAER,UAAA,SAAS,IAAoB,eAAe,aAAa;AACzD,UAAA,kBAAkB,IAAY,EAAE;AAClC,QAAA;AAEE,UAAA,kBAAkB,IAAI,IAAI;AAEhC,UAAM,iBAAiB,wBAAC,EAAE,QAAQ,gBAA4C;AAC5E,aAAO,QAAQ;AAGf,UAAI,cAAc,eAAe,MAAO,iBAAgB,QAAQ;AAAA,kBACpD,MAAM;AAAA,IAAA,GALG;AAQvB,UAAM,kBAAkB,wBACtB,EAAE,UAAU,YAAA,GACZ,SACG;AACK,cAAA;AAER,kBAAY,EAAE,MAAM,UAAU,MAAM,UAAU,MAAM;AAC3C,eAAA,aAAa,CAAC,YAAoB;AACzC,iBAAS,MAAM,OAAO;AAAA,MAAA,CACvB;AAED,eAAS,QAAQ,cAAc;AAC/B,eAAS,QAAQ,eAAe;AAChC,eAAS,QAAQ,sBAAsB;AAAA,IAAA,GAbjB;AAgBlB,UAAA,eAAe,6BAAM,SAAS,wBAAf;AACrB,UAAM,cAAc,6BAAM;AACjB,aAAA,KAAK,0CAA0C,QAAQ;AAAA,IAAA,GAD5C;AAGd,UAAA,WAAW,6BAAM,SAAS,kBAAf;AAEjB,cAAU,YAAY;AACpB,eAAS,UAAU;AACnB,eAAS,iBAAiB,cAAc;AACxB,sBAAA,QAAQ,MAAM,SAAS,mBAAmB;AAAA,IAAA,CAC3D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}