{"id": "01d66ae9-78be-4a8d-b737-24eee5e1d447", "revision": 0, "last_node_id": 80, "last_link_id": 166, "nodes": [{"id": 53, "type": "EmptySD3LatentImage", "pos": [150, 430], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "shape": 3, "type": "LATENT", "slot_index": 0, "links": [100]}], "properties": {"Node name for S&R": "EmptySD3LatentImage", "cnr_id": "comfy-core", "ver": "0.3.28", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [1024, 1024, 1]}, {"id": 8, "type": "VAEDecode", "pos": [900, 190], "size": [210, 46], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 160}, {"name": "vae", "type": "VAE", "link": 107}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [51]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.28", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": []}, {"id": 72, "type": "<PERSON>downNote", "pos": [1250, -130], "size": [320, 360], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "Sampling Settings", "properties": {}, "widgets_values": ["## Official sampling settings\n\nProvided for reference, my workflows may have slightly different settings.\n\n### HiDream Full\n\n* hidream_i1_full_fp16.safetensors\n* shift: 3.0\n* steps: 50\n* sampler: uni_pc\n* scheduler: simple\n* cfg: 5.0\n\n### HiDream Dev\n\n* hidream_i1_dev_bf16.safetensors\n* shift: 6.0\n* steps: 28\n* sampler: lcm\n* scheduler: normal\n* cfg: 1.0 (no negative prompt)\n\n### HiDream Fast\n\n* hidream_i1_fast_bf16.safetensors\n* shift: 3.0\n* steps: 16\n* sampler: lcm\n* scheduler: normal\n* cfg: 1.0 (no negative prompt)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 40, "type": "CLIPTextEncode", "pos": [530, 210], "size": [432, 192], "flags": {"collapsed": true}, "order": 9, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 112}], "outputs": [{"name": "CONDITIONING", "shape": 3, "type": "CONDITIONING", "slot_index": 0, "links": [114]}], "title": "Negative Prompt", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.28", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["bad ugly jpeg artifacts"], "color": "#322", "bgcolor": "#533"}, {"id": 70, "type": "ModelSamplingSD3", "pos": [520, -140], "size": [210, 58], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 166}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [163]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "cnr_id": "comfy-core", "ver": "0.3.28", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [3.0000000000000004]}, {"id": 9, "type": "SaveImage", "pos": [900, 290], "size": [570, 420], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 51}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "cnr_id": "comfy-core", "ver": "0.3.28", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["ComfyUI"]}, {"id": 55, "type": "VAELoader", "pos": [150, 230], "size": [310, 60], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [107]}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.28", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/resolve/main/split_files/vae/ae.safetensors?download=true", "directory": "vae"}], "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 54, "type": "QuadrupleCLIPLoader", "pos": [140, 40], "size": [320, 130], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [111, 112]}], "properties": {"Node name for S&R": "QuadrupleCLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.28", "models": [{"name": "clip_l_hidream.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/resolve/main/split_files/text_encoders/clip_l_hidream.safetensors?download=true", "directory": "text_encoders"}, {"name": "clip_g_hidream.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/resolve/main/split_files/text_encoders/clip_g_hidream.safetensors?download=true", "directory": "text_encoders"}, {"name": "t5xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/resolve/main/split_files/text_encoders/t5xxl_fp8_e4m3fn_scaled.safetensors?download=true", "directory": "text_encoders"}, {"name": "llama_3.1_8b_instruct_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/resolve/main/split_files/text_encoders/t5xxl_fp8_e4m3fn_scaled.safetensors?download=true", "directory": "text_encoders"}], "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["clip_l_hidream.safetensors", "clip_g_hidream.safetensors", "t5xxl_fp8_e4m3fn_scaled.safetensors", "llama_3.1_8b_instruct_fp8_scaled.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 16, "type": "CLIPTextEncode", "pos": [520, -30], "size": [350, 170], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 111}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [21]}], "title": "Positive Prompt", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.28", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["photo of an androgynous humanoid. fuzzy hair. Light from above. Dark. Multilayered costume. Minimalistic. Studio lighting. Frontal. Looking at the viewer"], "color": "#232", "bgcolor": "#353"}, {"id": 76, "type": "UNETLoader", "pos": [180, -100], "size": [280, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [166]}], "properties": {"Node name for S&R": "UNETLoader", "models": [{"name": "hidream_i1_full_fp8.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/resolve/main/split_files/diffusion_models/hidream_i1_full_fp8.safetensors?download=true", "directory": "diffusion_models"}]}, "widgets_values": ["hidream_i1_full_fp8.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [910, -130], "size": [310, 262], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 163}, {"name": "positive", "type": "CONDITIONING", "link": 21}, {"name": "negative", "type": "CONDITIONING", "link": 114}, {"name": "latent_image", "type": "LATENT", "link": 100}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [160]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.28", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [647719102242276, "randomize", 50, 5, "uni_pc", "simple", 1]}, {"id": 79, "type": "<PERSON>downNote", "pos": [-100, -110], "size": [240, 120], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["You can try changing the weight_dtype to fp8 if you are running out of memory.\n\nDownload one of diffusion Models file [here](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/tree/main/split_files/diffusion_models)\n\nPut it in the **ComfyUI/models/diffusion_models** directory"], "color": "#432", "bgcolor": "#653"}, {"id": 78, "type": "<PERSON>downNote", "pos": [-100, 50], "size": [220, 120], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Download all of the text_encoders models [here](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/tree/main/split_files/text_encoders)\n\nPut them in the **ComfyUI/models/text_encoders** directory"], "color": "#432", "bgcolor": "#653"}, {"id": 80, "type": "<PERSON>downNote", "pos": [-100, 230], "size": [210, 88], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Download the VAE model [here](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/vae/ae.safetensors) \n\nPut it in the **ComfyUI/models/vae** directory"], "color": "#432", "bgcolor": "#653"}], "links": [[21, 16, 0, 3, 1, "CONDITIONING"], [51, 8, 0, 9, 0, "IMAGE"], [100, 53, 0, 3, 3, "LATENT"], [107, 55, 0, 8, 1, "VAE"], [111, 54, 0, 16, 0, "CLIP"], [112, 54, 0, 40, 0, "CLIP"], [114, 40, 0, 3, 2, "CONDITIONING"], [160, 3, 0, 8, 0, "LATENT"], [163, 70, 0, 3, 0, "MODEL"], [166, 76, 0, 70, 0, "MODEL"]], "groups": [{"id": 1, "title": "Load Models Here", "bounding": [-120, -180, 600, 520], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Size", "bounding": [140, 360, 335, 189.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.9646149645000134, "offset": [511.84952762688897, 179.14172009127353]}, "node_versions": {"comfy-core": "0.3.28"}}, "version": 0.4}