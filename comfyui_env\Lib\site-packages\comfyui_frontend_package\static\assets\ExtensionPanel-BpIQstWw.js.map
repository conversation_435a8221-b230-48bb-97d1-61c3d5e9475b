{"version": 3, "file": "ExtensionPanel-BpIQstWw.js", "sources": ["../../src/components/dialog/content/setting/ExtensionPanel.vue"], "sourcesContent": ["<template>\n  <PanelTemplate value=\"Extension\" class=\"extension-panel\">\n    <template #header>\n      <SearchBox\n        v-model=\"filters['global'].value\"\n        :placeholder=\"$t('g.searchExtensions') + '...'\"\n      />\n      <Message\n        v-if=\"hasChanges\"\n        severity=\"info\"\n        pt:text=\"w-full\"\n        class=\"max-h-96 overflow-y-auto\"\n      >\n        <ul>\n          <li v-for=\"ext in changedExtensions\" :key=\"ext.name\">\n            <span>\n              {{ extensionStore.isExtensionEnabled(ext.name) ? '[-]' : '[+]' }}\n            </span>\n            {{ ext.name }}\n          </li>\n        </ul>\n        <div class=\"flex justify-end\">\n          <Button\n            :label=\"$t('g.reloadToApplyChanges')\"\n            outlined\n            severity=\"danger\"\n            @click=\"applyChanges\"\n          />\n        </div>\n      </Message>\n    </template>\n    <DataTable\n      :value=\"extensionStore.extensions\"\n      striped-rows\n      size=\"small\"\n      :filters=\"filters\"\n    >\n      <Column :header=\"$t('g.extensionName')\" sortable field=\"name\">\n        <template #body=\"slotProps\">\n          {{ slotProps.data.name }}\n          <Tag\n            v-if=\"extensionStore.isCoreExtension(slotProps.data.name)\"\n            value=\"Core\"\n          />\n        </template>\n      </Column>\n      <Column\n        :pt=\"{\n          headerCell: 'flex items-center justify-end',\n          bodyCell: 'flex items-center justify-end'\n        }\"\n      >\n        <template #header>\n          <Button\n            icon=\"pi pi-ellipsis-h\"\n            text\n            severity=\"secondary\"\n            @click=\"menu?.show($event)\"\n          />\n          <ContextMenu ref=\"menu\" :model=\"contextMenuItems\" />\n        </template>\n        <template #body=\"slotProps\">\n          <ToggleSwitch\n            v-model=\"editingEnabledExtensions[slotProps.data.name]\"\n            :disabled=\"extensionStore.isExtensionReadOnly(slotProps.data.name)\"\n            @change=\"updateExtensionStatus\"\n          />\n        </template>\n      </Column>\n    </DataTable>\n  </PanelTemplate>\n</template>\n\n<script setup lang=\"ts\">\nimport { FilterMatchMode } from '@primevue/core/api'\nimport Button from 'primevue/button'\nimport Column from 'primevue/column'\nimport ContextMenu from 'primevue/contextmenu'\nimport DataTable from 'primevue/datatable'\nimport Message from 'primevue/message'\nimport Tag from 'primevue/tag'\nimport ToggleSwitch from 'primevue/toggleswitch'\nimport { computed, onMounted, ref } from 'vue'\n\nimport SearchBox from '@/components/common/SearchBox.vue'\nimport { useExtensionStore } from '@/stores/extensionStore'\nimport { useSettingStore } from '@/stores/settingStore'\n\nimport PanelTemplate from './PanelTemplate.vue'\n\nconst filters = ref({\n  global: { value: '', matchMode: FilterMatchMode.CONTAINS }\n})\n\nconst extensionStore = useExtensionStore()\nconst settingStore = useSettingStore()\n\nconst editingEnabledExtensions = ref<Record<string, boolean>>({})\n\nonMounted(() => {\n  extensionStore.extensions.forEach((ext) => {\n    editingEnabledExtensions.value[ext.name] =\n      extensionStore.isExtensionEnabled(ext.name)\n  })\n})\n\nconst changedExtensions = computed(() => {\n  return extensionStore.extensions.filter(\n    (ext) =>\n      editingEnabledExtensions.value[ext.name] !==\n      extensionStore.isExtensionEnabled(ext.name)\n  )\n})\n\nconst hasChanges = computed(() => {\n  return changedExtensions.value.length > 0\n})\n\nconst updateExtensionStatus = async () => {\n  const editingDisabledExtensionNames = Object.entries(\n    editingEnabledExtensions.value\n  )\n    .filter(([_, enabled]) => !enabled)\n    .map(([name]) => name)\n\n  await settingStore.set('Comfy.Extension.Disabled', [\n    ...extensionStore.inactiveDisabledExtensionNames,\n    ...editingDisabledExtensionNames\n  ])\n}\n\nconst enableAllExtensions = async () => {\n  extensionStore.extensions.forEach((ext) => {\n    if (extensionStore.isExtensionReadOnly(ext.name)) return\n\n    editingEnabledExtensions.value[ext.name] = true\n  })\n  await updateExtensionStatus()\n}\n\nconst disableAllExtensions = async () => {\n  extensionStore.extensions.forEach((ext) => {\n    if (extensionStore.isExtensionReadOnly(ext.name)) return\n\n    editingEnabledExtensions.value[ext.name] = false\n  })\n  await updateExtensionStatus()\n}\n\nconst disableThirdPartyExtensions = async () => {\n  extensionStore.extensions.forEach((ext) => {\n    if (extensionStore.isCoreExtension(ext.name)) return\n\n    editingEnabledExtensions.value[ext.name] = false\n  })\n  await updateExtensionStatus()\n}\n\nconst applyChanges = () => {\n  // Refresh the page to apply changes\n  window.location.reload()\n}\n\nconst menu = ref<InstanceType<typeof ContextMenu>>()\nconst contextMenuItems = [\n  {\n    label: 'Enable All',\n    icon: 'pi pi-check',\n    command: enableAllExtensions\n  },\n  {\n    label: 'Disable All',\n    icon: 'pi pi-times',\n    command: disableAllExtensions\n  },\n  {\n    label: 'Disable 3rd Party',\n    icon: 'pi pi-times',\n    command: disableThirdPartyExtensions,\n    disabled: !extensionStore.hasThirdPartyExtensions\n  }\n]\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;AA0FA,UAAM,UAAU,IAAI;AAAA,MAClB,QAAQ,EAAE,OAAO,IAAI,WAAW,gBAAgB,SAAS;AAAA,IAAA,CAC1D;AAED,UAAM,iBAAiB;AACvB,UAAM,eAAe;AAEf,UAAA,2BAA2B,IAA6B,CAAA,CAAE;AAEhE,cAAU,MAAM;AACC,qBAAA,WAAW,QAAQ,CAAC,QAAQ;AACzC,iCAAyB,MAAM,IAAI,IAAI,IACrC,eAAe,mBAAmB,IAAI,IAAI;AAAA,MAAA,CAC7C;AAAA,IAAA,CACF;AAEK,UAAA,oBAAoB,SAAS,MAAM;AACvC,aAAO,eAAe,WAAW;AAAA,QAC/B,CAAC,QACC,yBAAyB,MAAM,IAAI,IAAI,MACvC,eAAe,mBAAmB,IAAI,IAAI;AAAA,MAAA;AAAA,IAC9C,CACD;AAEK,UAAA,aAAa,SAAS,MAAM;AACzB,aAAA,kBAAkB,MAAM,SAAS;AAAA,IAAA,CACzC;AAED,UAAM,wBAAwB,mCAAY;AACxC,YAAM,gCAAgC,OAAO;AAAA,QAC3C,yBAAyB;AAAA,MAExB,EAAA,OAAO,CAAC,CAAC,GAAG,OAAO,MAAM,CAAC,OAAO,EACjC,IAAI,CAAC,CAAC,IAAI,MAAM,IAAI;AAEjB,YAAA,aAAa,IAAI,4BAA4B;AAAA,QACjD,GAAG,eAAe;AAAA,QAClB,GAAG;AAAA,MAAA,CACJ;AAAA,IAAA,GAV2B;AAa9B,UAAM,sBAAsB,mCAAY;AACvB,qBAAA,WAAW,QAAQ,CAAC,QAAQ;AACzC,YAAI,eAAe,oBAAoB,IAAI,IAAI,EAAG;AAEzB,iCAAA,MAAM,IAAI,IAAI,IAAI;AAAA,MAAA,CAC5C;AACD,YAAM,sBAAsB;AAAA,IAAA,GANF;AAS5B,UAAM,uBAAuB,mCAAY;AACxB,qBAAA,WAAW,QAAQ,CAAC,QAAQ;AACzC,YAAI,eAAe,oBAAoB,IAAI,IAAI,EAAG;AAEzB,iCAAA,MAAM,IAAI,IAAI,IAAI;AAAA,MAAA,CAC5C;AACD,YAAM,sBAAsB;AAAA,IAAA,GAND;AAS7B,UAAM,8BAA8B,mCAAY;AAC/B,qBAAA,WAAW,QAAQ,CAAC,QAAQ;AACzC,YAAI,eAAe,gBAAgB,IAAI,IAAI,EAAG;AAErB,iCAAA,MAAM,IAAI,IAAI,IAAI;AAAA,MAAA,CAC5C;AACD,YAAM,sBAAsB;AAAA,IAAA,GANM;AASpC,UAAM,eAAe,6BAAM;AAEzB,aAAO,SAAS;IAAO,GAFJ;AAKrB,UAAM,OAAO;AACb,UAAM,mBAAmB;AAAA,MACvB;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU,CAAC,eAAe;AAAA,MAC5B;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}