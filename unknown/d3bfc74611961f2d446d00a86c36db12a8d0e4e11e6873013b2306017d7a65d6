# SD3.5 Large 手动下载指南

## 🚨 下载权限问题

您遇到的 401 错误是因为 SD3.5 Large 需要：
1. Hugging Face 账户登录
2. 同意 Stability AI 社区许可证

## 🔧 解决方案

### 方案 1: 浏览器手动下载（最简单）

#### 步骤 1: 注册/登录 Hugging Face
1. 访问：https://huggingface.co/
2. 点击右上角 "Sign Up" 注册（如果没有账户）
3. 或点击 "Log In" 登录

#### 步骤 2: 访问模型页面
1. 访问：https://huggingface.co/stabilityai/stable-diffusion-3.5-large
2. 点击 **"Agree and access repository"**
3. 阅读并同意许可协议

#### 步骤 3: 下载文件
1. 点击 **"Files and versions"** 标签
2. 下载以下文件：

**主模型文件**：
- 文件名：`sd3.5_large.safetensors`
- 大小：8.9GB
- 点击文件名 → 点击 "Download" 按钮

**文本编码器**：
- 进入 `text_encoders` 文件夹
- 文件名：`clip_g.safetensors`
- 大小：1.4GB
- 点击文件名 → 点击 "Download" 按钮

#### 步骤 4: 放置文件
将下载的文件放入正确位置：

```
下载的文件 → 目标位置
sd3.5_large.safetensors → D:\AI\pic\ComfyUI-master\models\checkpoints\
clip_g.safetensors → D:\AI\pic\ComfyUI-master\models\clip\
```

### 方案 2: 使用 Hugging Face CLI

#### 步骤 1: 登录 Hugging Face CLI
```bash
# 激活虚拟环境
.\comfyui_env\Scripts\Activate.ps1

# 登录 Hugging Face
huggingface-cli login
```

#### 步骤 2: 获取 Access Token
1. 访问：https://huggingface.co/settings/tokens
2. 点击 "New token"
3. 选择 "Read" 权限
4. 复制生成的 token
5. 在命令行中粘贴 token

#### 步骤 3: 运行认证下载脚本
```bash
python download_sd35_with_auth.py
```

### 方案 3: 使用第三方下载工具

#### 推荐工具：
- **IDM (Internet Download Manager)**
- **Free Download Manager**
- **迅雷**

#### 下载链接（需要先登录 HF）：
```
主模型：
https://huggingface.co/stabilityai/stable-diffusion-3.5-large/resolve/main/sd3.5_large.safetensors

CLIP-G：
https://huggingface.co/stabilityai/stable-diffusion-3.5-large/resolve/main/text_encoders/clip_g.safetensors
```

## 📁 文件验证

下载完成后，验证文件：

### 检查文件大小
```bash
# 检查主模型
dir models\checkpoints\sd3.5_large.safetensors

# 检查 CLIP-G
dir models\clip\clip_g.safetensors
```

### 预期文件大小
- `sd3.5_large.safetensors`: 约 8.9GB (9,563,500,000+ 字节)
- `clip_g.safetensors`: 约 1.4GB (1,400,000,000+ 字节)

## 🚀 下载完成后的使用

### 1. 验证文件结构
```
models/
├── checkpoints/
│   ├── sd3.5_large.safetensors ✅ (新增)
│   └── v1-5-pruned-emaonly.safetensors ✅
├── clip/
│   ├── clip_g.safetensors ✅ (新增)
│   ├── clip_l.safetensors ✅
│   └── t5xxl_fp8_e4m3fn.safetensors ✅
└── vae/
    └── ae.safetensors ✅
```

### 2. 启动 ComfyUI（SD3.5 需要更多显存）
```bash
# 使用低显存模式
python main.py --lowvram --auto-launch
```

### 3. 导入 SD3.5 工作流
```
拖拽 workflows/sd35_large_best_quality.json 到 ComfyUI 界面
```

### 4. 测试生成
使用这个提示词测试：
```
"a professional portrait photograph of a beautiful woman, natural lighting, photorealistic, highly detailed, 8k resolution, masterpiece"
```

## 🔍 故障排除

### 1. 下载中断
- 使用支持断点续传的下载工具
- 或重新开始下载

### 2. 文件损坏
- 检查文件大小是否正确
- 重新下载损坏的文件

### 3. 权限问题
- 确保已登录 Hugging Face
- 确保已同意许可协议

### 4. 显存不足
```bash
# 使用更激进的显存优化
python main.py --lowvram --cpu-vae --auto-launch
```

## 💡 下载建议

### 推荐下载顺序：
1. **先下载主模型** (`sd3.5_large.safetensors`)
2. **测试是否能正常加载**
3. **再下载 CLIP-G** (`clip_g.safetensors`)

### 网络优化：
- 使用稳定的网络连接
- 避免高峰时段下载
- 考虑使用代理或 VPN（如果访问慢）

## 🎉 完成后的优势

下载完成后，您将拥有：
- **三个顶级模型**：Flux + SD3.5 + SD1.5
- **不同场景的最佳选择**
- **完整的 AI 图像生成工作站**

现在开始下载吧！推荐使用浏览器手动下载，最简单可靠。📥✨
