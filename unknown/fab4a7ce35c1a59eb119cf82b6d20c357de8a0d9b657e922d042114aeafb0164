#!/usr/bin/env python3
"""
使用 Hugging Face CLI 下载 SD3.5 Large
需要先登录 Hugging Face 账户
"""

import os
import subprocess
from pathlib import Path
import sys

def run_command(command, description=""):
    """运行命令并显示结果"""
    print(f"执行: {description}")
    print(f"命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            if result.stdout:
                print(result.stdout)
            return True
        else:
            print(f"❌ {description} 失败")
            if result.stderr:
                print(f"错误: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 SD3.5 Large 认证下载器")
    print("=" * 50)
    
    # 确保目录存在
    checkpoints_dir = Path("models/checkpoints")
    clip_dir = Path("models/clip")
    checkpoints_dir.mkdir(parents=True, exist_ok=True)
    clip_dir.mkdir(parents=True, exist_ok=True)
    
    print("此脚本需要 Hugging Face 账户登录")
    print("请确保您已经:")
    print("1. 注册了 Hugging Face 账户")
    print("2. 同意了 SD3.5 Large 的许可协议")
    print()
    
    # 检查是否已登录
    print("检查 Hugging Face 登录状态...")
    login_check = subprocess.run("huggingface-cli whoami", shell=True, capture_output=True, text=True)
    
    if login_check.returncode != 0:
        print("❌ 未登录 Hugging Face")
        print("请先登录:")
        print("1. 运行: huggingface-cli login")
        print("2. 输入您的 Hugging Face token")
        print("3. Token 获取地址: https://huggingface.co/settings/tokens")
        
        login_now = input("是否现在登录? (y/N): ").strip().lower()
        if login_now == 'y':
            print("正在启动登录...")
            os.system("huggingface-cli login")
        else:
            print("请先登录后再运行此脚本")
            return
    else:
        print("✅ 已登录 Hugging Face")
        print(login_check.stdout)
    
    print("\n可下载的文件:")
    print("1. SD3.5 Large 主模型 (8.9GB)")
    print("2. CLIP-G 文本编码器 (1.4GB)")
    print("3. 全部下载")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    downloads = []
    
    if choice == "1":
        downloads = [
            {
                "repo": "stabilityai/stable-diffusion-3.5-large",
                "filename": "sd3.5_large.safetensors",
                "local_dir": str(checkpoints_dir),
                "description": "SD3.5 Large 主模型"
            }
        ]
    elif choice == "2":
        downloads = [
            {
                "repo": "stabilityai/stable-diffusion-3.5-large",
                "filename": "text_encoders/clip_g.safetensors",
                "local_dir": str(clip_dir),
                "local_filename": "clip_g.safetensors",
                "description": "CLIP-G 文本编码器"
            }
        ]
    elif choice == "3":
        downloads = [
            {
                "repo": "stabilityai/stable-diffusion-3.5-large",
                "filename": "sd3.5_large.safetensors",
                "local_dir": str(checkpoints_dir),
                "description": "SD3.5 Large 主模型"
            },
            {
                "repo": "stabilityai/stable-diffusion-3.5-large",
                "filename": "text_encoders/clip_g.safetensors",
                "local_dir": str(clip_dir),
                "local_filename": "clip_g.safetensors",
                "description": "CLIP-G 文本编码器"
            }
        ]
    else:
        print("❌ 无效选择")
        return
    
    # 开始下载
    for download in downloads:
        print(f"\n开始下载: {download['description']}")
        
        # 构建下载命令
        cmd = f"huggingface-cli download {download['repo']} {download['filename']}"
        cmd += f" --local-dir {download['local_dir']}"
        
        if 'local_filename' in download:
            cmd += f" --local-dir-use-symlinks False"
        
        success = run_command(cmd, f"下载 {download['description']}")
        
        # 如果需要重命名文件
        if success and 'local_filename' in download:
            src_path = Path(download['local_dir']) / download['filename']
            dst_path = Path(download['local_dir']) / download['local_filename']
            
            if src_path.exists():
                try:
                    # 创建目标目录
                    dst_path.parent.mkdir(parents=True, exist_ok=True)
                    # 移动文件
                    src_path.rename(dst_path)
                    print(f"✅ 文件已移动到: {dst_path}")
                    
                    # 清理空目录
                    try:
                        src_path.parent.rmdir()
                    except:
                        pass
                        
                except Exception as e:
                    print(f"⚠️  文件移动失败: {e}")
    
    print("\n🎉 下载完成!")
    print("\n下一步:")
    print("1. 使用 SD3.5 工作流: workflows/sd35_large_best_quality.json")
    print("2. 启动 ComfyUI: python main.py --lowvram --auto-launch")
    print("3. 拖拽工作流到界面开始使用")

if __name__ == "__main__":
    main()
