{"id": "dbedd4b2-c963-475d-8057-72a15e532fd5", "revision": 0, "last_node_id": 56, "last_link_id": 447, "nodes": [{"id": 48, "type": "Reroute", "pos": [1150.56298828125, 378.3611755371094], "size": [75, 26], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 439}], "outputs": [{"name": "", "type": "MODEL", "links": [427, 430]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 52, "type": "InstructPixToPixConditioning", "pos": [1118.1785888671875, 90.97599029541016], "size": [235.1999969482422, 86], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 445}, {"name": "negative", "type": "CONDITIONING", "link": 446}, {"name": "vae", "type": "VAE", "link": 442}, {"name": "pixels", "type": "IMAGE", "link": 443}], "outputs": [{"name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [431]}, {"name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [432]}, {"name": "latent", "type": "LATENT", "slot_index": 2, "links": [438]}], "properties": {"Node name for S&R": "InstructPixToPixConditioning"}, "widgets_values": []}, {"id": 47, "type": "RandomNoise", "pos": [1386.3912353515625, 85.34677124023438], "size": [315, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [434]}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [705826023365990, "randomize"]}, {"id": 53, "type": "DualCFGGuider", "pos": [1387.2498779296875, 215.46206665039062], "size": [315, 142], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 430}, {"name": "cond1", "type": "CONDITIONING", "link": 431}, {"name": "cond2", "type": "CONDITIONING", "link": 432}, {"name": "negative", "type": "CONDITIONING", "link": 447}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "slot_index": 0, "links": [435]}], "properties": {"Node name for S&R": "DualCFGGuider"}, "widgets_values": [5, 2]}, {"id": 46, "type": "KSamplerSelect", "pos": [1396.8743896484375, 405.3129577636719], "size": [315, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [436]}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 49, "type": "BasicScheduler", "pos": [1400.17626953125, 505.5678405761719], "size": [315, 106], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 427}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [437]}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["normal", 28, 1]}, {"id": 54, "type": "SamplerCustomAdvanced", "pos": [1775.568359375, 437.6390075683594], "size": [355.20001220703125, 106], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 434}, {"name": "guider", "type": "GUIDER", "link": 435}, {"name": "sampler", "type": "SAMPLER", "link": 436}, {"name": "sigmas", "type": "SIGMAS", "link": 437}, {"name": "latent_image", "type": "LATENT", "link": 438}], "outputs": [{"name": "output", "type": "LATENT", "slot_index": 0, "links": [444]}, {"name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 8, "type": "VAEDecode", "pos": [1794.017578125, 595.6263427734375], "size": [210, 46], "flags": {"collapsed": true}, "order": 16, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 444}, {"name": "vae", "type": "VAE", "link": 422}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 55, "type": "<PERSON>downNote", "pos": [-138.36375427246094, 22.427885055541992], "size": [369.************, 439.1597900390625], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tutorial](https://docs.comfy.org//tutorials/advanced/hidream-e1) | [教程](https://docs.comfy.org//tutorials/advanced/hidream-e1)\n\n**text_encoders**：\n\n- [clip_l_hidream.safetensors](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/clip_l_hidream.safetensors)\n- [clip_g_hidream.safetensors](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/clip_g_hidream.safetensors)\n- [t5xxl_fp8_e4m3fn_scaled.safetensors](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/t5xxl_fp8_e4m3fn_scaled.safetensors) \n- [llama_3.1_8b_instruct_fp8_scaled.safetensors](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/llama_3.1_8b_instruct_fp8_scaled.safetensors)\n\n**VAE**\n-  [ae.safetensors](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/vae/ae.safetensors)\n\n **diffusion models**\n- [hidream_e1_full_bf16.safetensors](https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/resolve/main/split_files/diffusion_models/hidream_e1_full_bf16.safetensors)\n\nFile saved location\n```\n📂 ComfyUI/\n├── 📂 models/\n│   ├── 📂 text_encoders/\n│   │   ├─── clip_l_hidream.safetensors\n│   │   ├─── clip_g_hidream.safetensors\n│   │   ├─── t5xxl_fp8_e4m3fn_scaled.safetensors\n│   │   └─── llama_3.1_8b_instruct_fp8_scaled.safetensors\n│   └── 📂 vae/\n│   │   └── ae.safetensors\n│   └── 📂 diffusion_models/\n│       └── hidream_e1_full_bf16.safetensors   \n```\n"], "color": "#432", "bgcolor": "#653"}, {"id": 9, "type": "SaveImage", "pos": [1109.441162109375, 692.375244140625], "size": [1045.0439453125, 715.8048706054688], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 56, "type": "Note", "pos": [733.3640747070312, 750.6859741210938], "size": [304.758056640625, 120.64888763427734], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["The image will be scaled to 768*768. Using other dimensions may not yield particularly good results."], "color": "#432", "bgcolor": "#653"}, {"id": 13, "type": "LoadImage", "pos": [262.5469970703125, 552.4728393554688], "size": [428.5234680175781, 602.3904418945312], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [425]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.webp", "image"]}, {"id": 41, "type": "UNETLoader", "pos": [266.0548095703125, 63.377586364746094], "size": [315, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [439]}], "properties": {"Node name for S&R": "UNETLoader", "models": [{"name": "hidream_e1_full_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/resolve/main/split_files/diffusion_models/hidream_e1_full_bf16.safetensors", "directory": "diffusion_models", "hash": "b645503c5d5d583a135d7e77c655237ebc6dc132d49d763759c1dd51683bd1dd", "hash_type": "SHA256"}]}, "widgets_values": ["hidream_e1_full_bf16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 42, "type": "QuadrupleCLIPLoader", "pos": [267.21856689453125, 190.70782470703125], "size": [315, 130], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [420, 421]}], "properties": {"Node name for S&R": "QuadrupleCLIPLoader", "models": [{"name": "clip_g_hidream.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/clip_g_hidream.safetensors", "directory": "text_encoders", "hash": "3771e70e36450e5199f30bad61a53faae85a2e02606974bcda0a6a573c0519d5", "hash_type": "SHA256"}, {"name": "clip_l_hidream.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/clip_l_hidream.safetensors", "directory": "text_encoders", "hash": "706fdb88e22e18177b207837c02f4b86a652abca0302821f2bfa24ac6aea4f71", "hash_type": "SHA256"}, {"name": "t5xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/t5xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders", "hash": "a498f0485dc9536735258018417c3fd7758dc3bccc0a645feaa472b34955557a", "hash_type": "SHA256"}, {"name": "llama_3.1_8b_instruct_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/text_encoders/llama_3.1_8b_instruct_fp8_scaled.safetensors", "directory": "text_encoders", "hash": "9f86897bbeb933ef4fd06297740edb8dd962c94efcd92b373a11460c33765ea6", "hash_type": "SHA256"}]}, "widgets_values": ["clip_g_hidream.safetensors", "clip_l_hidream.safetensors", "t5xxl_fp8_e4m3fn_scaled.safetensors", "llama_3.1_8b_instruct_fp8_scaled.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 43, "type": "VAELoader", "pos": [267.9156188964844, 371.0837707519531], "size": [315, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [422, 442]}], "properties": {"Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/HiDream-I1_ComfyUI/blob/main/split_files/vae/ae.safetensors", "directory": "vae", "hash": "afc8e28272cd15db3919bacdb6918ce9c1ed22e96cb12c4d5ed0fba823529e38", "hash_type": "SHA256"}]}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 7, "type": "CLIPTextEncode", "pos": [634.9760131835938, 275.4197998046875], "size": [414.3643798828125, 155.3189697265625], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 421}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [446, 447]}], "title": "Negative", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#223", "bgcolor": "#335"}, {"id": 44, "type": "ImageScale", "pos": [718.4549560546875, 558.5048217773438], "size": [315, 130], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 425}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [443]}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 768, 768, "center"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [636.9760131835938, 66.41992950439453], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 420}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [445]}], "title": "Positive", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Let the girl put on the VR glasses full of a sense of technology, just like the scenes in Ready Player One, with CG rendering and ultra-realism."], "color": "#232", "bgcolor": "#353"}], "links": [[9, 8, 0, 9, 0, "IMAGE"], [420, 42, 0, 6, 0, "CLIP"], [421, 42, 0, 7, 0, "CLIP"], [422, 43, 0, 8, 1, "VAE"], [425, 13, 0, 44, 0, "IMAGE"], [427, 48, 0, 49, 0, "MODEL"], [430, 48, 0, 53, 0, "MODEL"], [431, 52, 0, 53, 1, "CONDITIONING"], [432, 52, 1, 53, 2, "CONDITIONING"], [434, 47, 0, 54, 0, "NOISE"], [435, 53, 0, 54, 1, "GUIDER"], [436, 46, 0, 54, 2, "SAMPLER"], [437, 49, 0, 54, 3, "SIGMAS"], [438, 52, 2, 54, 4, "LATENT"], [439, 41, 0, 48, 0, "*"], [442, 43, 0, 52, 2, "VAE"], [443, 44, 0, 52, 3, "IMAGE"], [444, 54, 0, 8, 0, "LATENT"], [445, 6, 0, 52, 0, "CONDITIONING"], [446, 7, 0, 52, 1, "CONDITIONING"], [447, 7, 0, 53, 3, "CONDITIONING"]], "groups": [{"id": 1, "title": "IP2PSampler", "bounding": [1092.3253173828125, -4.117127418518066, 1071.************, 634.0662231445312], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Load models here", "bounding": [250, -10.222414016723633, 346.5339050292969, 460.7162780761719], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Prompt", "bounding": [624.9760131835938, -7.1800456047058105, 442.4894104003906, 458.2039794921875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Upload Image", "bounding": [252.54710388183594, 478.************, 816.************, 674.3783569335938], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.4665073802097334, "offset": [722.7298901511426, 539.8385069969991]}, "frontendVersion": "1.17.11", "groupNodes": {"IP2PSampler": {"nodes": [{"type": "KSamplerSelect", "pos": [912, 1536], "size": {"0": 315, "1": 58}, "flags": {}, "order": 6, "mode": 0, "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [], "localized_name": "SAMPLER"}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"], "index": 0, "inputs": [{"name": "sampler_name", "localized_name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "boundingRect": [0, 0, 0, 0], "link": null}]}, {"type": "RandomNoise", "pos": [912, 1200], "size": {"0": 315, "1": 82}, "flags": {}, "order": 7, "mode": 0, "outputs": [{"name": "NOISE", "type": "NOISE", "links": [], "localized_name": "NOISE"}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [156680208700303, "fixed"], "index": 1, "inputs": [{"name": "noise_seed", "localized_name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "boundingRect": [0, 0, 0, 0], "link": null}]}, {"type": "Reroute", "pos": [720, 1488], "size": [75, 26], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "", "type": "*", "link": null}], "outputs": [{"name": "", "type": "*", "links": null}], "properties": {"showOutputText": false, "horizontal": false}, "index": 2}, {"type": "BasicScheduler", "pos": [912, 1632], "size": {"0": 315, "1": 106}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": null, "localized_name": "model"}, {"name": "scheduler", "localized_name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "boundingRect": [0, 0, 0, 0], "link": null}, {"name": "steps", "localized_name": "steps", "type": "INT", "widget": {"name": "steps"}, "boundingRect": [0, 0, 0, 0], "link": null}, {"name": "denoise", "localized_name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "boundingRect": [0, 0, 0, 0], "link": null}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [], "localized_name": "SIGMAS"}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["normal", 20, 1], "index": 3}, {"type": "Reroute", "pos": [575, 1344], "size": [75, 26], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "", "type": "*", "link": null}], "outputs": [{"name": "", "type": "*", "links": null}], "properties": {"showOutputText": false, "horizontal": false}, "index": 4}, {"type": "Reroute", "pos": [570, 1386], "size": [75, 26], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "", "type": "*", "link": null}], "outputs": [{"name": "", "type": "*", "links": null}], "properties": {"showOutputText": false, "horizontal": false}, "index": 5}, {"type": "InstructPixToPixConditioning", "pos": [672, 1344], "size": {"0": 235.1999969482422, "1": 86}, "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": null, "localized_name": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": null, "localized_name": "negative"}, {"name": "vae", "type": "VAE", "link": null, "localized_name": "vae"}, {"name": "pixels", "type": "IMAGE", "link": null, "localized_name": "pixels"}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [], "slot_index": 0, "localized_name": "positive"}, {"name": "negative", "type": "CONDITIONING", "links": [], "slot_index": 1, "localized_name": "negative"}, {"name": "latent", "type": "LATENT", "links": [], "slot_index": 2, "localized_name": "latent"}], "properties": {"Node name for S&R": "InstructPixToPixConditioning"}, "index": 6, "widgets_values": []}, {"type": "DualCFGGuider", "pos": [912, 1344], "size": {"0": 315, "1": 142}, "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": null, "localized_name": "model"}, {"name": "cond1", "type": "CONDITIONING", "link": null, "localized_name": "cond1"}, {"name": "cond2", "type": "CONDITIONING", "link": null, "localized_name": "cond2"}, {"name": "negative", "type": "CONDITIONING", "link": null, "localized_name": "negative"}, {"name": "cfg_conds", "localized_name": "cfg_conds", "type": "FLOAT", "widget": {"name": "cfg_conds"}, "boundingRect": [0, 0, 0, 0], "link": null}, {"name": "cfg_cond2_negative", "localized_name": "cfg_cond2_negative", "type": "FLOAT", "widget": {"name": "cfg_cond2_negative"}, "boundingRect": [0, 0, 0, 0], "link": null}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [], "slot_index": 0, "localized_name": "GUIDER"}], "properties": {"Node name for S&R": "DualCFGGuider"}, "widgets_values": [3, 1.5], "index": 7}, {"type": "SamplerCustomAdvanced", "pos": [1296, 1200], "size": {"0": 355.20001220703125, "1": 106}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": null, "slot_index": 0, "localized_name": "noise"}, {"name": "guider", "type": "GUIDER", "link": null, "slot_index": 1, "localized_name": "guider"}, {"name": "sampler", "type": "SAMPLER", "link": null, "slot_index": 2, "localized_name": "sampler"}, {"name": "sigmas", "type": "SIGMAS", "link": null, "slot_index": 3, "localized_name": "sigmas"}, {"name": "latent_image", "type": "LATENT", "link": null, "slot_index": 4, "localized_name": "latent_image"}], "outputs": [{"name": "output", "type": "LATENT", "links": [], "slot_index": 0, "localized_name": "output"}, {"name": "denoised_output", "type": "LATENT", "links": null, "localized_name": "denoised_output"}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "index": 8, "widgets_values": []}], "links": [[null, 0, 2, 0, 4, "MODEL"], [2, 0, 3, 0, 26, "MODEL"], [null, 0, 5, 0, 7, "CONDITIONING"], [null, 0, 4, 0, 6, "CONDITIONING"], [4, 0, 6, 0, 35, "CONDITIONING"], [5, 0, 6, 1, 29, "CONDITIONING"], [null, 2, 6, 2, 4, "VAE"], [null, 0, 6, 3, 13, "IMAGE"], [2, 0, 7, 0, 26, "MODEL"], [6, 0, 7, 1, 33, "CONDITIONING"], [6, 1, 7, 2, 33, "CONDITIONING"], [5, 0, 7, 3, 29, "CONDITIONING"], [1, 0, 8, 0, 19, "NOISE"], [7, 0, 8, 1, 28, "GUIDER"], [0, 0, 8, 2, 20, "SAMPLER"], [3, 0, 8, 3, 21, "SIGMAS"], [6, 2, 8, 4, 33, "LATENT"]], "external": [[{"type": "SamplerCustomAdvanced", "pos": [1296, 1200], "size": {"0": 355.20001220703125, "1": 106}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": null, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": null, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": null, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": null, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": null, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [], "shape": 3, "slot_index": 0}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "index": 8}, 0, "LATENT"]], "config": {"0": {}, "1": {}, "2": {"input": {"MODEL": {"name": "model"}}}, "3": {}, "4": {"input": {"CONDITIONING": {"name": "positive"}}}, "5": {"input": {"CONDITIONING": {"name": "negative"}}}, "6": {}, "7": {"input": {"cfg_conds": {"name": "cfg_text"}, "cfg_cond2_negative": {"name": "cfg_image"}}}, "8": {}}}}}, "version": 0.4}