# Flux 批量生成图片指南

## 🚀 批量生成方案

我为您创建了三种批量生成10张图片的方案：

### 方案 1: 简单批量生成 ⭐⭐⭐⭐⭐ (推荐)

**文件**: `workflows/flux_batch_generation.json`

**特点**:
- ✅ 一次生成10张图片
- ✅ 使用相同提示词
- ✅ 自动变化种子
- ✅ 最简单易用

**使用方法**:
1. 拖拽 `flux_batch_generation.json` 到 ComfyUI
2. 修改提示词
3. 点击 "Queue Prompt"
4. 等待生成10张图片

### 方案 2: 修改现有工作流

**文件**: `workflows/flux_ultimate_quality.json` (已更新)

**改动**:
- 批量大小从 1 改为 10
- 一次生成10张高质量图片

### 方案 3: 多种子批量生成 ⭐⭐⭐⭐

**文件**: `workflows/flux_multi_seed_batch.json`

**特点**:
- 🎯 使用不同种子
- 🎨 更多样化的结果
- 🔧 可扩展到更多图片

## 📊 方案对比

| 方案 | 图片数量 | 多样性 | 显存需求 | 生成时间 | 推荐度 |
|------|----------|--------|----------|----------|--------|
| 简单批量 | 10张 | 中等 | 高 | 中等 | ⭐⭐⭐⭐⭐ |
| 修改现有 | 10张 | 中等 | 高 | 中等 | ⭐⭐⭐⭐ |
| 多种子 | 可扩展 | 高 | 中等 | 长 | ⭐⭐⭐⭐ |

## 🎯 推荐使用：简单批量生成

### 立即开始：

1. **导入工作流**:
   ```
   拖拽 workflows/flux_batch_generation.json 到 ComfyUI 界面
   ```

2. **调整参数**:
   - 批量大小: 10 (可调整为 5, 15, 20 等)
   - 提示词: 修改为您想要的内容
   - 种子: 42 (固定种子确保可重现)

3. **生成图片**:
   点击 "Queue Prompt" 开始批量生成

## ⚙️ 显存优化建议

### 针对您的 RTX 4070 (8GB)

#### 批量大小建议:
```
1024x1024 分辨率:
- 批量 5张: 安全
- 批量 10张: 可能需要优化
- 批量 15张: 需要 --lowvram

768x768 分辨率:
- 批量 10张: 安全
- 批量 15张: 安全
- 批量 20张: 可能需要优化
```

#### 启动参数优化:
```bash
# 标准批量生成
python main.py --auto-launch

# 如果显存不足
python main.py --lowvram --auto-launch

# 极限批量生成
python main.py --lowvram --cpu-vae --auto-launch
```

## 🔧 自定义批量数量

### 修改批量大小:

在工作流中找到 "Empty Latent Image" 节点，修改 `batch_size`:

```json
"batch_size": 10  // 改为您想要的数量
```

### 推荐批量大小:
- **快速测试**: 3-5张
- **日常使用**: 8-10张
- **大量生成**: 15-20张
- **极限生成**: 25-50张 (需要优化)

## 🎨 批量生成技巧

### 1. 种子控制策略

#### 固定种子 (相似结果):
```json
"seed": 42  // 所有图片基于相同种子
```

#### 随机种子 (多样结果):
```json
"seed": -1  // 每张图片使用随机种子
```

### 2. 提示词优化

#### 批量生成适用的提示词:
```
"a professional portrait, natural lighting, photorealistic, highly detailed, masterpiece"
```

#### 避免过于具体的描述:
```
❌ "a woman with blue eyes wearing a red dress"
✅ "a beautiful woman, professional portrait, natural lighting"
```

### 3. 分辨率选择

#### 平衡质量和速度:
```
快速批量: 768x768
标准批量: 1024x1024
高质量: 1216x832 (人像)
```

## 📁 输出文件管理

### 文件命名规则:
```
Flux_Batch_00001.png
Flux_Batch_00002.png
...
Flux_Batch_00010.png
```

### 自定义文件名前缀:
在 "Save Image" 节点中修改 `filename_prefix`:
```json
"filename_prefix": "MyProject_Batch_"
```

## 🚀 高级批量技巧

### 1. 分批生成策略
如果显存不足，可以分批生成：
- 第一批: 5张
- 第二批: 5张
- 总计: 10张

### 2. 质量与数量平衡
```
高质量少量: 5张 × 30步
平衡模式: 10张 × 25步
快速大量: 20张 × 20步
```

### 3. 监控生成进度
- 观察显存使用情况
- 注意生成时间
- 调整参数优化性能

## 🔍 故障排除

### 1. 显存不足 (CUDA OOM)
**解决方案**:
```bash
# 减少批量大小
batch_size: 10 → 5

# 使用低显存模式
python main.py --lowvram

# 降低分辨率
1024x1024 → 768x768
```

### 2. 生成时间过长
**优化方案**:
```
减少步数: 30 → 20
降低分辨率: 1024 → 768
减少批量: 10 → 5
```

### 3. 结果过于相似
**解决方案**:
```
使用随机种子: seed: -1
调整提示词变化
使用多种子工作流
```

## 🎉 开始批量创作

**推荐流程**:

1. ✅ **导入**: `flux_batch_generation.json`
2. 🎨 **设置**: 修改提示词和批量大小
3. 🚀 **生成**: 点击 "Queue Prompt"
4. 📁 **查看**: 检查 `output/` 目录中的结果
5. 🔄 **优化**: 根据结果调整参数

现在您可以一次生成10张高质量的 Flux 图片了！🎨✨

需要我帮您测试批量生成工作流吗？
