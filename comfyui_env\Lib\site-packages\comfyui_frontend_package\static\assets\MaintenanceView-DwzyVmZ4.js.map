{"version": 3, "file": "MaintenanceView-DwzyVmZ4.js", "sources": ["../../src/components/common/RefreshButton.vue", "../../src/components/maintenance/StatusTag.vue", "../../src/constants/desktopMaintenanceTasks.ts", "../../src/stores/maintenanceTaskStore.ts", "../../src/utils/refUtil.ts", "../../src/components/maintenance/TaskCard.vue", "../../src/components/maintenance/TaskListStatusIcon.vue", "../../src/components/maintenance/TaskListItem.vue", "../../src/components/maintenance/TaskListPanel.vue", "../../src/views/MaintenanceView.vue"], "sourcesContent": ["<!--\n  A refresh button that disables and shows a progress spinner whilst active.\n\n  Usage:\n  ```vue\n    <RefreshButton\n      v-model=\"isRefreshing\"\n      :outlined=\"false\"\n      @refresh=\"refresh\"\n    />\n  ```\n-->\n<template>\n  <Button\n    class=\"relative p-button-icon-only\"\n    :outlined=\"outlined\"\n    :severity=\"severity\"\n    :disabled=\"active || disabled\"\n    @click=\"(event) => $emit('refresh', event)\"\n  >\n    <span\n      class=\"p-button-icon pi pi-refresh transition-all\"\n      :class=\"{ 'opacity-0': active }\"\n      data-pc-section=\"icon\"\n    />\n    <span class=\"p-button-label\" data-pc-section=\"label\">&nbsp;</span>\n    <ProgressSpinner v-show=\"active\" class=\"absolute w-1/2 h-1/2\" />\n  </Button>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport ProgressSpinner from 'primevue/progressspinner'\n\nimport { PrimeVueSeverity } from '@/types/primeVueTypes'\n\nconst {\n  disabled,\n  outlined = true,\n  severity = 'secondary'\n} = defineProps<{\n  disabled?: boolean\n  outlined?: boolean\n  severity?: PrimeVueSeverity\n}>()\n\n// Model\nconst active = defineModel<boolean>({ required: true })\n\n// Emits\ndefineEmits(['refresh'])\n</script>\n", "<template>\n  <Tag :icon :severity :value />\n</template>\n\n<script setup lang=\"ts\">\nimport { PrimeIcons } from '@primevue/core/api'\nimport Tag from 'primevue/tag'\nimport { computed } from 'vue'\n\nimport { t } from '@/i18n'\n\n// Properties\nconst props = defineProps<{\n  error: boolean\n  refreshing?: boolean\n}>()\n\n// Bindings\nconst icon = computed(() => {\n  if (props.refreshing) return PrimeIcons.QUESTION\n  if (props.error) return PrimeIcons.TIMES\n  return PrimeIcons.CHECK\n})\n\nconst severity = computed(() => {\n  if (props.refreshing) return 'info'\n  if (props.error) return 'danger'\n  return 'success'\n})\n\nconst value = computed(() => {\n  if (props.refreshing) return t('maintenance.refreshing')\n  if (props.error) return t('g.error')\n  return t('maintenance.OK')\n})\n</script>\n", "import { PrimeIcons } from '@primevue/core'\n\nimport type { MaintenanceTask } from '@/types/desktop/maintenanceTypes'\nimport { electronAPI } from '@/utils/envUtil'\n\nconst electron = electronAPI()\n\nconst openUrl = (url: string) => {\n  window.open(url, '_blank')\n  return true\n}\n\nexport const DESKTOP_MAINTENANCE_TASKS: Readonly<MaintenanceTask>[] = [\n  {\n    id: 'basePath',\n    execute: async () => await electron.setBasePath(),\n    name: 'Base path',\n    shortDescription: 'Change the application base path.',\n    errorDescription: 'Unable to open the base path.  Please select a new one.',\n    description:\n      'The base path is the default location where ComfyUI stores data. It is the location for the python environment, and may also contain models, custom nodes, and other extensions.',\n    isInstallationFix: true,\n    button: {\n      icon: PrimeIcons.QUESTION,\n      text: 'Select'\n    }\n  },\n  {\n    id: 'git',\n    headerImg: 'assets/images/Git-Logo-White.svg',\n    execute: () => openUrl('https://git-scm.com/downloads/'),\n    name: 'Download git',\n    shortDescription: 'Open the git download page.',\n    errorDescription:\n      'Git is missing. Please download and install git, then restart ComfyUI Desktop.',\n    description:\n      'Git is required to download and manage custom nodes and other extensions. This task opens the download page in your default browser, where you can download the latest version of git. Once you have installed git, please restart ComfyUI Desktop.',\n    button: {\n      icon: PrimeIcons.EXTERNAL_LINK,\n      text: 'Download'\n    }\n  },\n  {\n    id: 'vcRedist',\n    execute: () => openUrl('https://aka.ms/vs/17/release/vc_redist.x64.exe'),\n    name: 'Download VC++ Redist',\n    shortDescription: 'Download the latest VC++ Redistributable runtime.',\n    description:\n      'The Visual C++ runtime libraries are required to run ComfyUI. You will need to download and install this file.',\n    button: {\n      icon: PrimeIcons.EXTERNAL_LINK,\n      text: 'Download'\n    }\n  },\n  {\n    id: 'reinstall',\n    severity: 'danger',\n    requireConfirm: true,\n    execute: async () => {\n      await electron.reinstall()\n      return true\n    },\n    name: 'Reinstall ComfyUI',\n    shortDescription:\n      'Deletes the desktop app config and load the welcome screen.',\n    description:\n      'Delete the desktop app config, restart the app, and load the installation screen.',\n    confirmText: 'Delete all saved config and reinstall?',\n    button: {\n      icon: PrimeIcons.EXCLAMATION_TRIANGLE,\n      text: 'Reinstall'\n    }\n  },\n  {\n    id: 'pythonPackages',\n    requireConfirm: true,\n    execute: async () => {\n      try {\n        await electron.uv.installRequirements()\n        return true\n      } catch (error) {\n        return false\n      }\n    },\n    name: 'Install python packages',\n    shortDescription:\n      'Installs the base python packages required to run ComfyUI.',\n    errorDescription:\n      'Python packages that are required to run ComfyUI are not installed.',\n    description:\n      'This will install the python packages required to run ComfyUI. This includes torch, torchvision, and other dependencies.',\n    usesTerminal: true,\n    isInstallationFix: true,\n    button: {\n      icon: PrimeIcons.DOWNLOAD,\n      text: 'Install'\n    }\n  },\n  {\n    id: 'uv',\n    execute: () =>\n      openUrl('https://docs.astral.sh/uv/getting-started/installation/'),\n    name: 'uv executable',\n    shortDescription: 'uv installs and maintains the python environment.',\n    description:\n      \"This will open the download page for Astral's uv tool. uv is used to install python and manage python packages.\",\n    button: {\n      icon: 'pi pi-asterisk',\n      text: 'Download'\n    }\n  },\n  {\n    id: 'uvCache',\n    severity: 'danger',\n    requireConfirm: true,\n    execute: async () => await electron.uv.clearCache(),\n    name: 'uv cache',\n    shortDescription: 'Remove the Astral uv cache of python packages.',\n    description:\n      'This will remove the uv cache directory and its contents. All downloaded python packages will need to be downloaded again.',\n    confirmText: 'Delete uv cache of python packages?',\n    usesTerminal: true,\n    isInstallationFix: true,\n    button: {\n      icon: PrimeIcons.TRASH,\n      text: 'Clear cache'\n    }\n  },\n  {\n    id: 'venvDirectory',\n    severity: 'danger',\n    requireConfirm: true,\n    execute: async () => await electron.uv.resetVenv(),\n    name: 'Reset virtual environment',\n    shortDescription:\n      'Remove and recreate the .venv directory. This removes all python packages.',\n    description:\n      'The python environment is where ComfyUI installs python and python packages. It is used to run the ComfyUI server.',\n    confirmText: 'Delete the .venv directory?',\n    usesTerminal: true,\n    isInstallationFix: true,\n    button: {\n      icon: PrimeIcons.FOLDER,\n      text: 'Recreate'\n    }\n  }\n] as const\n", "import type { InstallValidation } from '@comfyorg/comfyui-electron-types'\nimport { defineStore } from 'pinia'\nimport { computed, ref } from 'vue'\n\nimport { DESKTOP_MAINTENANCE_TASKS } from '@/constants/desktopMaintenanceTasks'\nimport type { MaintenanceTask } from '@/types/desktop/maintenanceTypes'\nimport { electronAPI } from '@/utils/envUtil'\n\n/** State of a maintenance task, managed by the maintenance task store. */\ntype MaintenanceTaskState = 'warning' | 'error' | 'OK' | 'skipped'\n\n// Type not exported by API\ntype ValidationState = InstallValidation['basePath']\n// Add index to API type\ntype IndexedUpdate = InstallValidation & Record<string, ValidationState>\n\n/** State of a maintenance task, managed by the maintenance task store. */\nexport class MaintenanceTaskRunner {\n  constructor(readonly task: MaintenanceTask) {}\n\n  private _state?: MaintenanceTaskState\n  /** The current state of the task. Setter also controls {@link resolved} as a side-effect. */\n  get state() {\n    return this._state\n  }\n\n  /** Updates the task state and {@link resolved} status. */\n  setState(value: MaintenanceTaskState) {\n    // Mark resolved\n    if (this._state === 'error' && value === 'OK') this.resolved = true\n    // Mark unresolved (if previously resolved)\n    if (value === 'error') this.resolved &&= false\n\n    this._state = value\n  }\n\n  /** `true` if the task has been resolved (was `error`, now `OK`). This is a side-effect of the {@link state} setter. */\n  resolved?: boolean\n\n  /** Whether the task state is currently being refreshed. */\n  refreshing?: boolean\n  /** Whether the task is currently running. */\n  executing?: boolean\n  /** The error message that occurred when the task failed. */\n  error?: string\n\n  update(update: IndexedUpdate) {\n    const state = update[this.task.id]\n\n    this.refreshing = state === undefined\n    if (state) this.setState(state)\n  }\n\n  finaliseUpdate(update: IndexedUpdate) {\n    this.refreshing = false\n    this.setState(update[this.task.id] ?? 'skipped')\n  }\n\n  /** Wraps the execution of a maintenance task, updating state and rethrowing errors. */\n  async execute(task: MaintenanceTask) {\n    try {\n      this.executing = true\n      const success = await task.execute()\n      if (!success) return false\n\n      this.error = undefined\n      return true\n    } catch (error) {\n      this.error = (error as Error)?.message\n      throw error\n    } finally {\n      this.executing = false\n    }\n  }\n}\n\n/**\n * User-initiated maintenance tasks.  Currently only used by the desktop app maintenance view.\n *\n * Includes running state, task list, and execution / refresh logic.\n * @returns The maintenance task store\n */\nexport const useMaintenanceTaskStore = defineStore('maintenanceTask', () => {\n  /** Refresh should run for at least this long, even if it completes much faster. Ensures refresh feels like it is doing something. */\n  const electron = electronAPI()\n\n  // Reactive state\n  const isRefreshing = ref(false)\n  const isRunningTerminalCommand = computed(() =>\n    tasks.value\n      .filter((task) => task.usesTerminal)\n      .some((task) => getRunner(task)?.executing)\n  )\n  const isRunningInstallationFix = computed(() =>\n    tasks.value\n      .filter((task) => task.isInstallationFix)\n      .some((task) => getRunner(task)?.executing)\n  )\n\n  // Task list\n  const tasks = ref(DESKTOP_MAINTENANCE_TASKS)\n\n  const taskRunners = ref(\n    new Map<MaintenanceTask['id'], MaintenanceTaskRunner>(\n      DESKTOP_MAINTENANCE_TASKS.map((x) => [x.id, new MaintenanceTaskRunner(x)])\n    )\n  )\n\n  /** True if any tasks are in an error state. */\n  const anyErrors = computed(() =>\n    tasks.value.some((task) => getRunner(task).state === 'error')\n  )\n\n  /**\n   * Returns the matching state object for a task.\n   * @param task Task to get the matching state object for\n   * @returns The state object for this task\n   */\n  const getRunner = (task: MaintenanceTask) => taskRunners.value.get(task.id)!\n\n  /**\n   * Updates the task list with the latest validation state.\n   * @param validationUpdate Update details passed in by electron\n   */\n  const processUpdate = (validationUpdate: InstallValidation) => {\n    const update = validationUpdate as IndexedUpdate\n    isRefreshing.value = true\n\n    // Update each task state\n    for (const task of tasks.value) {\n      getRunner(task).update(update)\n    }\n\n    // Final update\n    if (!update.inProgress && isRefreshing.value) {\n      isRefreshing.value = false\n\n      for (const task of tasks.value) {\n        getRunner(task).finaliseUpdate(update)\n      }\n    }\n  }\n\n  /** Clears the resolved status of tasks (when changing filters) */\n  const clearResolved = () => {\n    for (const task of tasks.value) {\n      getRunner(task).resolved &&= false\n    }\n  }\n\n  /** @todo Refreshes Electron tasks only. */\n  const refreshDesktopTasks = async () => {\n    isRefreshing.value = true\n    console.log('Refreshing desktop tasks')\n    await electron.Validation.validateInstallation(processUpdate)\n  }\n\n  const execute = async (task: MaintenanceTask) => {\n    return getRunner(task).execute(task)\n  }\n\n  return {\n    tasks,\n    isRefreshing,\n    isRunningTerminalCommand,\n    isRunningInstallationFix,\n    execute,\n    getRunner,\n    processUpdate,\n    clearResolved,\n    /** True if any tasks are in an error state. */\n    anyErrors,\n    refreshDesktopTasks\n  }\n})\n", "import { useTimeout } from '@vueuse/core'\nimport { type Ref, computed, ref, watch } from 'vue'\n\n/**\n * Vue boolean ref (writable computed) with one difference: when set to `true` it stays that way for at least {@link minDuration}.\n * If set to `false` before {@link minDuration} has passed, it uses a timer to delay the change.\n * @param value The default value to set on this ref\n * @param minDuration The minimum time that this ref must be `true` for\n * @returns A custom boolean vue ref with a minimum activation time\n */\nexport function useMinLoadingDurationRef(\n  value: Ref<boolean>,\n  minDuration = 250\n) {\n  const current = ref(value.value)\n\n  const { ready, start } = useTimeout(minDuration, {\n    controls: true,\n    immediate: false\n  })\n\n  watch(value, (newValue) => {\n    if (newValue && !current.value) start()\n\n    current.value = newValue\n  })\n\n  return computed(() => current.value || !ready.value)\n}\n", "<template>\n  <div\n    class=\"task-div max-w-48 min-h-52 grid relative\"\n    :class=\"{ 'opacity-75': isLoading }\"\n  >\n    <Card\n      class=\"max-w-48 relative h-full overflow-hidden\"\n      :class=\"{ 'opacity-65': runner.state !== 'error' }\"\n      v-bind=\"(({ onClick, ...rest }) => rest)($attrs)\"\n    >\n      <template #header>\n        <i\n          v-if=\"runner.state === 'error'\"\n          class=\"pi pi-exclamation-triangle text-red-500 absolute m-2 top-0 -right-14 opacity-15\"\n          style=\"font-size: 10rem\"\n        />\n        <img\n          v-if=\"task.headerImg\"\n          :src=\"task.headerImg\"\n          class=\"object-contain w-full h-full opacity-25 pt-4 px-4\"\n        />\n      </template>\n      <template #title>\n        {{ task.name }}\n      </template>\n      <template #content>\n        {{ description }}\n      </template>\n      <template #footer>\n        <div class=\"flex gap-4 mt-1\">\n          <Button\n            :icon=\"task.button?.icon\"\n            :label=\"task.button?.text\"\n            class=\"w-full\"\n            raised\n            icon-pos=\"right\"\n            :loading=\"isExecuting\"\n            @click=\"(event) => $emit('execute', event)\"\n          />\n        </div>\n      </template>\n    </Card>\n\n    <i\n      v-if=\"!isLoading && runner.state === 'OK'\"\n      class=\"task-card-ok pi pi-check\"\n    />\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport Card from 'primevue/card'\nimport { computed } from 'vue'\n\nimport { useMaintenanceTaskStore } from '@/stores/maintenanceTaskStore'\nimport type { MaintenanceTask } from '@/types/desktop/maintenanceTypes'\nimport { useMinLoadingDurationRef } from '@/utils/refUtil'\n\nconst taskStore = useMaintenanceTaskStore()\nconst runner = computed(() => taskStore.getRunner(props.task))\n\n// Properties\nconst props = defineProps<{\n  task: MaintenanceTask\n}>()\n\n// Events\ndefineEmits<{\n  execute: [event: MouseEvent]\n}>()\n\n// Bindings\nconst description = computed(() =>\n  runner.value.state === 'error'\n    ? props.task.errorDescription ?? props.task.shortDescription\n    : props.task.shortDescription\n)\n\n// Use a minimum run time to ensure tasks \"feel\" like they have run\nconst reactiveLoading = computed(() => !!runner.value.refreshing)\nconst reactiveExecuting = computed(() => !!runner.value.executing)\n\nconst isLoading = useMinLoadingDurationRef(reactiveLoading, 250)\nconst isExecuting = useMinLoadingDurationRef(reactiveExecuting, 250)\n</script>\n\n<style scoped>\n.task-card-ok {\n  @apply text-green-500 absolute -right-4 -bottom-4 opacity-100 row-span-full col-span-full transition-opacity;\n\n  font-size: 4rem;\n  text-shadow: 0.25rem 0 0.5rem black;\n  z-index: 10;\n}\n\n.p-card {\n  @apply transition-opacity;\n\n  --p-card-background: var(--p-button-secondary-background);\n  opacity: 0.9;\n\n  &.opacity-65 {\n    opacity: 0.4;\n  }\n\n  &:hover {\n    opacity: 1;\n  }\n}\n\n:deep(.p-card-header) {\n  z-index: 0;\n}\n\n:deep(.p-card-body) {\n  z-index: 1;\n  flex-grow: 1;\n  justify-content: space-between;\n}\n\n.task-div {\n  > i {\n    pointer-events: none;\n  }\n\n  &:hover > i {\n    opacity: 0.2;\n  }\n}\n</style>\n", "<template>\n  <ProgressSpinner v-if=\"!state || loading\" class=\"h-8 w-8\" />\n  <template v-else>\n    <i v-tooltip.top=\"{ value: tooltip, showDelay: 250 }\" :class=\"cssClasses\" />\n  </template>\n</template>\n\n<script setup lang=\"ts\">\nimport { PrimeIcons } from '@primevue/core/api'\nimport ProgressSpinner from 'primevue/progressspinner'\nimport { MaybeRef, computed } from 'vue'\n\nimport { t } from '@/i18n'\n\n// Properties\nconst tooltip = computed(() => {\n  if (props.state === 'error') {\n    return t('g.error')\n  } else if (props.state === 'OK') {\n    return t('maintenance.OK')\n  } else {\n    return t('maintenance.Skipped')\n  }\n})\n\nconst cssClasses = computed(() => {\n  let classes: string\n  if (props.state === 'error') {\n    classes = `${PrimeIcons.EXCLAMATION_TRIANGLE} text-red-500`\n  } else if (props.state === 'OK') {\n    classes = `${PrimeIcons.CHECK} text-green-500`\n  } else {\n    classes = PrimeIcons.MINUS\n  }\n\n  return `text-3xl pi ${classes}`\n})\n\n// Model\nconst props = defineProps<{\n  state: 'warning' | 'error' | 'resolved' | 'OK' | 'skipped' | undefined\n  loading?: MaybeRef<boolean>\n}>()\n</script>\n", "<template>\n  <tr\n    class=\"border-neutral-700 border-solid border-y\"\n    :class=\"{\n      'opacity-50': runner.resolved,\n      'opacity-75': isLoading && runner.resolved\n    }\"\n  >\n    <td class=\"text-center w-16\">\n      <TaskListStatusIcon :state=\"runner.state\" :loading=\"isLoading\" />\n    </td>\n    <td>\n      <p class=\"inline-block\">\n        {{ task.name }}\n      </p>\n      <Button\n        class=\"inline-block mx-2\"\n        type=\"button\"\n        :icon=\"PrimeIcons.INFO_CIRCLE\"\n        severity=\"secondary\"\n        :text=\"true\"\n        @click=\"toggle\"\n      />\n\n      <Popover ref=\"infoPopover\" class=\"block m-1 max-w-64 min-w-32\">\n        <span class=\"whitespace-pre-line\">{{ task.description }}</span>\n      </Popover>\n    </td>\n    <td class=\"text-right px-4\">\n      <Button\n        :icon=\"task.button?.icon\"\n        :label=\"task.button?.text\"\n        :severity\n        icon-pos=\"right\"\n        :loading=\"isExecuting\"\n        @click=\"(event) => $emit('execute', event)\"\n      />\n    </td>\n  </tr>\n</template>\n\n<script setup lang=\"ts\">\nimport { PrimeIcons } from '@primevue/core/api'\nimport Button from 'primevue/button'\nimport Popover from 'primevue/popover'\nimport { computed, ref } from 'vue'\n\nimport { useMaintenanceTaskStore } from '@/stores/maintenanceTaskStore'\nimport type { MaintenanceTask } from '@/types/desktop/maintenanceTypes'\nimport { PrimeVueSeverity } from '@/types/primeVueTypes'\nimport { useMinLoadingDurationRef } from '@/utils/refUtil'\n\nimport TaskListStatusIcon from './TaskListStatusIcon.vue'\n\nconst taskStore = useMaintenanceTaskStore()\nconst runner = computed(() => taskStore.getRunner(props.task))\n\n// Properties\nconst props = defineProps<{\n  task: MaintenanceTask\n}>()\n\n// Events\ndefineEmits<{\n  execute: [event: MouseEvent]\n}>()\n\n// Binding\nconst severity = computed<PrimeVueSeverity>(() =>\n  runner.value.state === 'error' || runner.value.state === 'warning'\n    ? 'primary'\n    : 'secondary'\n)\n\n// Use a minimum run time to ensure tasks \"feel\" like they have run\nconst reactiveLoading = computed(() => !!runner.value.refreshing)\nconst reactiveExecuting = computed(() => !!runner.value.executing)\n\nconst isLoading = useMinLoadingDurationRef(reactiveLoading, 250)\nconst isExecuting = useMinLoadingDurationRef(reactiveExecuting, 250)\n\n// Popover\nconst infoPopover = ref<InstanceType<typeof Popover> | null>(null)\n\nconst toggle = (event: Event) => {\n  infoPopover.value?.toggle(event)\n}\n</script>\n", "<template>\n  <!-- Tasks -->\n  <section class=\"my-4\">\n    <template v-if=\"filter.tasks.length === 0\">\n      <!-- Empty filter -->\n      <Divider />\n      <p class=\"text-neutral-400 w-full text-center\">\n        {{ $t('maintenance.allOk') }}\n      </p>\n    </template>\n\n    <template v-else>\n      <!-- Display: List -->\n      <table\n        v-if=\"displayAsList === PrimeIcons.LIST\"\n        class=\"w-full border-collapse border-hidden\"\n      >\n        <TaskListItem\n          v-for=\"task in filter.tasks\"\n          :key=\"task.id\"\n          :task\n          @execute=\"(event) => confirmButton(event, task)\"\n        />\n      </table>\n\n      <!-- Display: Cards -->\n      <template v-else>\n        <div class=\"flex flex-wrap justify-evenly gap-8 pad-y my-4\">\n          <TaskCard\n            v-for=\"task in filter.tasks\"\n            :key=\"task.id\"\n            :task\n            @execute=\"(event) => confirmButton(event, task)\"\n          />\n        </div>\n      </template>\n    </template>\n    <ConfirmPopup />\n  </section>\n</template>\n\n<script setup lang=\"ts\">\nimport { PrimeIcons } from '@primevue/core/api'\nimport { useConfirm, useToast } from 'primevue'\nimport ConfirmPopup from 'primevue/confirmpopup'\nimport Divider from 'primevue/divider'\n\nimport { t } from '@/i18n'\nimport { useMaintenanceTaskStore } from '@/stores/maintenanceTaskStore'\nimport type {\n  MaintenanceFilter,\n  MaintenanceTask\n} from '@/types/desktop/maintenanceTypes'\n\nimport TaskCard from './TaskCard.vue'\nimport TaskListItem from './TaskListItem.vue'\n\nconst toast = useToast()\nconst confirm = useConfirm()\nconst taskStore = useMaintenanceTaskStore()\n\n// Properties\ndefineProps<{\n  displayAsList: string\n  filter: MaintenanceFilter\n  isRefreshing: boolean\n}>()\n\nconst executeTask = async (task: MaintenanceTask) => {\n  let message: string | undefined\n\n  try {\n    // Success\n    if ((await taskStore.execute(task)) === true) return\n\n    message = t('maintenance.error.taskFailed')\n  } catch (error) {\n    message = (error as Error)?.message\n  }\n\n  toast.add({\n    severity: 'error',\n    summary: t('maintenance.error.toastTitle'),\n    detail: message ?? t('maintenance.error.defaultDescription'),\n    life: 10_000\n  })\n}\n\n// Commands\nconst confirmButton = async (event: MouseEvent, task: MaintenanceTask) => {\n  if (!task.requireConfirm) {\n    await executeTask(task)\n    return\n  }\n\n  confirm.require({\n    target: event.currentTarget as HTMLElement,\n    message: task.confirmText ?? t('maintenance.confirmTitle'),\n    icon: 'pi pi-exclamation-circle',\n    rejectProps: {\n      label: t('g.cancel'),\n      severity: 'secondary',\n      outlined: true\n    },\n    acceptProps: {\n      label: task.button?.text ?? t('g.save'),\n      severity: task.severity ?? 'primary'\n    },\n    // TODO: Not awaited.\n    accept: async () => {\n      await executeTask(task)\n    }\n  })\n}\n</script>\n", "<template>\n  <BaseViewTemplate dark>\n    <div\n      class=\"min-w-full min-h-full font-sans w-screen h-screen grid justify-around text-neutral-300 bg-neutral-900 dark-theme overflow-y-auto\"\n    >\n      <div class=\"max-w-screen-sm w-screen m-8 relative\">\n        <!-- Header -->\n        <h1 class=\"backspan pi-wrench text-4xl font-bold\">\n          {{ t('maintenance.title') }}\n        </h1>\n\n        <!-- Toolbar -->\n        <div class=\"w-full flex flex-wrap gap-4 items-center\">\n          <span class=\"grow\">\n            {{ t('maintenance.status') }}:\n            <StatusTag :refreshing=\"isRefreshing\" :error=\"anyErrors\" />\n          </span>\n          <div class=\"flex gap-4 items-center\">\n            <SelectButton\n              v-model=\"displayAsList\"\n              :options=\"[PrimeIcons.LIST, PrimeIcons.TH_LARGE]\"\n              :allow-empty=\"false\"\n            >\n              <template #option=\"opts\">\n                <i :class=\"opts.option\" />\n              </template>\n            </SelectButton>\n            <SelectButton\n              v-model=\"filter\"\n              :options=\"filterOptions\"\n              :allow-empty=\"false\"\n              option-label=\"value\"\n              data-key=\"value\"\n              area-labelledby=\"custom\"\n              @change=\"clearResolved\"\n            >\n              <template #option=\"opts\">\n                <i :class=\"opts.option.icon\" />\n                <span class=\"max-sm:hidden\">{{ opts.option.value }}</span>\n              </template>\n            </SelectButton>\n            <RefreshButton\n              v-model=\"isRefreshing\"\n              severity=\"secondary\"\n              @refresh=\"refreshDesktopTasks\"\n            />\n          </div>\n        </div>\n\n        <!-- Tasks -->\n        <TaskListPanel\n          class=\"border-neutral-700 border-solid border-x-0 border-y\"\n          :filter\n          :display-as-list\n          :is-refreshing\n        />\n\n        <!-- Actions -->\n        <div class=\"flex justify-between gap-4 flex-row\">\n          <Button\n            :label=\"t('maintenance.consoleLogs')\"\n            icon=\"pi pi-desktop\"\n            icon-pos=\"left\"\n            severity=\"secondary\"\n            @click=\"toggleConsoleDrawer\"\n          />\n          <Button\n            :label=\"t('g.continue')\"\n            icon=\"pi pi-arrow-right\"\n            icon-pos=\"left\"\n            :severity=\"anyErrors ? 'secondary' : 'primary'\"\n            :loading=\"isRefreshing\"\n            @click=\"() => completeValidation()\"\n          />\n        </div>\n      </div>\n\n      <TerminalOutputDrawer\n        v-model=\"terminalVisible\"\n        :header=\"t('g.terminal')\"\n        :default-message=\"t('maintenance.terminalDefaultMessage')\"\n      />\n      <Toast />\n    </div>\n  </BaseViewTemplate>\n</template>\n\n<script setup lang=\"ts\">\nimport { PrimeIcons } from '@primevue/core/api'\nimport Button from 'primevue/button'\nimport SelectButton from 'primevue/selectbutton'\nimport Toast from 'primevue/toast'\nimport { useToast } from 'primevue/usetoast'\nimport { computed, onMounted, onUnmounted, ref } from 'vue'\nimport { watch } from 'vue'\n\nimport RefreshButton from '@/components/common/RefreshButton.vue'\nimport StatusTag from '@/components/maintenance/StatusTag.vue'\nimport TaskListPanel from '@/components/maintenance/TaskListPanel.vue'\nimport TerminalOutputDrawer from '@/components/maintenance/TerminalOutputDrawer.vue'\nimport { t } from '@/i18n'\nimport { useMaintenanceTaskStore } from '@/stores/maintenanceTaskStore'\nimport { MaintenanceFilter } from '@/types/desktop/maintenanceTypes'\nimport { electronAPI } from '@/utils/envUtil'\nimport { useMinLoadingDurationRef } from '@/utils/refUtil'\n\nimport BaseViewTemplate from './templates/BaseViewTemplate.vue'\n\nconst electron = electronAPI()\nconst toast = useToast()\nconst taskStore = useMaintenanceTaskStore()\nconst { clearResolved, processUpdate, refreshDesktopTasks } = taskStore\n\nconst terminalVisible = ref(false)\n\n// Use a minimum run time to ensure tasks \"feel\" like they have run\nconst reactiveIsRefreshing = computed(() => taskStore.isRefreshing)\n/** `true` when waiting on tasks to complete. */\nconst isRefreshing = useMinLoadingDurationRef(reactiveIsRefreshing, 250)\n\n/** True if any tasks are in an error state. */\nconst anyErrors = computed(() => taskStore.anyErrors)\n\n/** Whether to display tasks as a list or cards. */\nconst displayAsList = ref(PrimeIcons.TH_LARGE)\n\nconst errorFilter = computed(() =>\n  taskStore.tasks.filter((x) => {\n    const { state, resolved } = taskStore.getRunner(x)\n    return state === 'error' || resolved\n  })\n)\n\nconst filterOptions = ref([\n  { icon: PrimeIcons.FILTER_FILL, value: 'All', tasks: taskStore.tasks },\n  { icon: PrimeIcons.EXCLAMATION_TRIANGLE, value: 'Errors', tasks: errorFilter }\n])\n\n/** Filter binding; can be set to show all tasks, or only errors. */\nconst filter = ref<MaintenanceFilter>(filterOptions.value[0])\n\n/** If valid, leave the validation window. */\nconst completeValidation = async () => {\n  const isValid = await electron.Validation.complete()\n  if (!isValid) {\n    toast.add({\n      severity: 'error',\n      summary: t('g.error'),\n      detail: t('maintenance.error.cannotContinue'),\n      life: 5_000\n    })\n  }\n}\n\nconst toggleConsoleDrawer = () => {\n  terminalVisible.value = !terminalVisible.value\n}\n\n// Show terminal when in use\nwatch(\n  () => taskStore.isRunningTerminalCommand,\n  (value) => {\n    terminalVisible.value = value\n  }\n)\n\nonMounted(async () => {\n  electron.Validation.onUpdate(processUpdate)\n\n  const update = await electron.Validation.getStatus()\n  if (Object.values(update).some((x) => x === 'error')) {\n    filter.value = filterOptions.value[1]\n  }\n  processUpdate(update)\n})\n\nonUnmounted(() => electron.Validation.dispose())\n</script>\n\n<style scoped>\n:deep(.p-tag) {\n  --p-tag-gap: 0.375rem;\n}\n\n.backspan::before {\n  @apply m-0 absolute text-muted;\n  font-family: 'primeicons';\n  top: -2rem;\n  right: -2rem;\n  speak: none;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n  display: inline-block;\n  -webkit-font-smoothing: antialiased;\n  opacity: 0.02;\n  font-size: min(14rem, 90vw);\n  z-index: 0;\n}\n</style>\n"], "names": ["_useModel", "electron"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA+CM,UAAA,SAASA,SAAuC,SAAA,YAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnCtD,UAAM,QAAQ;AAMR,UAAA,OAAO,SAAS,MAAM;AACtB,UAAA,MAAM,WAAY,QAAO,WAAW;AACpC,UAAA,MAAM,MAAO,QAAO,WAAW;AACnC,aAAO,WAAW;AAAA,IAAA,CACnB;AAEK,UAAA,WAAW,SAAS,MAAM;AAC1B,UAAA,MAAM,WAAmB,QAAA;AACzB,UAAA,MAAM,MAAc,QAAA;AACjB,aAAA;AAAA,IAAA,CACR;AAEK,UAAA,QAAQ,SAAS,MAAM;AAC3B,UAAI,MAAM,WAAmB,QAAA,EAAE,wBAAwB;AACvD,UAAI,MAAM,MAAc,QAAA,EAAE,SAAS;AACnC,aAAO,EAAE,gBAAgB;AAAA,IAAA,CAC1B;;;;;;;;;;AC7BD,MAAM,WAAW,YAAY;AAE7B,MAAM,UAAU,wBAAC,QAAgB;AACxB,SAAA,KAAK,KAAK,QAAQ;AAClB,SAAA;AACT,GAHgB;AAKT,MAAM,4BAAyD;AAAA,EACpE;AAAA,IACE,IAAI;AAAA,IACJ,SAAS,mCAAY,MAAM,SAAS,YAAY,GAAvC;AAAA,IACT,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,aACE;AAAA,IACF,mBAAmB;AAAA,IACnB,QAAQ;AAAA,MACN,MAAM,WAAW;AAAA,MACjB,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,WAAW;AAAA,IACX,SAAS,6BAAM,QAAQ,gCAAgC,GAA9C;AAAA,IACT,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,kBACE;AAAA,IACF,aACE;AAAA,IACF,QAAQ;AAAA,MACN,MAAM,WAAW;AAAA,MACjB,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,SAAS,6BAAM,QAAQ,gDAAgD,GAA9D;AAAA,IACT,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,aACE;AAAA,IACF,QAAQ;AAAA,MACN,MAAM,WAAW;AAAA,MACjB,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS,mCAAY;AACnB,YAAM,SAAS;AACR,aAAA;AAAA,IACT,GAHS;AAAA,IAIT,MAAM;AAAA,IACN,kBACE;AAAA,IACF,aACE;AAAA,IACF,aAAa;AAAA,IACb,QAAQ;AAAA,MACN,MAAM,WAAW;AAAA,MACjB,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,gBAAgB;AAAA,IAChB,SAAS,mCAAY;AACf,UAAA;AACI,cAAA,SAAS,GAAG;AACX,eAAA;AAAA,eACA,OAAO;AACP,eAAA;AAAA,MACT;AAAA,IACF,GAPS;AAAA,IAQT,MAAM;AAAA,IACN,kBACE;AAAA,IACF,kBACE;AAAA,IACF,aACE;AAAA,IACF,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,QAAQ;AAAA,MACN,MAAM,WAAW;AAAA,MACjB,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,SAAS,6BACP,QAAQ,yDAAyD,GAD1D;AAAA,IAET,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,aACE;AAAA,IACF,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS,mCAAY,MAAM,SAAS,GAAG,WAAW,GAAzC;AAAA,IACT,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,aACE;AAAA,IACF,aAAa;AAAA,IACb,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,QAAQ;AAAA,MACN,MAAM,WAAW;AAAA,MACjB,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA;AAAA,IACE,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,SAAS,mCAAY,MAAM,SAAS,GAAG,UAAU,GAAxC;AAAA,IACT,MAAM;AAAA,IACN,kBACE;AAAA,IACF,aACE;AAAA,IACF,aAAa;AAAA,IACb,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,QAAQ;AAAA,MACN,MAAM,WAAW;AAAA,MACjB,MAAM;AAAA,IACR;AAAA,EACF;AACF;ACjIO,MAAM,sBAAsB;AAAA,SAAA;AAAA;AAAA;AAAA,EACjC,YAAqB,MAAuB;AAAvB,SAAA,OAAA;AAAA,EAAwB;AAAA,EAErC;AAAA;AAAA,EAER,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAGA,SAAS,OAA6B;AAEpC,QAAI,KAAK,WAAW,WAAW,UAAU,WAAW,WAAW;AAE3D,QAAA,UAAU,QAAS,MAAK,aAAa;AAEzC,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA,EAGA;AAAA;AAAA,EAGA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EAEA,OAAO,QAAuB;AAC5B,UAAM,QAAQ,OAAO,KAAK,KAAK,EAAE;AAEjC,SAAK,aAAa,UAAU;AACxB,QAAA,MAAY,MAAA,SAAS,KAAK;AAAA,EAChC;AAAA,EAEA,eAAe,QAAuB;AACpC,SAAK,aAAa;AAClB,SAAK,SAAS,OAAO,KAAK,KAAK,EAAE,KAAK,SAAS;AAAA,EACjD;AAAA;AAAA,EAGA,MAAM,QAAQ,MAAuB;AAC/B,QAAA;AACF,WAAK,YAAY;AACX,YAAA,UAAU,MAAM,KAAK;AACvB,UAAA,CAAC,QAAgB,QAAA;AAErB,WAAK,QAAQ;AACN,aAAA;AAAA,aACA,OAAO;AACd,WAAK,QAAS,OAAiB;AACzB,YAAA;AAAA,IAAA,UACN;AACA,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AACF;AAQa,MAAA,0BAA0B,YAAY,mBAAmB,MAAM;AAE1E,QAAMC,YAAW;AAGX,QAAA,eAAe,IAAI,KAAK;AAC9B,QAAM,2BAA2B;AAAA,IAAS,MACxC,MAAM,MACH,OAAO,CAAC,SAAS,KAAK,YAAY,EAClC,KAAK,CAAC,SAAS,UAAU,IAAI,GAAG,SAAS;AAAA,EAAA;AAE9C,QAAM,2BAA2B;AAAA,IAAS,MACxC,MAAM,MACH,OAAO,CAAC,SAAS,KAAK,iBAAiB,EACvC,KAAK,CAAC,SAAS,UAAU,IAAI,GAAG,SAAS;AAAA,EAAA;AAIxC,QAAA,QAAQ,IAAI,yBAAyB;AAE3C,QAAM,cAAc;AAAA,IAClB,IAAI;AAAA,MACF,0BAA0B,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,IAAI,sBAAsB,CAAC,CAAC,CAAC;AAAA,IAC3E;AAAA,EAAA;AAIF,QAAM,YAAY;AAAA,IAAS,MACzB,MAAM,MAAM,KAAK,CAAC,SAAS,UAAU,IAAI,EAAE,UAAU,OAAO;AAAA,EAAA;AAQ9D,QAAM,YAAY,wBAAC,SAA0B,YAAY,MAAM,IAAI,KAAK,EAAE,GAAxD;AAMZ,QAAA,gBAAgB,wBAAC,qBAAwC;AAC7D,UAAM,SAAS;AACf,iBAAa,QAAQ;AAGV,eAAA,QAAQ,MAAM,OAAO;AACpB,gBAAA,IAAI,EAAE,OAAO,MAAM;AAAA,IAC/B;AAGA,QAAI,CAAC,OAAO,cAAc,aAAa,OAAO;AAC5C,mBAAa,QAAQ;AAEV,iBAAA,QAAQ,MAAM,OAAO;AACpB,kBAAA,IAAI,EAAE,eAAe,MAAM;AAAA,MACvC;AAAA,IACF;AAAA,EAAA,GAhBoB;AAoBtB,QAAM,gBAAgB,6BAAM;AACf,eAAA,QAAQ,MAAM,OAAO;AACpB,gBAAA,IAAI,EAAE,aAAa;AAAA,IAC/B;AAAA,EAAA,GAHoB;AAOtB,QAAM,sBAAsB,mCAAY;AACtC,iBAAa,QAAQ;AACrB,YAAQ,IAAI,0BAA0B;AAChC,UAAAA,UAAS,WAAW,qBAAqB,aAAa;AAAA,EAAA,GAHlC;AAMtB,QAAA,UAAU,8BAAO,SAA0B;AAC/C,WAAO,UAAU,IAAI,EAAE,QAAQ,IAAI;AAAA,EAAA,GADrB;AAIT,SAAA;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,EAAA;AAEJ,CAAC;ACpKe,SAAA,yBACd,OACA,cAAc,KACd;AACM,QAAA,UAAU,IAAI,MAAM,KAAK;AAE/B,QAAM,EAAE,OAAO,UAAU,WAAW,aAAa;AAAA,IAC/C,UAAU;AAAA,IACV,WAAW;AAAA,EAAA,CACZ;AAEK,QAAA,OAAO,CAAC,aAAa;AACzB,QAAI,YAAY,CAAC,QAAQ,MAAa,OAAA;AAEtC,YAAQ,QAAQ;AAAA,EAAA,CACjB;AAED,SAAO,SAAS,MAAM,QAAQ,SAAS,CAAC,MAAM,KAAK;AACrD;AAlBgB;;;;;;;;;;;;;;;;;;;ACiDhB,UAAM,YAAY;AAClB,UAAM,SAAS,SAAS,MAAM,UAAU,UAAU,MAAM,IAAI,CAAC;AAG7D,UAAM,QAAQ;AAUd,UAAM,cAAc;AAAA,MAAS,MAC3B,OAAO,MAAM,UAAU,UACnB,MAAM,KAAK,oBAAoB,MAAM,KAAK,mBAC1C,MAAM,KAAK;AAAA,IAAA;AAIjB,UAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,OAAO,MAAM,UAAU;AAChE,UAAM,oBAAoB,SAAS,MAAM,CAAC,CAAC,OAAO,MAAM,SAAS;AAE3D,UAAA,YAAY,yBAAyB,iBAAiB,GAAG;AACzD,UAAA,cAAc,yBAAyB,mBAAmB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrE7D,UAAA,UAAU,SAAS,MAAM;AACzB,UAAA,MAAM,UAAU,SAAS;AAC3B,eAAO,EAAE,SAAS;AAAA,MAAA,WACT,MAAM,UAAU,MAAM;AAC/B,eAAO,EAAE,gBAAgB;AAAA,MAAA,OACpB;AACL,eAAO,EAAE,qBAAqB;AAAA,MAChC;AAAA,IAAA,CACD;AAEK,UAAA,aAAa,SAAS,MAAM;AAC5B,UAAA;AACA,UAAA,MAAM,UAAU,SAAS;AACjB,kBAAA,GAAG,WAAW,oBAAoB;AAAA,MAAA,WACnC,MAAM,UAAU,MAAM;AACrB,kBAAA,GAAG,WAAW,KAAK;AAAA,MAAA,OACxB;AACL,kBAAU,WAAW;AAAA,MACvB;AAEA,aAAO,eAAe,OAAO;AAAA,IAAA,CAC9B;AAGD,UAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACed,UAAM,YAAY;AAClB,UAAM,SAAS,SAAS,MAAM,UAAU,UAAU,MAAM,IAAI,CAAC;AAG7D,UAAM,QAAQ;AAUd,UAAM,WAAW;AAAA,MAA2B,MAC1C,OAAO,MAAM,UAAU,WAAW,OAAO,MAAM,UAAU,YACrD,YACA;AAAA,IAAA;AAIN,UAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,OAAO,MAAM,UAAU;AAChE,UAAM,oBAAoB,SAAS,MAAM,CAAC,CAAC,OAAO,MAAM,SAAS;AAE3D,UAAA,YAAY,yBAAyB,iBAAiB,GAAG;AACzD,UAAA,cAAc,yBAAyB,mBAAmB,GAAG;AAG7D,UAAA,cAAc,IAAyC,IAAI;AAE3D,UAAA,SAAS,wBAAC,UAAiB;AACnB,kBAAA,OAAO,OAAO,KAAK;AAAA,IAAA,GADlB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3Bf,UAAM,QAAQ;AACd,UAAM,UAAU;AAChB,UAAM,YAAY;AASZ,UAAA,cAAc,8BAAO,SAA0B;AAC/C,UAAA;AAEA,UAAA;AAEF,YAAK,MAAM,UAAU,QAAQ,IAAI,MAAO,KAAM;AAE9C,kBAAU,EAAE,8BAA8B;AAAA,eACnC,OAAO;AACd,kBAAW,OAAiB;AAAA,MAC9B;AAEA,YAAM,IAAI;AAAA,QACR,UAAU;AAAA,QACV,SAAS,EAAE,8BAA8B;AAAA,QACzC,QAAQ,WAAW,EAAE,sCAAsC;AAAA,QAC3D,MAAM;AAAA,MAAA,CACP;AAAA,IAAA,GAjBiB;AAqBd,UAAA,gBAAgB,8BAAO,OAAmB,SAA0B;AACpE,UAAA,CAAC,KAAK,gBAAgB;AACxB,cAAM,YAAY,IAAI;AACtB;AAAA,MACF;AAEA,cAAQ,QAAQ;AAAA,QACd,QAAQ,MAAM;AAAA,QACd,SAAS,KAAK,eAAe,EAAE,0BAA0B;AAAA,QACzD,MAAM;AAAA,QACN,aAAa;AAAA,UACX,OAAO,EAAE,UAAU;AAAA,UACnB,UAAU;AAAA,UACV,UAAU;AAAA,QACZ;AAAA,QACA,aAAa;AAAA,UACX,OAAO,KAAK,QAAQ,QAAQ,EAAE,QAAQ;AAAA,UACtC,UAAU,KAAK,YAAY;AAAA,QAC7B;AAAA;AAAA,QAEA,QAAQ,mCAAY;AAClB,gBAAM,YAAY,IAAI;AAAA,QACxB,GAFQ;AAAA,MAER,CACD;AAAA,IAAA,GAvBmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmBtB,UAAMA,YAAW;AACjB,UAAM,QAAQ;AACd,UAAM,YAAY;AAClB,UAAM,EAAE,eAAe,eAAe,oBAAA,IAAwB;AAExD,UAAA,kBAAkB,IAAI,KAAK;AAGjC,UAAM,uBAAuB,SAAS,MAAM,UAAU,YAAY;AAE5D,UAAA,eAAe,yBAAyB,sBAAsB,GAAG;AAGvE,UAAM,YAAY,SAAS,MAAM,UAAU,SAAS;AAG9C,UAAA,gBAAgB,IAAI,WAAW,QAAQ;AAE7C,UAAM,cAAc;AAAA,MAAS,MAC3B,UAAU,MAAM,OAAO,CAAC,MAAM;AAC5B,cAAM,EAAE,OAAO,SAAA,IAAa,UAAU,UAAU,CAAC;AACjD,eAAO,UAAU,WAAW;AAAA,MAAA,CAC7B;AAAA,IAAA;AAGH,UAAM,gBAAgB,IAAI;AAAA,MACxB,EAAE,MAAM,WAAW,aAAa,OAAO,OAAO,OAAO,UAAU,MAAM;AAAA,MACrE,EAAE,MAAM,WAAW,sBAAsB,OAAO,UAAU,OAAO,YAAY;AAAA,IAAA,CAC9E;AAGD,UAAM,SAAS,IAAuB,cAAc,MAAM,CAAC,CAAC;AAG5D,UAAM,qBAAqB,mCAAY;AACrC,YAAM,UAAU,MAAMA,UAAS,WAAW,SAAS;AACnD,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI;AAAA,UACR,UAAU;AAAA,UACV,SAAS,EAAE,SAAS;AAAA,UACpB,QAAQ,EAAE,kCAAkC;AAAA,UAC5C,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,IAAA,GATyB;AAY3B,UAAM,sBAAsB,6BAAM;AAChB,sBAAA,QAAQ,CAAC,gBAAgB;AAAA,IAAA,GADf;AAK5B;AAAA,MACE,MAAM,UAAU;AAAA,MAChB,CAAC,UAAU;AACT,wBAAgB,QAAQ;AAAA,MAC1B;AAAA,IAAA;AAGF,cAAU,YAAY;AACX,MAAAA,UAAA,WAAW,SAAS,aAAa;AAE1C,YAAM,SAAS,MAAMA,UAAS,WAAW,UAAU;AAC/C,UAAA,OAAO,OAAO,MAAM,EAAE,KAAK,CAAC,MAAM,MAAM,OAAO,GAAG;AAC7C,eAAA,QAAQ,cAAc,MAAM,CAAC;AAAA,MACtC;AACA,oBAAc,MAAM;AAAA,IAAA,CACrB;AAED,gBAAY,MAAMA,UAAS,WAAW,QAAS,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}