@echo off
echo Starting ComfyUI - High Performance GPU Mode...
echo.

REM 激活虚拟环境
call comfyui_env\Scripts\activate.bat

REM 显示 GPU 信息
echo Virtual environment activated.
echo Checking GPU status...
python -c "import torch; print('GPU Available:', torch.cuda.is_available()); print('GPU Name:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'No GPU'); print('VRAM Total:', round(torch.cuda.get_device_properties(0).total_memory/1024**3, 1), 'GB' if torch.cuda.is_available() else '')"
echo.

REM 启动 ComfyUI (高性能模式)
echo Starting ComfyUI server in HIGH PERFORMANCE mode...
echo Using standard VRAM management for maximum speed
echo WARNING: May use more VRAM - monitor usage
echo.
echo ComfyUI will be available at: http://127.0.0.1:8188
echo Press Ctrl+C to stop the server.
echo.

python main.py --auto-launch

pause
