{"version": 3, "file": "DesktopUpdateView-BuCQvA3z.js", "sources": ["../../src/views/DesktopUpdateView.vue"], "sourcesContent": ["<template>\n  <BaseViewTemplate dark>\n    <div\n      class=\"h-screen w-screen grid items-center justify-around overflow-y-auto\"\n    >\n      <div class=\"relative m-8 text-center\">\n        <!-- Header -->\n        <h1 class=\"download-bg pi-download text-4xl font-bold\">\n          {{ t('desktopUpdate.title') }}\n        </h1>\n\n        <div class=\"m-8\">\n          <span>{{ t('desktopUpdate.description') }}</span>\n        </div>\n\n        <ProgressSpinner class=\"m-8 w-48 h-48\" />\n\n        <!-- Console button -->\n        <Button\n          style=\"transform: translateX(-50%)\"\n          class=\"fixed bottom-0 left-1/2 my-8\"\n          :label=\"t('maintenance.consoleLogs')\"\n          icon=\"pi pi-desktop\"\n          icon-pos=\"left\"\n          severity=\"secondary\"\n          @click=\"toggleConsoleDrawer\"\n        />\n\n        <TerminalOutputDrawer\n          v-model=\"terminalVisible\"\n          :header=\"t('g.terminal')\"\n          :default-message=\"t('desktopUpdate.terminalDefaultMessage')\"\n        />\n      </div>\n    </div>\n    <Toast />\n  </BaseViewTemplate>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport ProgressSpinner from 'primevue/progressspinner'\nimport Toast from 'primevue/toast'\nimport { onUnmounted, ref } from 'vue'\n\nimport TerminalOutputDrawer from '@/components/maintenance/TerminalOutputDrawer.vue'\nimport { t } from '@/i18n'\nimport { electronAPI } from '@/utils/envUtil'\n\nimport BaseViewTemplate from './templates/BaseViewTemplate.vue'\n\nconst electron = electronAPI()\n\nconst terminalVisible = ref(false)\n\nconst toggleConsoleDrawer = () => {\n  terminalVisible.value = !terminalVisible.value\n}\n\nonUnmounted(() => electron.Validation.dispose())\n</script>\n\n<style scoped>\n.download-bg::before {\n  @apply m-0 absolute text-muted;\n  font-family: 'primeicons';\n  top: -2rem;\n  right: 2rem;\n  speak: none;\n  font-style: normal;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n  display: inline-block;\n  -webkit-font-smoothing: antialiased;\n  opacity: 0.02;\n  font-size: min(14rem, 90vw);\n  z-index: 0;\n}\n</style>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAmDA,UAAM,WAAW;AAEX,UAAA,kBAAkB,IAAI,KAAK;AAEjC,UAAM,sBAAsB,6BAAM;AAChB,sBAAA,QAAQ,CAAC,gBAAgB;AAAA,IAAA,GADf;AAI5B,gBAAY,MAAM,SAAS,WAAW,QAAS,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}