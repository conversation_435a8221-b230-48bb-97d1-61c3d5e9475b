{"last_node_id": 33, "last_link_id": 62, "nodes": [{"id": 4, "type": "CheckpointLoaderSimple", "pos": [-60, 229], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [54], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [3, 5], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [8, 31], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["wd-illusion-fp16.safetensors"]}, {"id": 13, "type": "CheckpointLoaderSimple", "pos": [1296, -571], "size": [315, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [56], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [27], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": null}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["cardosAnime_v10.safetensors"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [370, 40], "size": [510, 220], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [4], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["anime happy girl (fennec:1.2) (ears:1.3) blonde long (messy hair:1.1) blue eyes, wearing serafuku jeans (sitting on rock:1.15) (spread legs:1.15) (sneakers:0.95) in lake rural swiss village on the mountain side sky clouds HDR sunset\n(exceptional, best aesthetic, new, newest, best quality, masterpiece, extremely detailed, anime:1.05)\n"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [370, 300], "size": [510, 190], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [6], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["lowres, bad anatomy, bad hands, (text:1.1), blurry, mutated hands and fingers, mutation, deformed face, ugly, (logo:1.1), cropped, worst quality, jpeg, (jpeg artifacts), deleted, old, oldest, (censored), (bad aesthetic), (mosaic censoring, bar censor, blur censor) earphones"]}, {"id": 5, "type": "EmptyLatentImage", "pos": [560, 540], "size": [315, 106], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1368, 768, 1]}, {"id": 8, "type": "VAEDecode", "pos": [1280, 140], "size": [210, 46], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [10], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 10, "type": "SaveImage", "pos": [1540, 140], "size": [1174.13, 734.16], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 10}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 22, "type": "CLIPSetLastLayer", "pos": [1670, -550], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 27}], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [13, 14], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2]}, {"id": 15, "type": "CLIPTextEncode", "pos": [2060, -920], "size": [662.38, 313.1], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 14}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [57], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["from far away anime happy girl (fennec ears:0.95) long (messy hair:1.3) blue eyes, wearing serafuku jeans sitting on rock spread legs (sneakers:0.95) in lake rural swiss village on the mountain side sky clouds HDR sunset\n"]}, {"id": 14, "type": "CLIPTextEncode", "pos": [2060, -550], "size": [660, 300], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 13}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [58], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(hands), (text:1.1), letters, numbers, error, cropped, (jpeg artifacts:1.2), (signature:1.1), (watermark:1.1), username, blurry, artist name, monochrome, sketch, censorship, censor, (copyright:1.1), extra legs, (forehead mark)  (penis)"]}, {"id": 11, "type": "VAEDecode", "pos": [3240, -750], "size": [210, 46], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 60}, {"name": "vae", "type": "VAE", "link": 31}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 12, "type": "SaveImage", "pos": [3540, -750], "size": [1868.09, 1101.47], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 12}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 32, "type": "K<PERSON><PERSON><PERSON>", "pos": [2830, -750], "size": [315, 262], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 56}, {"name": "positive", "type": "CONDITIONING", "link": 57}, {"name": "negative", "type": "CONDITIONING", "link": 58}, {"name": "latent_image", "type": "LATENT", "link": 59}], "outputs": [{"name": "LATENT", "type": "LATENT", "shape": 3, "links": [60], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [417682270866800, "randomize", 8, 13, "dpmpp_sde", "simple", 0.5]}, {"id": 27, "type": "LatentUpscaleBy", "pos": [1510, -160], "size": [325.41, 82], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 62, "slot_index": 0}], "outputs": [{"name": "LATENT", "type": "LATENT", "shape": 3, "links": [59], "slot_index": 0}], "properties": {"Node name for S&R": "LatentUpscaleBy"}, "widgets_values": ["bislerp", 1.5]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [920, 140], "size": [318.5, 262], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 54}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7, 62], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [758448896326830, "randomize", 14, 8, "dpmpp_sde", "simple", 1]}, {"id": 33, "type": "<PERSON>downNote", "pos": [-45, 375], "size": [225, 60], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [Latent Upscale - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/2_pass_txt2img/#more-examples) — Overview\n> \n> [ComfyUI Image Upscale - docs.comfy.org](https://docs.comfy.org/tutorials/basic/upscale) — Upscaling step-by-step tutorial"], "color": "#432", "bgcolor": "#653"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [3, 4, 1, 6, 0, "CLIP"], [4, 6, 0, 3, 1, "CONDITIONING"], [5, 4, 1, 7, 0, "CLIP"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [8, 4, 2, 8, 1, "VAE"], [10, 8, 0, 10, 0, "IMAGE"], [12, 11, 0, 12, 0, "IMAGE"], [13, 22, 0, 14, 0, "CLIP"], [14, 22, 0, 15, 0, "CLIP"], [27, 13, 1, 22, 0, "CLIP"], [31, 4, 2, 11, 1, "VAE"], [54, 4, 0, 3, 0, "MODEL"], [56, 13, 0, 32, 0, "MODEL"], [57, 15, 0, 32, 1, "CONDITIONING"], [58, 14, 0, 32, 2, "CONDITIONING"], [59, 27, 0, 32, 3, "LATENT"], [60, 32, 0, 11, 0, "LATENT"], [62, 3, 0, 27, 0, "LATENT"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.76, "offset": [1200.17, 444.58]}}, "version": 0.4}