{"version": 3, "file": "MetricsConsentView-DWTSgL2l.js", "sources": ["../../src/views/MetricsConsentView.vue"], "sourcesContent": ["<template>\n  <BaseViewTemplate dark>\n    <div class=\"h-full p-8 2xl:p-16 flex flex-col items-center justify-center\">\n      <div\n        class=\"bg-neutral-800 rounded-lg shadow-lg p-6 w-full max-w-[600px] flex flex-col gap-6\"\n      >\n        <h2 class=\"text-3xl font-semibold text-neutral-100\">\n          {{ $t('install.helpImprove') }}\n        </h2>\n        <p class=\"text-neutral-400\">\n          {{ $t('install.updateConsent') }}\n        </p>\n        <p class=\"text-neutral-400\">\n          {{ $t('install.moreInfo') }}\n          <a\n            href=\"https://comfy.org/privacy\"\n            target=\"_blank\"\n            class=\"text-blue-400 hover:text-blue-300 underline\"\n          >\n            {{ $t('install.privacyPolicy') }} </a\n          >.\n        </p>\n        <div class=\"flex items-center gap-4\">\n          <ToggleSwitch\n            v-model=\"allowMetrics\"\n            aria-describedby=\"metricsDescription\"\n          />\n          <span id=\"metricsDescription\" class=\"text-neutral-100\">\n            {{\n              allowMetrics\n                ? $t('install.metricsEnabled')\n                : $t('install.metricsDisabled')\n            }}\n          </span>\n        </div>\n        <div class=\"flex pt-6 justify-end\">\n          <Button\n            :label=\"$t('g.ok')\"\n            icon=\"pi pi-check\"\n            :loading=\"isUpdating\"\n            icon-pos=\"right\"\n            @click=\"updateConsent\"\n          />\n        </div>\n      </div>\n    </div>\n  </BaseViewTemplate>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport ToggleSwitch from 'primevue/toggleswitch'\nimport { useToast } from 'primevue/usetoast'\nimport { ref } from 'vue'\nimport { useI18n } from 'vue-i18n'\nimport { useRouter } from 'vue-router'\n\nimport { electronAPI } from '@/utils/envUtil'\n\nconst toast = useToast()\nconst { t } = useI18n()\n\nconst allowMetrics = ref(true)\nconst router = useRouter()\nconst isUpdating = ref(false)\n\nconst updateConsent = async () => {\n  isUpdating.value = true\n  try {\n    await electronAPI().setMetricsConsent(allowMetrics.value)\n  } catch (error) {\n    toast.add({\n      severity: 'error',\n      summary: t('install.errorUpdatingConsent'),\n      detail: t('install.errorUpdatingConsentDetail'),\n      life: 3000\n    })\n  } finally {\n    isUpdating.value = false\n  }\n  await router.push('/')\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA,UAAM,QAAQ;AACR,UAAA,EAAE,MAAM;AAER,UAAA,eAAe,IAAI,IAAI;AAC7B,UAAM,SAAS;AACT,UAAA,aAAa,IAAI,KAAK;AAE5B,UAAM,gBAAgB,mCAAY;AAChC,iBAAW,QAAQ;AACf,UAAA;AACF,cAAM,YAAY,EAAE,kBAAkB,aAAa,KAAK;AAAA,eACjD,OAAO;AACd,cAAM,IAAI;AAAA,UACR,UAAU;AAAA,UACV,SAAS,EAAE,8BAA8B;AAAA,UACzC,QAAQ,EAAE,oCAAoC;AAAA,UAC9C,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,mBAAW,QAAQ;AAAA,MACrB;AACM,YAAA,OAAO,KAAK,GAAG;AAAA,IAAA,GAdD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}