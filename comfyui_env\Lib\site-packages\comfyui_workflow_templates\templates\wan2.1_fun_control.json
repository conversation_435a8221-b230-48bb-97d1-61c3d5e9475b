{"id": "e7533930-2792-43a9-b4b5-ded4617d8a43", "revision": 0, "last_node_id": 77, "last_link_id": 146, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [1170, 500], "size": [280, 262], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 135}, {"name": "positive", "type": "CONDITIONING", "link": 115}, {"name": "negative", "type": "CONDITIONING", "link": 116}, {"name": "latent_image", "type": "LATENT", "link": 117}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [35]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [887940314022885, "randomize", 20, 6, "uni_pc", "simple", 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [420, 70], "size": [420, 140], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 74}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [112]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a women sticking out her toungue while mkaing wierd faces,(clay style:1.1)"], "color": "#232", "bgcolor": "#353"}, {"id": 7, "type": "CLIPTextEncode", "pos": [420, 260], "size": [420, 130], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 75}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [113]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走，不合理的动作"], "color": "#323", "bgcolor": "#535"}, {"id": 8, "type": "VAEDecode", "pos": [910, 800], "size": [210, 266], "flags": {"collapsed": true}, "order": 18, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 35}, {"name": "vae", "type": "VAE", "link": 76}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [56]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 28, "type": "SaveAnimatedWEBP", "pos": [870, 900], "size": [600, 520], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 56}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI", 16, false, 90, "default"]}, {"id": 37, "type": "UNETLoader", "pos": [30, 70], "size": [346.7470703125, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [146]}], "properties": {"Node name for S&R": "UNETLoader", "models": [{"name": "wan2.1_fun_control_1.3B_bf16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_fun_control_1.3B_bf16.safetensors?download=true", "directory": "diffusion_models"}]}, "widgets_values": ["wan2.1_fun_control_1.3B_bf16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 38, "type": "CLIPLoader", "pos": [30, 190], "size": [350, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [74, 75]}], "properties": {"Node name for S&R": "CLIPLoader", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true", "directory": "text_encoders"}]}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 39, "type": "VAELoader", "pos": [30, 330], "size": [350, 60], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "slot_index": 0, "links": [76, 114]}], "properties": {"Node name for S&R": "VAELoader", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true", "directory": "vae"}]}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 49, "type": "CLIPVisionLoader", "pos": [30, 500], "size": [340, 60], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "slot_index": 0, "links": [94]}], "properties": {"Node name for S&R": "CLIPVisionLoader", "models": [{"name": "clip_vision_h.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/clip_vision/clip_vision_h.safetensors?download=true", "directory": "clip_vision"}]}, "widgets_values": ["clip_vision_h.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 51, "type": "CLIPVisionEncode", "pos": [440, 660], "size": [253.60000610351562, 78], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 94}, {"name": "image", "type": "IMAGE", "link": 109}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [119]}], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 52, "type": "LoadImage", "pos": [30, 600], "size": [340, 326], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [109, 118]}, {"name": "MASK", "type": "MASK", "slot_index": 1, "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["20250330_1419_<PERSON><PERSON>ugh Portrait_remix_01jqkfw8jhf6brecnqvwjt85sc (1).png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 55, "type": "WanFunControlToVideo", "pos": [880, 500], "size": [250, 230], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 112}, {"name": "negative", "type": "CONDITIONING", "link": 113}, {"name": "vae", "type": "VAE", "link": 114}, {"name": "clip_vision_output", "shape": 7, "type": "CLIP_VISION_OUTPUT", "link": 119}, {"name": "start_image", "shape": 7, "type": "IMAGE", "link": 118}, {"name": "control_video", "shape": 7, "type": "IMAGE", "link": 142}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [115]}, {"name": "negative", "type": "CONDITIONING", "links": [116]}, {"name": "latent", "type": "LATENT", "links": [117]}], "properties": {"Node name for S&R": "WanFunControlToVideo"}, "widgets_values": [960, 544, 77, 1]}, {"id": 65, "type": "SkipLayerGuidanceDiT", "pos": [890, 80], "size": [230, 180], "flags": {"collapsed": false}, "order": 11, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 146}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [132]}], "properties": {"Node name for S&R": "SkipLayerGuidanceDiT", "cnr_id": "comfy-core", "ver": "0.3.27", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": ["9,10", "9,10", 3, 0.01, 0.8000000000000002, 0]}, {"id": 66, "type": "CFGZeroStar", "pos": [890, 310], "size": [230, 30], "flags": {"collapsed": false}, "order": 16, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 131}], "outputs": [{"name": "patched_model", "type": "MODEL", "links": [135]}], "properties": {"Node name for S&R": "CFGZeroStar", "cnr_id": "comfy-core", "ver": "0.3.27", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": []}, {"id": 67, "type": "ModelSamplingSD3", "pos": [1200, 80], "size": [240, 60], "flags": {"collapsed": false}, "order": 14, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 132}], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [133]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "cnr_id": "comfy-core", "ver": "0.3.26", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [5.000000000000001]}, {"id": 68, "type": "UNetTemporalAttentionMultiply", "pos": [1200, 190], "size": [243.60000610351562, 150], "flags": {"collapsed": false}, "order": 15, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 133}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [131]}], "properties": {"Node name for S&R": "UNetTemporalAttentionMultiply", "cnr_id": "comfy-core", "ver": "0.3.27", "enableTabs": false, "tabWidth": 65, "tabXOffset": 10, "hasSecondTab": false, "secondTabText": "Send Back", "secondTabOffset": 80, "secondTabWidth": 65}, "widgets_values": [1, 1, 1.2, 1.3]}, {"id": 71, "type": "<PERSON><PERSON>", "pos": [390, 1040], "size": [315, 82], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 141}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [142, 143]}], "properties": {"Node name for S&R": "<PERSON><PERSON>"}, "widgets_values": [0.20000000000000004, 0.5000000000000001]}, {"id": 72, "type": "LoadImage", "pos": [30, 1040], "size": [315, 314], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [141]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_00007_.webp", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 74, "type": "PreviewImage", "pos": [390, 1170], "size": [360, 258], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 143}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 77, "type": "<PERSON>downNote", "pos": [24, 1480], "size": [320, 112], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [ComfyUI Wan2.1 Fun Control Video - docs.comfy](https://docs.comfy.org/tutorials/video/wan/fun-control)\n> \n"], "color": "#432", "bgcolor": "#653"}], "links": [[35, 3, 0, 8, 0, "LATENT"], [56, 8, 0, 28, 0, "IMAGE"], [74, 38, 0, 6, 0, "CLIP"], [75, 38, 0, 7, 0, "CLIP"], [76, 39, 0, 8, 1, "VAE"], [94, 49, 0, 51, 0, "CLIP_VISION"], [109, 52, 0, 51, 1, "IMAGE"], [112, 6, 0, 55, 0, "CONDITIONING"], [113, 7, 0, 55, 1, "CONDITIONING"], [114, 39, 0, 55, 2, "VAE"], [115, 55, 0, 3, 1, "CONDITIONING"], [116, 55, 1, 3, 2, "CONDITIONING"], [117, 55, 2, 3, 3, "LATENT"], [118, 52, 0, 55, 4, "IMAGE"], [119, 51, 0, 55, 3, "CLIP_VISION_OUTPUT"], [131, 68, 0, 66, 0, "MODEL"], [132, 65, 0, 67, 0, "MODEL"], [133, 67, 0, 68, 0, "MODEL"], [135, 66, 0, 3, 0, "MODEL"], [141, 72, 0, 71, 0, "IMAGE"], [142, 71, 0, 55, 5, "IMAGE"], [143, 71, 0, 74, 0, "IMAGE"], [146, 37, 0, 65, 0, "MODEL"]], "groups": [{"id": 1, "title": "Load Models", "bounding": [24, 0, 370, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Start Frame & CLIP Vision", "bounding": [24, 432, 830, 510], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Attention Booster", "bounding": [872, 0, 600, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Prompt", "bounding": [408, 0, 440, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "Video and Preprocessor", "bounding": [24, 960, 830, 480], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "Sampling & Decode", "bounding": [872, 432, 600, 400], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"frontendVersion": "1.16.7"}, "version": 0.4}