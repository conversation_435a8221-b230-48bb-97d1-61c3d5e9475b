# 最佳质量照片生成工作流指南

## 🏆 您的模型资产分析

### ✅ 完整的 Flux 生态系统
您拥有完整的 Flux 模型套件，这是目前最先进的 AI 图像生成技术：

```
🔥 Flux UNet: flux1-dev-fp8.safetensors (16GB → 8GB FP8 优化)
🧠 CLIP-L: clip_l.safetensors (高质量文本编码)
🧠 T5-XXL: t5xxl_fp8_e4m3fn.safetensors (超强文本理解)
🎨 Flux VAE: ae.safetensors (16通道高质量解码)
📦 SD1.5: v1-5-pruned-emaonly.safetensors (备用选择)
```

## 🚀 推荐工作流

### 🥇 终极质量工作流 (推荐)
**文件**: `workflows/flux_ultimate_quality.json`

**特点**:
- 🎯 30 步采样 (最高质量)
- 📐 1024x1024 分辨率
- 🧠 完整 Flux 生态系统
- 🎨 专业级照片质量

### 🥈 平衡质量工作流
**文件**: `workflows/flux_best_quality.json`

**特点**:
- ⚡ 25 步采样 (平衡速度和质量)
- 📐 1024x1024 分辨率
- 🔄 更快的生成速度

## 🎯 最佳质量设置

### 分辨率建议
```
人物肖像: 832x1216 或 1024x1024
风景照片: 1216x832 或 1024x1024
方形构图: 1024x1024
超高清: 1536x1536 (需要更多显存)
```

### 采样参数优化
```
步数: 25-35 (更多步数 = 更高质量)
采样器: euler (Flux 最佳)
调度器: simple (稳定可靠)
种子: 固定数值获得可重现结果
```

## 🎨 专业提示词模板

### 人物肖像 (最佳效果)
```
"professional portrait photograph of [描述], natural lighting, shallow depth of field, photorealistic, highly detailed, 8k resolution, award winning photography, masterpiece"

示例:
"professional portrait photograph of a young woman with curly hair, natural lighting, shallow depth of field, photorealistic, highly detailed, 8k resolution, award winning photography, masterpiece"
```

### 风景摄影
```
"landscape photography of [场景], golden hour lighting, professional composition, highly detailed, photorealistic, 8k resolution, award winning photography"

示例:
"landscape photography of mountain peaks reflected in a crystal clear lake, golden hour lighting, professional composition, highly detailed, photorealistic, 8k resolution, award winning photography"
```

### 产品摄影
```
"product photography of [产品], studio lighting, clean background, professional composition, highly detailed, commercial photography, 8k resolution"
```

### 街头摄影
```
"street photography of [场景], natural lighting, candid moment, photojournalism style, highly detailed, authentic, 8k resolution"
```

## ⚙️ 硬件优化 (RTX 4070 8GB)

### 推荐启动参数
```bash
# 标准模式 (推荐)
python main.py --auto-launch

# 如果显存不足
python main.py --lowvram --auto-launch

# 极限优化 (如果仍然不足)
python main.py --lowvram --cpu-vae --auto-launch
```

### 分辨率与显存关系
```
1024x1024: 约 6-7GB VRAM (推荐)
1216x832:  约 6-7GB VRAM (推荐)
1536x1536: 约 8-10GB VRAM (可能需要 --lowvram)
```

## 🔧 质量优化技巧

### 1. 种子控制
```json
"seed": 42  // 固定种子获得一致结果
"seed": -1  // 随机种子探索不同可能
```

### 2. 步数调整
```
快速预览: 15-20 步
标准质量: 25 步
高质量: 30-35 步
极致质量: 40+ 步 (时间较长)
```

### 3. 提示词优化
- **具体描述**: 详细描述想要的效果
- **摄影术语**: 使用专业摄影词汇
- **质量词汇**: 添加 "masterpiece", "best quality", "8k"
- **风格指定**: 指定摄影风格和光照

### 4. 负面提示词 (Flux 通常不需要)
Flux 模型很少需要负面提示词，但如果需要可以添加：
```
"blurry, low quality, distorted, ugly, bad anatomy"
```

## 📊 工作流对比

| 工作流 | 质量 | 速度 | 显存需求 | 推荐用途 |
|--------|------|------|----------|----------|
| flux_ultimate_quality | 最高 | 慢 | 高 | 最终作品 |
| flux_best_quality | 高 | 中 | 中 | 日常使用 |
| sd15_fixed | 中 | 快 | 低 | 快速预览 |

## 🎯 使用流程

### 第一步: 导入工作流
```
拖拽 workflows/flux_ultimate_quality.json 到 ComfyUI 界面
```

### 第二步: 调整提示词
使用专业摄影提示词模板

### 第三步: 设置参数
- 分辨率: 1024x1024
- 种子: 42 (或您喜欢的数字)
- 步数: 30

### 第四步: 生成图像
点击 "Queue Prompt" 开始生成

### 第五步: 迭代优化
- 调整提示词细节
- 尝试不同种子
- 微调参数设置

## 🌟 专业技巧

### 1. 批量生成
修改 `batch_size` 为 2-4 生成多个变体

### 2. 分辨率实验
尝试不同的宽高比：
- 肖像: 832x1216
- 风景: 1216x832
- 方形: 1024x1024

### 3. 种子探索
使用连续种子 (42, 43, 44...) 找到最佳结果

### 4. 提示词 A/B 测试
对比不同提示词的效果差异

## 🎉 开始创作

现在您拥有了最先进的 AI 图像生成工具！

**推荐开始**:
1. 使用 `flux_ultimate_quality.json`
2. 尝试人物肖像提示词
3. 体验 Flux 的惊人质量

您的 RTX 4070 + 完整 Flux 套件 = 专业级 AI 摄影工作室！📸✨
