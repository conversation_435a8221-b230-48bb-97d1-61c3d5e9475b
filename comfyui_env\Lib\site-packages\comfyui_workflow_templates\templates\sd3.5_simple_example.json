{"last_node_id": 54, "last_link_id": 102, "nodes": [{"id": 8, "type": "VAEDecode", "pos": [1200, 96], "size": [210, 46], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 53, "slot_index": 1}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [51], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1440, 96], "size": [952.51, 1007.93], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 51, "slot_index": 0}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 40, "type": "CLIPTextEncode", "pos": [384, 336], "size": [432, 192], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 102}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "shape": 3, "links": [80], "slot_index": 0}], "title": "Negative Prompt", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533"}, {"id": 53, "type": "EmptySD3LatentImage", "pos": [480, 576], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "shape": 3, "links": [100], "slot_index": 0}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-48, 96], "size": [384.76, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [99], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [101, 102], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [53], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["sd3.5_large_fp8_scaled.safetensors"]}, {"id": 16, "type": "CLIPTextEncode", "pos": [384, 96], "size": [432, 192], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 101}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [21], "slot_index": 0}], "title": "Positive Prompt", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a bottle with a pink and red galaxy inside it on top of a wooden table on a table in the middle of a modern kitchen with a window to the outdoors mountain range bright sun clouds forest"], "color": "#232", "bgcolor": "#353"}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [864, 96], "size": [315, 262], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 99, "slot_index": 0}, {"name": "positive", "type": "CONDITIONING", "link": 21}, {"name": "negative", "type": "CONDITIONING", "link": 80}, {"name": "latent_image", "type": "LATENT", "link": 100}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [585483408983215, "randomize", 20, 4.01, "euler", "sgm_uniform", 1]}, {"id": 54, "type": "<PERSON>downNote", "pos": [-45, 240], "size": [225, 60], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/sd3/#sd35)"], "color": "#432", "bgcolor": "#653"}], "links": [[7, 3, 0, 8, 0, "LATENT"], [21, 16, 0, 3, 1, "CONDITIONING"], [51, 8, 0, 9, 0, "IMAGE"], [53, 4, 2, 8, 1, "VAE"], [80, 40, 0, 3, 2, "CONDITIONING"], [99, 4, 0, 3, 0, "MODEL"], [100, 53, 0, 3, 3, "LATENT"], [101, 4, 1, 16, 0, "CLIP"], [102, 4, 1, 40, 0, "CLIP"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.14, "offset": [93.35, -1.71]}}, "version": 0.4, "models": [{"name": "sd3.5_large_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/stable-diffusion-3.5-fp8/resolve/main/sd3.5_large_fp8_scaled.safetensors?download=true", "directory": "checkpoints"}]}