{"id": "89cbbecd-10c7-4fe6-ae28-5078159eb4cb", "revision": 0, "last_node_id": 13, "last_link_id": 26, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [332.53900146484375, 143.21365356445312], "size": [315, 474], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 23}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 4}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 6}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": [261660645921551, "randomize", 30, 7, "dpmpp_2m", "karras", 1]}, {"id": 7, "type": "CLIPTextEncode", "pos": [-117.46131896972656, 343.2137145996094], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 26}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["(worst quality, low quality:1.4), (bad anatomy), text, error, missing fingers, extra digit, fewer digits, cropped, jpeg artifacts, signature, watermark, username, blurry, deformed face"]}, {"id": 8, "type": "VAEDecode", "pos": [682.5383911132812, 143.21365356445312], "size": [210, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 7}, {"label": "vae", "name": "vae", "type": "VAE", "link": 8}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [922.5383911132812, 143.21365356445312], "size": [210, 270], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["2loras_test_"]}, {"id": 13, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-470, 340], "size": [315, 126], "flags": {}, "order": 4, "mode": 4, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 22}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 24}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [23]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [25, 26]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.18", "models": [{"name": "MoXinV1.safetensors", "url": "https://civitai.com/api/download/models/14856?type=Model&format=SafeTensor&size=full&fp=fp16", "directory": "loras"}]}, "widgets_values": ["MoXinV1.safetensors", 0.5, 1], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "CLIPTextEncode", "pos": [-117.46131896972656, 143.21365356445312], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 25}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": ["upperbody shot, 1girl,solo,chibi,long hairs, happy, laugh, hugging a teddy bear, looking at viewers, dancing stand, cute, soft color, flowers in background, many flowers, among flowers, best quality, highres, delicate details,"]}, {"id": 5, "type": "EmptyLatentImage", "pos": [-103.87992858886719, 579.0239868164062], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"Node name for S&R": "EmptyLatentImage", "cnr_id": "comfy-core", "ver": "0.3.18"}, "widgets_values": [768, 768, 1]}, {"id": 11, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-480, 150], "size": [315, 126], "flags": {}, "order": 3, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 17}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 18}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [22]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [24]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.18", "models": [{"name": "blindbox_v1_mix.safetensors", "url": "https://civitai.com/api/download/models/32988?type=Model&format=SafeTensor&size=full&fp=fp16", "directory": "loras"}]}, "widgets_values": ["blindbox_v1_mix.safetensors", 0.75, 1], "color": "#322", "bgcolor": "#533"}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-841.1585083007812, 149.66726684570312], "size": [315, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [17]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [18]}, {"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [8]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "cnr_id": "comfy-core", "ver": "0.3.18", "models": [{"name": "dreamshaper_8.safetensors", "url": "https://civitai.com/api/download/models/128713?type=Model&format=SafeTensor&size=pruned&fp=fp16", "directory": "checkpoints"}]}, "widgets_values": ["dreamshaper_8.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 12, "type": "<PERSON>downNote", "pos": [-843.087158203125, 337.3919982910156], "size": [312, 136], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [LoRA - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/lora/) — Overview\n> \n> [Multiple LoRAs - docs.comfy.org](https://docs.comfy.org/tutorials/basic/multiple-loras) — Detailed guide to using multiple LoRAs"], "color": "#432", "bgcolor": "#653"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [8, 4, 2, 8, 1, "VAE"], [9, 8, 0, 9, 0, "IMAGE"], [17, 4, 0, 11, 0, "MODEL"], [18, 4, 1, 11, 1, "CLIP"], [22, 11, 0, 13, 0, "MODEL"], [23, 13, 0, 3, 0, "MODEL"], [24, 11, 1, 13, 1, "CLIP"], [25, 13, 1, 6, 0, "CLIP"], [26, 13, 1, 7, 0, "CLIP"]], "groups": [], "config": {}, "extra": {"node_versions": {"comfy-core": "v0.3.9"}, "frontendVersion": "1.19.4"}, "version": 0.4}