{"id": "96995b8f-85c5-47af-b3cf-7b6a24675694", "revision": 0, "last_node_id": 163, "last_link_id": 295, "nodes": [{"id": 120, "type": "<PERSON>downNote", "pos": [-710, 510], "size": [370, 220], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "title": "Note", "properties": {}, "widgets_values": ["The generation quality of the 14B model is better, but it takes longer to generate. If you have already downloaded the model, you can choose to directly use the nodes above, or just modify the model loaded by the loader node.\n\nThe corresponding LoRA should match the Diffusion Model. For example, the LoRA corresponding to the 14B diffusion model is the 14B LoRA. \n\n---\n\n14B 的生成质量更好，但是需要更长的时间去生成，如果你下载好了模型，你可以选择直接使用上面的节点，或者只是修改 loader 节点加载的模型。\n\n对应的 LoRA 应该是和 Diffusion Model匹配的，比如 14B 的 diffusion model 对应的是 14B 的 LoRA"], "color": "#432", "bgcolor": "#653"}, {"id": 124, "type": "UNETLoader", "pos": [-700, 50], "size": [350, 82], "flags": {}, "order": 1, "mode": 4, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [195]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "UNETLoader", "models": [{"name": "wan2.1_vace_1.3B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_vace_1.3B_fp16.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["wan2.1_vace_1.3B_fp16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 125, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-700, 170], "size": [350, 126], "flags": {}, "order": 17, "mode": 4, "inputs": [{"name": "model", "type": "MODEL", "link": 195}, {"name": "clip", "type": "CLIP", "link": 196}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": []}, {"name": "CLIP", "type": "CLIP", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "models": [{"name": "Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors", "url": "https://huggingface.co/Kijai/WanVideo_comfy/resolve/main/Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors", "directory": "loras"}]}, "widgets_values": ["Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors", 0.7000000000000002, 1], "color": "#322", "bgcolor": "#533"}, {"id": 126, "type": "CLIPLoader", "pos": [-710, 360], "size": [370, 106], "flags": {}, "order": 2, "mode": 4, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPLoader", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true", "directory": "text_encoders"}]}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 119, "type": "VAEDecode", "pos": [1140, 580], "size": [210, 46], "flags": {"collapsed": true}, "order": 35, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 191}, {"name": "vae", "type": "VAE", "link": 192}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [197, 198]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 127, "type": "CLIPLoader", "pos": [-290, 390], "size": [350, 106], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [188, 196]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPLoader", "models": [{"name": "umt5_xxl_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp16.safetensors", "directory": "text_encoders"}]}, "widgets_values": ["umt5_xxl_fp16.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 70, "type": "SaveAnimatedWEBP", "pos": [2090, 70], "size": [670, 780], "flags": {}, "order": 37, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 198}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SaveAnimatedWEBP"}, "widgets_values": ["ComfyUI", 6, true, 80, "default"]}, {"id": 118, "type": "TrimVideoLatent", "pos": [960, 580], "size": [276.5860290527344, 58], "flags": {"collapsed": true}, "order": 34, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 189}, {"name": "trim_amount", "type": "INT", "widget": {"name": "trim_amount"}, "link": 282}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [191]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "TrimVideoLatent"}, "widgets_values": [0]}, {"id": 110, "type": "ModelSamplingSD3", "pos": [610, 60], "size": [210, 58], "flags": {"collapsed": true}, "order": 23, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 186}], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [179]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "ModelSamplingSD3"}, "widgets_values": [8.000000000000002]}, {"id": 146, "type": "SolidMask", "pos": [800, 990], "size": [210, 106], "flags": {}, "order": 19, "mode": 4, "inputs": [{"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 289}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 290}], "outputs": [{"name": "MASK", "type": "MASK", "links": [230]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "SolidMask"}, "widgets_values": [0, 768, 768]}, {"id": 145, "type": "MaskToImage", "pos": [800, 920], "size": [210, 30], "flags": {}, "order": 22, "mode": 4, "inputs": [{"name": "mask", "type": "MASK", "link": 230}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [231]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 134, "type": "ImageBatch", "pos": [800, 840], "size": [210, 46], "flags": {}, "order": 28, "mode": 4, "inputs": [{"name": "image1", "type": "IMAGE", "link": 231}, {"name": "image2", "type": "IMAGE", "link": 239}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [224, 225]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 142, "type": "ImageToMask", "pos": [800, 740], "size": [210, 60], "flags": {}, "order": 31, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 225}], "outputs": [{"name": "MASK", "type": "MASK", "links": [219]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 132, "type": "RepeatImageBatch", "pos": [1060, 830], "size": [210, 60], "flags": {}, "order": 26, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 237}, {"name": "amount", "type": "INT", "widget": {"name": "amount"}, "link": 236}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [238, 239]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "RepeatImageBatch"}, "widgets_values": [80]}, {"id": 129, "type": "MaskToImage", "pos": [1060, 930], "size": [210, 26], "flags": {"collapsed": false}, "order": 21, "mode": 4, "inputs": [{"name": "mask", "type": "MASK", "link": 205}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [237]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 130, "type": "SolidMask", "pos": [1060, 1000], "size": [210, 110], "flags": {}, "order": 18, "mode": 4, "inputs": [{"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 287}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 288}], "outputs": [{"name": "MASK", "type": "MASK", "links": [205]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "SolidMask"}, "widgets_values": [1, 768, 768]}, {"id": 128, "type": "ImageBatch", "pos": [1060, 740], "size": [210, 46], "flags": {}, "order": 27, "mode": 4, "inputs": [{"name": "image1", "type": "IMAGE", "link": 240}, {"name": "image2", "type": "IMAGE", "link": 238}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [212, 241]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 135, "type": "PreviewImage", "pos": [710, 1200], "size": [280, 270], "flags": {}, "order": 30, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 224}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 133, "type": "PreviewImage", "pos": [1000, 1200], "size": [290, 270], "flags": {}, "order": 29, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 212}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 161, "type": "<PERSON>downNote", "pos": [470, 1200], "size": [230, 270], "flags": {}, "order": 4, "mode": 4, "inputs": [], "outputs": [], "title": "Note", "properties": {}, "widgets_values": ["Since VACE supports converting any frame into a video, here we have created a sequence of images with the first frame and the corresponding mask. In this way, we can control the starting frame.\n\n---\n\n由于 VACE 支持任意帧到视频，所以在这里，我们创建了一个带有首帧的序列图像和对应的蒙版，这样我们就可以控制起始帧"], "color": "#432", "bgcolor": "#653"}, {"id": 147, "type": "PrimitiveNode", "pos": [60, 720], "size": [340, 82], "flags": {}, "order": 5, "mode": 4, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "amount"}, "links": [236]}], "properties": {"Run widget replace on values": false}, "widgets_values": [80, "fixed"], "color": "#322", "bgcolor": "#533"}, {"id": 162, "type": "<PERSON>downNote", "pos": [-320, 1080], "size": [340, 280], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "title": "Note", "properties": {}, "widgets_values": ["VACE does not use data with style reference for training. Currently, it only has the functions of object or background reference. Therefore, at \"Load reference image\", you should upload a image with a solid-colored background or a background image. At present, the WanVaceToVideo node only supports the input of a single reference_image image.\n\n---\n\nVACE 并没有使用风格参考的数据去训练，目前只有物体或背景参考的功能，所以在 Load reference image 这里你应该上传背景为纯色的图片，或者背景图，目前 WanVaceToVideo 节点只支持输入单个 reference_image 图片"], "color": "#432", "bgcolor": "#653"}, {"id": 121, "type": "<PERSON>downNote", "pos": [-1200, 10], "size": [470, 800], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tutorial](https://docs.comfy.org/tutorials/video/wan/vace) | [教程](https://docs.comfy.org/zh-CN/tutorials/video/wan/vace)\n\n[Causvid Lora extracted  by <PERSON><PERSON><PERSON>](https://www.reddit.com/r/StableDiffusion/comments/1knuafk/causvid_lora_massive_speedup_for_wan21_made_by/) Thanks to CausVid MIT\n\n##  14B Support 480P 720P\n\n**Diffusion Model**\n- [wan2.1_vace_14B_fp16.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_vace_14B_fp16.safetensors)\n\n**LoRA**\n- [Wan21_CausVid_14B_T2V_lora_rank32.safetensors](https://huggingface.co/Kijai/WanVideo_comfy/blob/main/Wan21_CausVid_14B_T2V_lora_rank32.safetensors)\n\nIt takes about 40 minutes to complete at 81 frames 720P resolution with the RTX 4090 .  \nAfter using Wan21_CausVid_14B_T2V_lora_rank32.safetensors, it only takes about 4 minutes.\n\n## 1.3B Support 480P only\n\n**Diffusion Model**\n- [wan2.1_vace_1.3B_fp16.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_vace_14B_fp16.safetensors)\n\n**LoRA**\n- [Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors](https://huggingface.co/Kijai/WanVideo_comfy/blob/main/Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors)\n \n\n## Other Models\n\n* You may already have these models if you use Wan workflow before.\n\n**VAE**\n- [wan_2.1_vae.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true)\n\n**Text encoders**   Chose one of following model\n- [umt5_xxl_fp16.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp16.safetensors?download=true)\n- [umt5_xxl_fp8_e4m3fn_scaled.safetensors](https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true)\n\n> You can choose between fp16 of fp8; I used fp16 to match what kijai's wrapper is compatible with.\n\nFile save location\n\n```\nComfyUI/\n├── models/\n│   ├── diffusion_models/\n│   │   ├-── wan2.1_vace_14B_fp16.safetensors\n│   │   └─── wan2.1_vace_1.3B_fp16.safetensors \n│   ├── text_encoders/\n│   │   └─── umt5_xxl_fp8_e4m3fn_scaled.safetensors # or fp16\n│   ├── loras/\n│   │   ├── Wan21_CausVid_14B_T2V_lora_rank32.safetensors\n│   │   └── Wan21_CausVid_bidirect2_T2V_1_3B_lora_rank32.safetensors\n│   └── vae/\n│       └──  wan_2.1_vae.safetensors\n```\n"], "color": "#432", "bgcolor": "#653"}, {"id": 73, "type": "LoadImage", "pos": [-290, 690], "size": [274.080078125, 314.00006103515625], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [202]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "LoadImage"}, "widgets_values": ["input.jpg", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 117, "type": "<PERSON>downNote", "pos": [950, 360], "size": [320, 170], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "title": "<PERSON><PERSON><PERSON><PERSON>", "properties": {}, "widgets_values": ["## Default\n\n- steps:20\n- cfg:6.0\n\n## [For CausVid LoRA](https://www.reddit.com/r/StableDiffusion/comments/1knuafk/causvid_lora_massive_speedup_for_wan21_made_by/)\n\n- steps: 2-4\n- cfg: 1.0\n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 158, "type": "PrimitiveNode", "pos": [70, 920], "size": [280, 82], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "width"}, "links": [285, 287, 289]}], "title": "<PERSON><PERSON><PERSON>", "properties": {"Run widget replace on values": false}, "widgets_values": [768, "fixed"], "color": "#322", "bgcolor": "#533"}, {"id": 159, "type": "PrimitiveNode", "pos": [70, 1050], "size": [280, 82], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "height"}, "links": [288, 290, 292]}], "title": "Height", "properties": {"Run widget replace on values": false}, "widgets_values": [768, "fixed"], "color": "#322", "bgcolor": "#533"}, {"id": 114, "type": "VAELoader", "pos": [-290, 540], "size": [350, 58], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "slot_index": 0, "links": [185, 192]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAELoader", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors", "directory": "vae"}]}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 69, "type": "SaveVideo", "pos": [1340, 70], "size": [676.23095703125, 774.23095703125], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 129}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SaveVideo"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 148, "type": "LoadImage", "pos": [480, 750], "size": [274.080078125, 314], "flags": {}, "order": 13, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [240]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "LoadImage"}, "widgets_values": ["input.jpg", "image"]}, {"id": 108, "type": "K<PERSON><PERSON><PERSON>", "pos": [950, 60], "size": [315, 262], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 179}, {"name": "positive", "type": "CONDITIONING", "link": 279}, {"name": "negative", "type": "CONDITIONING", "link": 280}, {"name": "latent_image", "type": "LATENT", "link": 281}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [189]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [552837504311715, "randomize", 4, 1, "uni_pc", "simple", 1]}, {"id": 116, "type": "<PERSON>downNote", "pos": [60, 1200], "size": [210, 110], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [], "title": "About Video Size", "properties": {}, "widgets_values": ["| Model                                                         | 480P | 720P |\n| ------------------------------------------------------------ | ---- | ---- |\n| [VACE-1.3B](https://huggingface.co/Wan-AI/Wan2.1-VACE-1.3B) | ✅   | ❌   |\n| [VACE-14B](https://huggingface.co/Wan-AI/Wan2.1-VACE-14B)   | ✅   | ✅   |"], "color": "#432", "bgcolor": "#653"}, {"id": 68, "type": "CreateVideo", "pos": [1330, -120], "size": [270, 78], "flags": {"collapsed": false}, "order": 36, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 197}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [129]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CreateVideo"}, "widgets_values": [16]}, {"id": 109, "type": "WanVaceToVideo", "pos": [620, 110], "size": [315, 254], "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 183}, {"name": "negative", "type": "CONDITIONING", "link": 184}, {"name": "vae", "type": "VAE", "link": 185}, {"name": "control_video", "shape": 7, "type": "IMAGE", "link": 241}, {"name": "control_masks", "shape": 7, "type": "MASK", "link": 219}, {"name": "reference_image", "shape": 7, "type": "IMAGE", "link": 202}, {"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 285}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 292}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [279]}, {"name": "negative", "type": "CONDITIONING", "links": [280]}, {"name": "latent", "type": "LATENT", "links": [281]}, {"name": "trim_latent", "type": "INT", "links": [282]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "WanVaceToVideo"}, "widgets_values": [768, 768, 81, 1, 1.0000000000000002]}, {"id": 113, "type": "UNETLoader", "pos": [-290, 100], "size": [350, 82], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [187]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "UNETLoader", "models": [{"name": "wan2.1_vace_14B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_vace_14B_fp16.safetensors", "directory": "diffusion_models"}]}, "widgets_values": ["wan2.1_vace_14B_fp16.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 115, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-290, 220], "size": [350, 126], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 187}, {"name": "clip", "type": "CLIP", "link": 188}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [186]}, {"name": "CLIP", "type": "CLIP", "links": [176, 178]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "models": [{"name": "Wan21_CausVid_14B_T2V_lora_rank32.safetensors", "url": "https://huggingface.co/Kijai/WanVideo_comfy/resolve/main/Wan21_CausVid_14B_T2V_lora_rank32.safetensors", "directory": "loras"}]}, "widgets_values": ["Wan21_CausVid_14B_T2V_lora_rank32.safetensors", 0.30000000000000004, 1], "color": "#322", "bgcolor": "#533"}, {"id": 163, "type": "<PERSON>downNote", "pos": [-320, -180], "size": [410, 140], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [], "title": "About CausVid LoRA", "properties": {}, "widgets_values": ["We have added CausVid LoRA by default to achieve acceleration. However, in some cases, the video may shake and become blurry. You might need to test different LoRA intensities to get the best results, which should be between 0.3 and 0.7. If you don't need it, you can use the bypass mode to disable it, and then restore the settings of `KSampler` to the default ones.\n\n\n我们默认添加了  CausVid LoRA 来实现加速，但有些情况下会出现视频抖动和模糊的情况，你可能需要测试不同的 LoRA 强度来获取最好的结果，0.3～0.7 之间。如果你不需要的话，可以使用 bypass 模式禁用它，然后恢复 `KSampler`的设置到默认的设置即可。"], "color": "#432", "bgcolor": "#653"}, {"id": 107, "type": "CLIPTextEncode", "pos": [120, 400], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 178}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [184]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走,"], "color": "#322", "bgcolor": "#533"}, {"id": 105, "type": "CLIPTextEncode", "pos": [120, 60], "size": [420, 290], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 176}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [183]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["An icicle dragon lunges forward, mouth wide open to exhale a stream of icy mist. Ultramarine energy flickers beneath its frost-coated scales as it twists. The camera circles slowly, capturing the swirling ice particles and the backdrop of floating glaciers and frozen nebulae under a cyan-blue filter."], "color": "#232", "bgcolor": "#353"}], "links": [[129, 68, 0, 69, 0, "VIDEO"], [176, 115, 1, 105, 0, "CLIP"], [178, 115, 1, 107, 0, "CLIP"], [179, 110, 0, 108, 0, "MODEL"], [183, 105, 0, 109, 0, "CONDITIONING"], [184, 107, 0, 109, 1, "CONDITIONING"], [185, 114, 0, 109, 2, "VAE"], [186, 115, 0, 110, 0, "MODEL"], [187, 113, 0, 115, 0, "MODEL"], [188, 127, 0, 115, 1, "CLIP"], [189, 108, 0, 118, 0, "LATENT"], [191, 118, 0, 119, 0, "LATENT"], [192, 114, 0, 119, 1, "VAE"], [195, 124, 0, 125, 0, "MODEL"], [196, 127, 0, 125, 1, "CLIP"], [197, 119, 0, 68, 0, "IMAGE"], [198, 119, 0, 70, 0, "IMAGE"], [202, 73, 0, 109, 5, "IMAGE"], [205, 130, 0, 129, 0, "MASK"], [212, 128, 0, 133, 0, "IMAGE"], [219, 142, 0, 109, 4, "MASK"], [224, 134, 0, 135, 0, "IMAGE"], [225, 134, 0, 142, 0, "IMAGE"], [230, 146, 0, 145, 0, "MASK"], [231, 145, 0, 134, 0, "IMAGE"], [236, 147, 0, 132, 1, "INT"], [237, 129, 0, 132, 0, "IMAGE"], [238, 132, 0, 128, 1, "IMAGE"], [239, 132, 0, 134, 1, "IMAGE"], [240, 148, 0, 128, 0, "IMAGE"], [241, 128, 0, 109, 3, "IMAGE"], [279, 109, 0, 108, 1, "CONDITIONING"], [280, 109, 1, 108, 2, "CONDITIONING"], [281, 109, 2, 108, 3, "LATENT"], [282, 109, 3, 118, 1, "INT"], [285, 158, 0, 109, 6, "INT"], [287, 158, 0, 130, 0, "INT"], [288, 159, 0, 130, 1, "INT"], [289, 158, 0, 146, 0, "INT"], [290, 159, 0, 146, 1, "INT"], [292, 159, 0, 109, 7, "INT"]], "groups": [{"id": 4, "title": "Save Video(Mp4)", "bounding": [1320, -20, 720, 900], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Save Video(WebP)", "bounding": [2060, -20, 700, 900], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "Load reference image", "bounding": [-320, 620, 340, 410], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 9, "title": "Prompt", "bounding": [110, -20, 450, 620], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "Sampling & Decoding", "bounding": [580, -20, 720, 620], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 13, "title": "Load models here", "bounding": [-320, -20, 410, 620], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 14, "title": "14B", "bounding": [-300, 20, 370, 340], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 15, "title": "1.3B", "bounding": [-710, -20, 370, 330], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 17, "title": "Video Size", "bounding": [40, 840, 390, 310], "color": "#A88", "font_size": 24, "flags": {}}, {"id": 19, "title": "Value= Length(WanVaceToVideo) - 1", "bounding": [40, 630, 400, 190], "color": "#A88", "font_size": 24, "flags": {}}, {"id": 24, "title": "First Frame control - enable it if you need", "bounding": [450, 620, 850, 870], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 21, "title": "Batch Images", "bounding": [1040, 660, 250, 470], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 22, "title": "Batch Masks", "bounding": [780, 660, 250, 470], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 23, "title": "First Frame", "bounding": [460, 660, 310, 470], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 1.1, "offset": [199.60496009744975, 149.56539080998232]}, "frontendVersion": "1.20.7", "node_versions": {"comfy-core": "0.3.34"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}