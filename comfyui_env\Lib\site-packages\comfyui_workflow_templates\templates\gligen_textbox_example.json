{"id": "4dfc326d-80f7-434c-ae9f-a5fe27b46d12", "revision": 0, "last_node_id": 28, "last_link_id": 85, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [863, 186], "size": [315, 474], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}, {"name": "positive", "type": "CONDITIONING", "link": 77}, {"name": "negative", "type": "CONDITIONING", "link": 57}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [1023216319780679, "randomize", 20, 8, "uni_pc_bh2", "normal", 1]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-224, 128], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [1]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [81, 83, 84, 85]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [80]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "v1-5-pruned-emaonly-fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/stable-diffusion-v1-5-archive/resolve/main/v1-5-pruned-emaonly-fp16.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["v1-5-pruned-emaonly-fp16.safetensors"]}, {"id": 5, "type": "EmptyLatentImage", "pos": [410, 460], "size": [315, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [768, 768, 1]}, {"id": 7, "type": "CLIPTextEncode", "pos": [300, 230], "size": [425.2799987792969, 180.61000061035156], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 81}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [57]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark"]}, {"id": 8, "type": "VAEDecode", "pos": [1209, 188], "size": [210, 46], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 80}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1495, 167], "size": [493.6300048828125, 561.5399780273438], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {}, "widgets_values": ["gligen/testing"]}, {"id": 10, "type": "GLIGEN<PERSON><PERSON>der", "pos": [-230, -70], "size": [390, 60], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "GLIGEN", "type": "GLIGEN", "slot_index": 0, "links": [54, 75]}], "properties": {"Node name for S&R": "GLIGEN<PERSON><PERSON>der", "models": [{"name": "gligen_sd14_textbox_pruned.safetensors", "url": "https://huggingface.co/comfyanonymous/G<PERSON><PERSON><PERSON>_pruned_safetensors/resolve/main/gligen_sd14_textbox_pruned.safetensors?download=true", "directory": "gligen"}]}, "widgets_values": ["gligen_sd14_textbox_pruned.safetensors"]}, {"id": 21, "type": "GLIGENTextBoxApply", "pos": [270, -340], "size": [437.2200012207031, 382.67999267578125], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "conditioning_to", "type": "CONDITIONING", "link": 69}, {"name": "clip", "type": "CLIP", "link": 84}, {"name": "gligen_textbox_model", "type": "GLIGEN", "link": 54}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [65, 78]}], "properties": {"Node name for S&R": "GLIGENTextBoxApply"}, "widgets_values": ["purple galaxy bottle", 192, 304, 176, 272]}, {"id": 24, "type": "CLIPTextEncode", "pos": [-260, -340], "size": [422.8500061035156, 164.30999755859375], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 85}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [69]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["photograph scenery landscape, snow beautiful scenery mountain, glass bottle; purple galaxy bottle; sun"]}, {"id": 27, "type": "GLIGENTextBoxApply", "pos": [770, -340], "size": [437.2200012207031, 382.67999267578125], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "conditioning_to", "type": "CONDITIONING", "link": 78}, {"name": "clip", "type": "CLIP", "link": 83}, {"name": "gligen_textbox_model", "type": "GLIGEN", "link": 75}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [77]}], "properties": {"Node name for S&R": "GLIGENTextBoxApply"}, "widgets_values": ["sun", 144, 144, 416, 16]}, {"id": 28, "type": "<PERSON>downNote", "pos": [-224, 280], "size": [225, 88], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/gligen/)"], "color": "#432", "bgcolor": "#653"}], "links": [[1, 4, 0, 3, 0, "MODEL"], [2, 5, 0, 3, 3, "LATENT"], [7, 3, 0, 8, 0, "LATENT"], [9, 8, 0, 9, 0, "IMAGE"], [54, 10, 0, 21, 2, "GLIGEN"], [57, 7, 0, 3, 2, "CONDITIONING"], [69, 24, 0, 21, 0, "CONDITIONING"], [75, 10, 0, 27, 2, "GLIGEN"], [77, 27, 0, 3, 1, "CONDITIONING"], [78, 21, 0, 27, 0, "CONDITIONING"], [80, 4, 2, 8, 1, "VAE"], [81, 4, 1, 7, 0, "CLIP"], [83, 4, 1, 27, 1, "CLIP"], [84, 4, 1, 21, 1, "CLIP"], [85, 4, 1, 24, 0, "CLIP"]], "groups": [{"id": 1, "title": "Base Prompt", "bounding": [-315, -465, 518, 302], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "GLIGEN (for best results the elements should match some elements in the base prompt)", "bounding": [255, -465, 980, 529], "color": "#A88", "font_size": 24, "flags": {}}], "config": {}, "extra": {"frontendVersion": "1.19.4"}, "version": 0.4}