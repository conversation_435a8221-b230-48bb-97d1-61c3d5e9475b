Metadata-Version: 2.1
Name: transformers
Version: 4.52.4
Summary: State-of-the-art Machine Learning for JAX, PyTorch and TensorFlow
Home-page: https://github.com/huggingface/transformers
Author: The Hugging Face team (past and future) with the help of all our contributors (https://github.com/huggingface/transformers/graphs/contributors)
Author-email: <EMAIL>
License: Apache 2.0 License
Keywords: NLP vision speech deep learning transformer pytorch tensorflow jax BERT GPT-2 Wav2Vec2 ViT
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.9.0
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: filelock
Requires-Dist: huggingface-hub<1.0,>=0.30.0
Requires-Dist: numpy>=1.17
Requires-Dist: packaging>=20.0
Requires-Dist: pyyaml>=5.1
Requires-Dist: regex!=2019.12.17
Requires-Dist: requests
Requires-Dist: tokenizers<0.22,>=0.21
Requires-Dist: safetensors>=0.4.3
Requires-Dist: tqdm>=4.27
Provides-Extra: accelerate
Requires-Dist: accelerate>=0.26.0; extra == "accelerate"
Provides-Extra: all
Requires-Dist: tensorflow<2.16,>2.9; extra == "all"
Requires-Dist: onnxconverter-common; extra == "all"
Requires-Dist: tf2onnx; extra == "all"
Requires-Dist: tensorflow-text<2.16; extra == "all"
Requires-Dist: keras-nlp<0.14.0,>=0.3.1; extra == "all"
Requires-Dist: torch<2.7,>=2.1; extra == "all"
Requires-Dist: accelerate>=0.26.0; extra == "all"
Requires-Dist: jax<=0.4.13,>=0.4.1; extra == "all"
Requires-Dist: jaxlib<=0.4.13,>=0.4.1; extra == "all"
Requires-Dist: flax<=0.7.0,>=0.4.1; extra == "all"
Requires-Dist: optax<=0.1.4,>=0.0.8; extra == "all"
Requires-Dist: scipy<1.13.0; extra == "all"
Requires-Dist: sentencepiece!=0.1.92,>=0.1.91; extra == "all"
Requires-Dist: protobuf; extra == "all"
Requires-Dist: tokenizers<0.22,>=0.21; extra == "all"
Requires-Dist: torchaudio; extra == "all"
Requires-Dist: librosa; extra == "all"
Requires-Dist: pyctcdecode>=0.4.0; extra == "all"
Requires-Dist: phonemizer; extra == "all"
Requires-Dist: kenlm; extra == "all"
Requires-Dist: Pillow<=15.0,>=10.0.1; extra == "all"
Requires-Dist: kernels<0.5,>=0.4.4; extra == "all"
Requires-Dist: optuna; extra == "all"
Requires-Dist: ray[tune]>=2.7.0; extra == "all"
Requires-Dist: sigopt; extra == "all"
Requires-Dist: timm<=1.0.11; extra == "all"
Requires-Dist: torchvision; extra == "all"
Requires-Dist: codecarbon>=2.8.1; extra == "all"
Requires-Dist: av; extra == "all"
Requires-Dist: num2words; extra == "all"
Provides-Extra: audio
Requires-Dist: librosa; extra == "audio"
Requires-Dist: pyctcdecode>=0.4.0; extra == "audio"
Requires-Dist: phonemizer; extra == "audio"
Requires-Dist: kenlm; extra == "audio"
Provides-Extra: benchmark
Requires-Dist: optimum-benchmark>=0.3.0; extra == "benchmark"
Provides-Extra: codecarbon
Requires-Dist: codecarbon>=2.8.1; extra == "codecarbon"
Provides-Extra: deepspeed
Requires-Dist: deepspeed>=0.9.3; extra == "deepspeed"
Requires-Dist: accelerate>=0.26.0; extra == "deepspeed"
Provides-Extra: deepspeed-testing
Requires-Dist: deepspeed>=0.9.3; extra == "deepspeed-testing"
Requires-Dist: accelerate>=0.26.0; extra == "deepspeed-testing"
Requires-Dist: pytest>=7.2.0; extra == "deepspeed-testing"
Requires-Dist: pytest-asyncio; extra == "deepspeed-testing"
Requires-Dist: pytest-rich; extra == "deepspeed-testing"
Requires-Dist: pytest-xdist; extra == "deepspeed-testing"
Requires-Dist: pytest-order; extra == "deepspeed-testing"
Requires-Dist: pytest-rerunfailures; extra == "deepspeed-testing"
Requires-Dist: timeout-decorator; extra == "deepspeed-testing"
Requires-Dist: parameterized; extra == "deepspeed-testing"
Requires-Dist: psutil; extra == "deepspeed-testing"
Requires-Dist: datasets!=2.5.0; extra == "deepspeed-testing"
Requires-Dist: dill<0.3.5; extra == "deepspeed-testing"
Requires-Dist: evaluate>=0.2.0; extra == "deepspeed-testing"
Requires-Dist: pytest-timeout; extra == "deepspeed-testing"
Requires-Dist: ruff==0.11.2; extra == "deepspeed-testing"
Requires-Dist: rouge-score!=0.0.7,!=0.0.8,!=0.1,!=0.1.1; extra == "deepspeed-testing"
Requires-Dist: nltk<=3.8.1; extra == "deepspeed-testing"
Requires-Dist: GitPython<3.1.19; extra == "deepspeed-testing"
Requires-Dist: sacremoses; extra == "deepspeed-testing"
Requires-Dist: rjieba; extra == "deepspeed-testing"
Requires-Dist: beautifulsoup4; extra == "deepspeed-testing"
Requires-Dist: tensorboard; extra == "deepspeed-testing"
Requires-Dist: pydantic; extra == "deepspeed-testing"
Requires-Dist: sentencepiece!=0.1.92,>=0.1.91; extra == "deepspeed-testing"
Requires-Dist: sacrebleu<2.0.0,>=1.4.12; extra == "deepspeed-testing"
Requires-Dist: faiss-cpu; extra == "deepspeed-testing"
Requires-Dist: cookiecutter==1.7.3; extra == "deepspeed-testing"
Requires-Dist: optuna; extra == "deepspeed-testing"
Requires-Dist: protobuf; extra == "deepspeed-testing"
Provides-Extra: dev
Requires-Dist: tensorflow<2.16,>2.9; extra == "dev"
Requires-Dist: onnxconverter-common; extra == "dev"
Requires-Dist: tf2onnx; extra == "dev"
Requires-Dist: tensorflow-text<2.16; extra == "dev"
Requires-Dist: keras-nlp<0.14.0,>=0.3.1; extra == "dev"
Requires-Dist: torch<2.7,>=2.1; extra == "dev"
Requires-Dist: accelerate>=0.26.0; extra == "dev"
Requires-Dist: jax<=0.4.13,>=0.4.1; extra == "dev"
Requires-Dist: jaxlib<=0.4.13,>=0.4.1; extra == "dev"
Requires-Dist: flax<=0.7.0,>=0.4.1; extra == "dev"
Requires-Dist: optax<=0.1.4,>=0.0.8; extra == "dev"
Requires-Dist: scipy<1.13.0; extra == "dev"
Requires-Dist: sentencepiece!=0.1.92,>=0.1.91; extra == "dev"
Requires-Dist: protobuf; extra == "dev"
Requires-Dist: tokenizers<0.22,>=0.21; extra == "dev"
Requires-Dist: torchaudio; extra == "dev"
Requires-Dist: librosa; extra == "dev"
Requires-Dist: pyctcdecode>=0.4.0; extra == "dev"
Requires-Dist: phonemizer; extra == "dev"
Requires-Dist: kenlm; extra == "dev"
Requires-Dist: Pillow<=15.0,>=10.0.1; extra == "dev"
Requires-Dist: kernels<0.5,>=0.4.4; extra == "dev"
Requires-Dist: optuna; extra == "dev"
Requires-Dist: ray[tune]>=2.7.0; extra == "dev"
Requires-Dist: sigopt; extra == "dev"
Requires-Dist: timm<=1.0.11; extra == "dev"
Requires-Dist: torchvision; extra == "dev"
Requires-Dist: codecarbon>=2.8.1; extra == "dev"
Requires-Dist: av; extra == "dev"
Requires-Dist: num2words; extra == "dev"
Requires-Dist: pytest>=7.2.0; extra == "dev"
Requires-Dist: pytest-asyncio; extra == "dev"
Requires-Dist: pytest-rich; extra == "dev"
Requires-Dist: pytest-xdist; extra == "dev"
Requires-Dist: pytest-order; extra == "dev"
Requires-Dist: pytest-rerunfailures; extra == "dev"
Requires-Dist: timeout-decorator; extra == "dev"
Requires-Dist: parameterized; extra == "dev"
Requires-Dist: psutil; extra == "dev"
Requires-Dist: datasets!=2.5.0; extra == "dev"
Requires-Dist: dill<0.3.5; extra == "dev"
Requires-Dist: evaluate>=0.2.0; extra == "dev"
Requires-Dist: pytest-timeout; extra == "dev"
Requires-Dist: ruff==0.11.2; extra == "dev"
Requires-Dist: rouge-score!=0.0.7,!=0.0.8,!=0.1,!=0.1.1; extra == "dev"
Requires-Dist: nltk<=3.8.1; extra == "dev"
Requires-Dist: GitPython<3.1.19; extra == "dev"
Requires-Dist: sacremoses; extra == "dev"
Requires-Dist: rjieba; extra == "dev"
Requires-Dist: beautifulsoup4; extra == "dev"
Requires-Dist: tensorboard; extra == "dev"
Requires-Dist: pydantic; extra == "dev"
Requires-Dist: sacrebleu<2.0.0,>=1.4.12; extra == "dev"
Requires-Dist: faiss-cpu; extra == "dev"
Requires-Dist: cookiecutter==1.7.3; extra == "dev"
Requires-Dist: isort>=5.5.4; extra == "dev"
Requires-Dist: urllib3<2.0.0; extra == "dev"
Requires-Dist: libcst; extra == "dev"
Requires-Dist: rich; extra == "dev"
Requires-Dist: fugashi>=1.0; extra == "dev"
Requires-Dist: ipadic<2.0,>=1.0.0; extra == "dev"
Requires-Dist: unidic-lite>=1.0.7; extra == "dev"
Requires-Dist: unidic>=1.0.2; extra == "dev"
Requires-Dist: sudachipy>=0.6.6; extra == "dev"
Requires-Dist: sudachidict-core>=20220729; extra == "dev"
Requires-Dist: rhoknp<1.3.1,>=1.1.0; extra == "dev"
Requires-Dist: scikit-learn; extra == "dev"
Provides-Extra: dev-tensorflow
Requires-Dist: pytest>=7.2.0; extra == "dev-tensorflow"
Requires-Dist: pytest-asyncio; extra == "dev-tensorflow"
Requires-Dist: pytest-rich; extra == "dev-tensorflow"
Requires-Dist: pytest-xdist; extra == "dev-tensorflow"
Requires-Dist: pytest-order; extra == "dev-tensorflow"
Requires-Dist: pytest-rerunfailures; extra == "dev-tensorflow"
Requires-Dist: timeout-decorator; extra == "dev-tensorflow"
Requires-Dist: parameterized; extra == "dev-tensorflow"
Requires-Dist: psutil; extra == "dev-tensorflow"
Requires-Dist: datasets!=2.5.0; extra == "dev-tensorflow"
Requires-Dist: dill<0.3.5; extra == "dev-tensorflow"
Requires-Dist: evaluate>=0.2.0; extra == "dev-tensorflow"
Requires-Dist: pytest-timeout; extra == "dev-tensorflow"
Requires-Dist: ruff==0.11.2; extra == "dev-tensorflow"
Requires-Dist: rouge-score!=0.0.7,!=0.0.8,!=0.1,!=0.1.1; extra == "dev-tensorflow"
Requires-Dist: nltk<=3.8.1; extra == "dev-tensorflow"
Requires-Dist: GitPython<3.1.19; extra == "dev-tensorflow"
Requires-Dist: sacremoses; extra == "dev-tensorflow"
Requires-Dist: rjieba; extra == "dev-tensorflow"
Requires-Dist: beautifulsoup4; extra == "dev-tensorflow"
Requires-Dist: tensorboard; extra == "dev-tensorflow"
Requires-Dist: pydantic; extra == "dev-tensorflow"
Requires-Dist: sentencepiece!=0.1.92,>=0.1.91; extra == "dev-tensorflow"
Requires-Dist: sacrebleu<2.0.0,>=1.4.12; extra == "dev-tensorflow"
Requires-Dist: faiss-cpu; extra == "dev-tensorflow"
Requires-Dist: cookiecutter==1.7.3; extra == "dev-tensorflow"
Requires-Dist: tensorflow<2.16,>2.9; extra == "dev-tensorflow"
Requires-Dist: onnxconverter-common; extra == "dev-tensorflow"
Requires-Dist: tf2onnx; extra == "dev-tensorflow"
Requires-Dist: tensorflow-text<2.16; extra == "dev-tensorflow"
Requires-Dist: keras-nlp<0.14.0,>=0.3.1; extra == "dev-tensorflow"
Requires-Dist: protobuf; extra == "dev-tensorflow"
Requires-Dist: tokenizers<0.22,>=0.21; extra == "dev-tensorflow"
Requires-Dist: Pillow<=15.0,>=10.0.1; extra == "dev-tensorflow"
Requires-Dist: isort>=5.5.4; extra == "dev-tensorflow"
Requires-Dist: urllib3<2.0.0; extra == "dev-tensorflow"
Requires-Dist: libcst; extra == "dev-tensorflow"
Requires-Dist: rich; extra == "dev-tensorflow"
Requires-Dist: scikit-learn; extra == "dev-tensorflow"
Requires-Dist: onnxruntime>=1.4.0; extra == "dev-tensorflow"
Requires-Dist: onnxruntime-tools>=1.4.2; extra == "dev-tensorflow"
Requires-Dist: librosa; extra == "dev-tensorflow"
Requires-Dist: pyctcdecode>=0.4.0; extra == "dev-tensorflow"
Requires-Dist: phonemizer; extra == "dev-tensorflow"
Requires-Dist: kenlm; extra == "dev-tensorflow"
Provides-Extra: dev-torch
Requires-Dist: pytest>=7.2.0; extra == "dev-torch"
Requires-Dist: pytest-asyncio; extra == "dev-torch"
Requires-Dist: pytest-rich; extra == "dev-torch"
Requires-Dist: pytest-xdist; extra == "dev-torch"
Requires-Dist: pytest-order; extra == "dev-torch"
Requires-Dist: pytest-rerunfailures; extra == "dev-torch"
Requires-Dist: timeout-decorator; extra == "dev-torch"
Requires-Dist: parameterized; extra == "dev-torch"
Requires-Dist: psutil; extra == "dev-torch"
Requires-Dist: datasets!=2.5.0; extra == "dev-torch"
Requires-Dist: dill<0.3.5; extra == "dev-torch"
Requires-Dist: evaluate>=0.2.0; extra == "dev-torch"
Requires-Dist: pytest-timeout; extra == "dev-torch"
Requires-Dist: ruff==0.11.2; extra == "dev-torch"
Requires-Dist: rouge-score!=0.0.7,!=0.0.8,!=0.1,!=0.1.1; extra == "dev-torch"
Requires-Dist: nltk<=3.8.1; extra == "dev-torch"
Requires-Dist: GitPython<3.1.19; extra == "dev-torch"
Requires-Dist: sacremoses; extra == "dev-torch"
Requires-Dist: rjieba; extra == "dev-torch"
Requires-Dist: beautifulsoup4; extra == "dev-torch"
Requires-Dist: tensorboard; extra == "dev-torch"
Requires-Dist: pydantic; extra == "dev-torch"
Requires-Dist: sentencepiece!=0.1.92,>=0.1.91; extra == "dev-torch"
Requires-Dist: sacrebleu<2.0.0,>=1.4.12; extra == "dev-torch"
Requires-Dist: faiss-cpu; extra == "dev-torch"
Requires-Dist: cookiecutter==1.7.3; extra == "dev-torch"
Requires-Dist: torch<2.7,>=2.1; extra == "dev-torch"
Requires-Dist: accelerate>=0.26.0; extra == "dev-torch"
Requires-Dist: protobuf; extra == "dev-torch"
Requires-Dist: tokenizers<0.22,>=0.21; extra == "dev-torch"
Requires-Dist: torchaudio; extra == "dev-torch"
Requires-Dist: librosa; extra == "dev-torch"
Requires-Dist: pyctcdecode>=0.4.0; extra == "dev-torch"
Requires-Dist: phonemizer; extra == "dev-torch"
Requires-Dist: kenlm; extra == "dev-torch"
Requires-Dist: Pillow<=15.0,>=10.0.1; extra == "dev-torch"
Requires-Dist: kernels<0.5,>=0.4.4; extra == "dev-torch"
Requires-Dist: optuna; extra == "dev-torch"
Requires-Dist: ray[tune]>=2.7.0; extra == "dev-torch"
Requires-Dist: sigopt; extra == "dev-torch"
Requires-Dist: timm<=1.0.11; extra == "dev-torch"
Requires-Dist: torchvision; extra == "dev-torch"
Requires-Dist: codecarbon>=2.8.1; extra == "dev-torch"
Requires-Dist: isort>=5.5.4; extra == "dev-torch"
Requires-Dist: urllib3<2.0.0; extra == "dev-torch"
Requires-Dist: libcst; extra == "dev-torch"
Requires-Dist: rich; extra == "dev-torch"
Requires-Dist: fugashi>=1.0; extra == "dev-torch"
Requires-Dist: ipadic<2.0,>=1.0.0; extra == "dev-torch"
Requires-Dist: unidic-lite>=1.0.7; extra == "dev-torch"
Requires-Dist: unidic>=1.0.2; extra == "dev-torch"
Requires-Dist: sudachipy>=0.6.6; extra == "dev-torch"
Requires-Dist: sudachidict-core>=20220729; extra == "dev-torch"
Requires-Dist: rhoknp<1.3.1,>=1.1.0; extra == "dev-torch"
Requires-Dist: scikit-learn; extra == "dev-torch"
Requires-Dist: onnxruntime>=1.4.0; extra == "dev-torch"
Requires-Dist: onnxruntime-tools>=1.4.2; extra == "dev-torch"
Requires-Dist: num2words; extra == "dev-torch"
Provides-Extra: flax
Requires-Dist: jax<=0.4.13,>=0.4.1; extra == "flax"
Requires-Dist: jaxlib<=0.4.13,>=0.4.1; extra == "flax"
Requires-Dist: flax<=0.7.0,>=0.4.1; extra == "flax"
Requires-Dist: optax<=0.1.4,>=0.0.8; extra == "flax"
Requires-Dist: scipy<1.13.0; extra == "flax"
Provides-Extra: flax-speech
Requires-Dist: librosa; extra == "flax-speech"
Requires-Dist: pyctcdecode>=0.4.0; extra == "flax-speech"
Requires-Dist: phonemizer; extra == "flax-speech"
Requires-Dist: kenlm; extra == "flax-speech"
Provides-Extra: ftfy
Requires-Dist: ftfy; extra == "ftfy"
Provides-Extra: hf_xet
Requires-Dist: hf-xet; extra == "hf-xet"
Provides-Extra: hub-kernels
Requires-Dist: kernels<0.5,>=0.4.4; extra == "hub-kernels"
Provides-Extra: integrations
Requires-Dist: kernels<0.5,>=0.4.4; extra == "integrations"
Requires-Dist: optuna; extra == "integrations"
Requires-Dist: ray[tune]>=2.7.0; extra == "integrations"
Requires-Dist: sigopt; extra == "integrations"
Provides-Extra: ja
Requires-Dist: fugashi>=1.0; extra == "ja"
Requires-Dist: ipadic<2.0,>=1.0.0; extra == "ja"
Requires-Dist: unidic-lite>=1.0.7; extra == "ja"
Requires-Dist: unidic>=1.0.2; extra == "ja"
Requires-Dist: sudachipy>=0.6.6; extra == "ja"
Requires-Dist: sudachidict-core>=20220729; extra == "ja"
Requires-Dist: rhoknp<1.3.1,>=1.1.0; extra == "ja"
Provides-Extra: modelcreation
Requires-Dist: cookiecutter==1.7.3; extra == "modelcreation"
Provides-Extra: natten
Requires-Dist: natten<0.15.0,>=0.14.6; extra == "natten"
Provides-Extra: num2words
Requires-Dist: num2words; extra == "num2words"
Provides-Extra: onnx
Requires-Dist: onnxconverter-common; extra == "onnx"
Requires-Dist: tf2onnx; extra == "onnx"
Requires-Dist: onnxruntime>=1.4.0; extra == "onnx"
Requires-Dist: onnxruntime-tools>=1.4.2; extra == "onnx"
Provides-Extra: onnxruntime
Requires-Dist: onnxruntime>=1.4.0; extra == "onnxruntime"
Requires-Dist: onnxruntime-tools>=1.4.2; extra == "onnxruntime"
Provides-Extra: optuna
Requires-Dist: optuna; extra == "optuna"
Provides-Extra: quality
Requires-Dist: datasets!=2.5.0; extra == "quality"
Requires-Dist: isort>=5.5.4; extra == "quality"
Requires-Dist: ruff==0.11.2; extra == "quality"
Requires-Dist: GitPython<3.1.19; extra == "quality"
Requires-Dist: urllib3<2.0.0; extra == "quality"
Requires-Dist: libcst; extra == "quality"
Requires-Dist: rich; extra == "quality"
Provides-Extra: ray
Requires-Dist: ray[tune]>=2.7.0; extra == "ray"
Provides-Extra: retrieval
Requires-Dist: faiss-cpu; extra == "retrieval"
Requires-Dist: datasets!=2.5.0; extra == "retrieval"
Provides-Extra: ruff
Requires-Dist: ruff==0.11.2; extra == "ruff"
Provides-Extra: sagemaker
Requires-Dist: sagemaker>=2.31.0; extra == "sagemaker"
Provides-Extra: sentencepiece
Requires-Dist: sentencepiece!=0.1.92,>=0.1.91; extra == "sentencepiece"
Requires-Dist: protobuf; extra == "sentencepiece"
Provides-Extra: serving
Requires-Dist: pydantic; extra == "serving"
Requires-Dist: uvicorn; extra == "serving"
Requires-Dist: fastapi; extra == "serving"
Requires-Dist: starlette; extra == "serving"
Provides-Extra: sigopt
Requires-Dist: sigopt; extra == "sigopt"
Provides-Extra: sklearn
Requires-Dist: scikit-learn; extra == "sklearn"
Provides-Extra: speech
Requires-Dist: torchaudio; extra == "speech"
Requires-Dist: librosa; extra == "speech"
Requires-Dist: pyctcdecode>=0.4.0; extra == "speech"
Requires-Dist: phonemizer; extra == "speech"
Requires-Dist: kenlm; extra == "speech"
Provides-Extra: testing
Requires-Dist: pytest>=7.2.0; extra == "testing"
Requires-Dist: pytest-asyncio; extra == "testing"
Requires-Dist: pytest-rich; extra == "testing"
Requires-Dist: pytest-xdist; extra == "testing"
Requires-Dist: pytest-order; extra == "testing"
Requires-Dist: pytest-rerunfailures; extra == "testing"
Requires-Dist: timeout-decorator; extra == "testing"
Requires-Dist: parameterized; extra == "testing"
Requires-Dist: psutil; extra == "testing"
Requires-Dist: datasets!=2.5.0; extra == "testing"
Requires-Dist: dill<0.3.5; extra == "testing"
Requires-Dist: evaluate>=0.2.0; extra == "testing"
Requires-Dist: pytest-timeout; extra == "testing"
Requires-Dist: ruff==0.11.2; extra == "testing"
Requires-Dist: rouge-score!=0.0.7,!=0.0.8,!=0.1,!=0.1.1; extra == "testing"
Requires-Dist: nltk<=3.8.1; extra == "testing"
Requires-Dist: GitPython<3.1.19; extra == "testing"
Requires-Dist: sacremoses; extra == "testing"
Requires-Dist: rjieba; extra == "testing"
Requires-Dist: beautifulsoup4; extra == "testing"
Requires-Dist: tensorboard; extra == "testing"
Requires-Dist: pydantic; extra == "testing"
Requires-Dist: sentencepiece!=0.1.92,>=0.1.91; extra == "testing"
Requires-Dist: sacrebleu<2.0.0,>=1.4.12; extra == "testing"
Requires-Dist: faiss-cpu; extra == "testing"
Requires-Dist: cookiecutter==1.7.3; extra == "testing"
Provides-Extra: tf
Requires-Dist: tensorflow<2.16,>2.9; extra == "tf"
Requires-Dist: onnxconverter-common; extra == "tf"
Requires-Dist: tf2onnx; extra == "tf"
Requires-Dist: tensorflow-text<2.16; extra == "tf"
Requires-Dist: keras-nlp<0.14.0,>=0.3.1; extra == "tf"
Provides-Extra: tf-cpu
Requires-Dist: keras<2.16,>2.9; extra == "tf-cpu"
Requires-Dist: tensorflow-cpu<2.16,>2.9; extra == "tf-cpu"
Requires-Dist: onnxconverter-common; extra == "tf-cpu"
Requires-Dist: tf2onnx; extra == "tf-cpu"
Requires-Dist: tensorflow-text<2.16; extra == "tf-cpu"
Requires-Dist: keras-nlp<0.14.0,>=0.3.1; extra == "tf-cpu"
Requires-Dist: tensorflow-probability<0.24; extra == "tf-cpu"
Provides-Extra: tf-speech
Requires-Dist: librosa; extra == "tf-speech"
Requires-Dist: pyctcdecode>=0.4.0; extra == "tf-speech"
Requires-Dist: phonemizer; extra == "tf-speech"
Requires-Dist: kenlm; extra == "tf-speech"
Provides-Extra: tiktoken
Requires-Dist: tiktoken; extra == "tiktoken"
Requires-Dist: blobfile; extra == "tiktoken"
Provides-Extra: timm
Requires-Dist: timm<=1.0.11; extra == "timm"
Provides-Extra: tokenizers
Requires-Dist: tokenizers<0.22,>=0.21; extra == "tokenizers"
Provides-Extra: torch
Requires-Dist: torch<2.7,>=2.1; extra == "torch"
Requires-Dist: accelerate>=0.26.0; extra == "torch"
Provides-Extra: torch-speech
Requires-Dist: torchaudio; extra == "torch-speech"
Requires-Dist: librosa; extra == "torch-speech"
Requires-Dist: pyctcdecode>=0.4.0; extra == "torch-speech"
Requires-Dist: phonemizer; extra == "torch-speech"
Requires-Dist: kenlm; extra == "torch-speech"
Provides-Extra: torch-vision
Requires-Dist: torchvision; extra == "torch-vision"
Requires-Dist: Pillow<=15.0,>=10.0.1; extra == "torch-vision"
Provides-Extra: torchhub
Requires-Dist: filelock; extra == "torchhub"
Requires-Dist: huggingface-hub<1.0,>=0.30.0; extra == "torchhub"
Requires-Dist: importlib-metadata; extra == "torchhub"
Requires-Dist: numpy>=1.17; extra == "torchhub"
Requires-Dist: packaging>=20.0; extra == "torchhub"
Requires-Dist: protobuf; extra == "torchhub"
Requires-Dist: regex!=2019.12.17; extra == "torchhub"
Requires-Dist: requests; extra == "torchhub"
Requires-Dist: sentencepiece!=0.1.92,>=0.1.91; extra == "torchhub"
Requires-Dist: torch<2.7,>=2.1; extra == "torchhub"
Requires-Dist: tokenizers<0.22,>=0.21; extra == "torchhub"
Requires-Dist: tqdm>=4.27; extra == "torchhub"
Provides-Extra: video
Requires-Dist: av; extra == "video"
Provides-Extra: vision
Requires-Dist: Pillow<=15.0,>=10.0.1; extra == "vision"

<!---
Copyright 2020 The HuggingFace Team. All rights reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<p align="center">
  <picture>
    <source media="(prefers-color-scheme: dark)" srcset="https://huggingface.co/datasets/huggingface/documentation-images/raw/main/transformers-logo-dark.svg">
    <source media="(prefers-color-scheme: light)" srcset="https://huggingface.co/datasets/huggingface/documentation-images/raw/main/transformers-logo-light.svg">
    <img alt="Hugging Face Transformers Library" src="https://huggingface.co/datasets/huggingface/documentation-images/raw/main/transformers-logo-light.svg" width="352" height="59" style="max-width: 100%;">
  </picture>
  <br/>
  <br/>
</p>

<p align="center">
    <a href="https://huggingface.com/models"><img alt="Checkpoints on Hub" src="https://img.shields.io/endpoint?url=https://huggingface.co/api/shields/models&color=brightgreen"></a>
    <a href="https://circleci.com/gh/huggingface/transformers"><img alt="Build" src="https://img.shields.io/circleci/build/github/huggingface/transformers/main"></a>
    <a href="https://github.com/huggingface/transformers/blob/main/LICENSE"><img alt="GitHub" src="https://img.shields.io/github/license/huggingface/transformers.svg?color=blue"></a>
    <a href="https://huggingface.co/docs/transformers/index"><img alt="Documentation" src="https://img.shields.io/website/http/huggingface.co/docs/transformers/index.svg?down_color=red&down_message=offline&up_message=online"></a>
    <a href="https://github.com/huggingface/transformers/releases"><img alt="GitHub release" src="https://img.shields.io/github/release/huggingface/transformers.svg"></a>
    <a href="https://github.com/huggingface/transformers/blob/main/CODE_OF_CONDUCT.md"><img alt="Contributor Covenant" src="https://img.shields.io/badge/Contributor%20Covenant-v2.0%20adopted-ff69b4.svg"></a>
    <a href="https://zenodo.org/badge/latestdoi/155220641"><img src="https://zenodo.org/badge/155220641.svg" alt="DOI"></a>
</p>

<h4 align="center">
    <p>
        <b>English</b> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_zh-hans.md">简体中文</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_zh-hant.md">繁體中文</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_ko.md">한국어</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_es.md">Español</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_ja.md">日本語</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_hd.md">हिन्दी</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_ru.md">Русский</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_pt-br.md">Рortuguês</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_te.md">తెలుగు</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_fr.md">Français</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_de.md">Deutsch</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_vi.md">Tiếng Việt</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_ar.md">العربية</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_ur.md">اردو</a> |
    </p>
</h4>

<h3 align="center">
    <p>State-of-the-art pretrained models for inference and training</p>
</h3>

<h3 align="center">
    <a href="https://hf.co/course"><img src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/course_banner.png"></a>
</h3>

Transformers is a library of pretrained text, computer vision, audio, video, and multimodal models for inference and training. Use Transformers to fine-tune models on your data, build inference applications, and for generative AI use cases across multiple modalities.

There are over 500K+ Transformers [model checkpoints](https://huggingface.co/models?library=transformers&sort=trending) on the [Hugging Face Hub](https://huggingface.com/models) you can use.

Explore the [Hub](https://huggingface.com/) today to find a model and use Transformers to help you get started right away.

## Installation

Transformers works with Python 3.9+ [PyTorch](https://pytorch.org/get-started/locally/) 2.1+, [TensorFlow](https://www.tensorflow.org/install/pip) 2.6+, and [Flax](https://flax.readthedocs.io/en/latest/) 0.4.1+.

Create and activate a virtual environment with [venv](https://docs.python.org/3/library/venv.html) or [uv](https://docs.astral.sh/uv/), a fast Rust-based Python package and project manager.

```py
# venv
python -m venv .my-env
source .my-env/bin/activate
# uv
uv venv .my-env
source .my-env/bin/activate
```

Install Transformers in your virtual environment.

```py
# pip
pip install "transformers[torch]"

# uv
uv pip install "transformers[torch]"
```

Install Transformers from source if you want the latest changes in the library or are interested in contributing. However, the *latest* version may not be stable. Feel free to open an [issue](https://github.com/huggingface/transformers/issues) if you encounter an error.

```shell
git clone https://github.com/huggingface/transformers.git
cd transformers

# pip
pip install .[torch]

# uv
uv pip install .[torch]
```

## Quickstart

Get started with Transformers right away with the [Pipeline](https://huggingface.co/docs/transformers/pipeline_tutorial) API. The `Pipeline` is a high-level inference class that supports text, audio, vision, and multimodal tasks. It handles preprocessing the input and returns the appropriate output.

Instantiate a pipeline and specify model to use for text generation. The model is downloaded and cached so you can easily reuse it again. Finally, pass some text to prompt the model.

```py
from transformers import pipeline

pipeline = pipeline(task="text-generation", model="Qwen/Qwen2.5-1.5B")
pipeline("the secret to baking a really good cake is ")
[{'generated_text': 'the secret to baking a really good cake is 1) to use the right ingredients and 2) to follow the recipe exactly. the recipe for the cake is as follows: 1 cup of sugar, 1 cup of flour, 1 cup of milk, 1 cup of butter, 1 cup of eggs, 1 cup of chocolate chips. if you want to make 2 cakes, how much sugar do you need? To make 2 cakes, you will need 2 cups of sugar.'}]
```

To chat with a model, the usage pattern is the same. The only difference is you need to construct a chat history (the input to `Pipeline`) between you and the system.

> [!TIP]
> You can also chat with a model directly from the command line.
> ```shell
> transformers chat Qwen/Qwen2.5-0.5B-Instruct
> ```

```py
import torch
from transformers import pipeline

chat = [
    {"role": "system", "content": "You are a sassy, wise-cracking robot as imagined by Hollywood circa 1986."},
    {"role": "user", "content": "Hey, can you tell me any fun things to do in New York?"}
]

pipeline = pipeline(task="text-generation", model="meta-llama/Meta-Llama-3-8B-Instruct", torch_dtype=torch.bfloat16, device_map="auto")
response = pipeline(chat, max_new_tokens=512)
print(response[0]["generated_text"][-1]["content"])
```

Expand the examples below to see how `Pipeline` works for different modalities and tasks.

<details>
<summary>Automatic speech recognition</summary>

```py
from transformers import pipeline

pipeline = pipeline(task="automatic-speech-recognition", model="openai/whisper-large-v3")
pipeline("https://huggingface.co/datasets/Narsil/asr_dummy/resolve/main/mlk.flac")
{'text': ' I have a dream that one day this nation will rise up and live out the true meaning of its creed.'}
```

</details>

<details>
<summary>Image classification</summary>

<h3 align="center">
    <a><img src="https://huggingface.co/datasets/Narsil/image_dummy/raw/main/parrots.png"></a>
</h3>

```py
from transformers import pipeline

pipeline = pipeline(task="image-classification", model="facebook/dinov2-small-imagenet1k-1-layer")
pipeline("https://huggingface.co/datasets/Narsil/image_dummy/raw/main/parrots.png")
[{'label': 'macaw', 'score': 0.997848391532898},
 {'label': 'sulphur-crested cockatoo, Kakatoe galerita, Cacatua galerita',
  'score': 0.0016551691805943847},
 {'label': 'lorikeet', 'score': 0.00018523589824326336},
 {'label': 'African grey, African gray, Psittacus erithacus',
  'score': 7.85409429227002e-05},
 {'label': 'quail', 'score': 5.502637941390276e-05}]
```

</details>

<details>
<summary>Visual question answering</summary>


<h3 align="center">
    <a><img src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/transformers/tasks/idefics-few-shot.jpg"></a>
</h3>

```py
from transformers import pipeline

pipeline = pipeline(task="visual-question-answering", model="Salesforce/blip-vqa-base")
pipeline(
    image="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/transformers/tasks/idefics-few-shot.jpg",
    question="What is in the image?",
)
[{'answer': 'statue of liberty'}]
```

</details>

## Why should I use Transformers?

1. Easy-to-use state-of-the-art models:
    - High performance on natural language understanding & generation, computer vision, audio, video, and multimodal tasks.
    - Low barrier to entry for researchers, engineers, and developers.
    - Few user-facing abstractions with just three classes to learn.
    - A unified API for using all our pretrained models.

1. Lower compute costs, smaller carbon footprint:
    - Share trained models instead of training from scratch.
    - Reduce compute time and production costs.
    - Dozens of model architectures with 1M+ pretrained checkpoints across all modalities.

1. Choose the right framework for every part of a models lifetime:
    - Train state-of-the-art models in 3 lines of code.
    - Move a single model between PyTorch/JAX/TF2.0 frameworks at will.
    - Pick the right framework for training, evaluation, and production.

1. Easily customize a model or an example to your needs:
    - We provide examples for each architecture to reproduce the results published by its original authors.
    - Model internals are exposed as consistently as possible.
    - Model files can be used independently of the library for quick experiments.

<a target="_blank" href="https://huggingface.co/enterprise">
    <img alt="Hugging Face Enterprise Hub" src="https://github.com/user-attachments/assets/247fb16d-d251-4583-96c4-d3d76dda4925">
</a><br>

## Why shouldn't I use Transformers?

- This library is not a modular toolbox of building blocks for neural nets. The code in the model files is not refactored with additional abstractions on purpose, so that researchers can quickly iterate on each of the models without diving into additional abstractions/files.
- The training API is optimized to work with PyTorch models provided by Transformers. For generic machine learning loops, you should use another library like [Accelerate](https://huggingface.co/docs/accelerate).
- The [example scripts]((https://github.com/huggingface/transformers/tree/main/examples)) are only *examples*. They may not necessarily work out-of-the-box on your specific use case and you'll need to adapt the code for it to work.

## 100 projects using Transformers

Transformers is more than a toolkit to use pretrained models, it's a community of projects built around it and the
Hugging Face Hub. We want Transformers to enable developers, researchers, students, professors, engineers, and anyone
else to build their dream projects.

In order to celebrate Transformers 100,000 stars, we wanted to put the spotlight on the
community with the [awesome-transformers](./awesome-transformers.md) page which lists 100
incredible projects built with Transformers.

If you own or use a project that you believe should be part of the list, please open a PR to add it!

## Example models

You can test most of our models directly on their [Hub model pages](https://huggingface.co/models).

Expand each modality below to see a few example models for various use cases.

<details>
<summary>Audio</summary>

- Audio classification with [Whisper](https://huggingface.co/openai/whisper-large-v3-turbo)
- Automatic speech recognition with [Moonshine](https://huggingface.co/UsefulSensors/moonshine)
- Keyword spotting with [Wav2Vec2](https://huggingface.co/superb/wav2vec2-base-superb-ks)
- Speech to speech generation with [Moshi](https://huggingface.co/kyutai/moshiko-pytorch-bf16)
- Text to audio with [MusicGen](https://huggingface.co/facebook/musicgen-large)
- Text to speech with [Bark](https://huggingface.co/suno/bark)

</details>

<details>
<summary>Computer vision</summary>

- Automatic mask generation with [SAM](https://huggingface.co/facebook/sam-vit-base)
- Depth estimation with [DepthPro](https://huggingface.co/apple/DepthPro-hf)
- Image classification with [DINO v2](https://huggingface.co/facebook/dinov2-base)
- Keypoint detection with [SuperGlue](https://huggingface.co/magic-leap-community/superglue_outdoor)
- Keypoint matching with [SuperGlue](https://huggingface.co/magic-leap-community/superglue)
- Object detection with [RT-DETRv2](https://huggingface.co/PekingU/rtdetr_v2_r50vd)
- Pose Estimation with [VitPose](https://huggingface.co/usyd-community/vitpose-base-simple)
- Universal segmentation with [OneFormer](https://huggingface.co/shi-labs/oneformer_ade20k_swin_large)
- Video classification with [VideoMAE](https://huggingface.co/MCG-NJU/videomae-large)

</details>

<details>
<summary>Multimodal</summary>

- Audio or text to text with [Qwen2-Audio](https://huggingface.co/Qwen/Qwen2-Audio-7B)
- Document question answering with [LayoutLMv3](https://huggingface.co/microsoft/layoutlmv3-base)
- Image or text to text with [Qwen-VL](https://huggingface.co/Qwen/Qwen2.5-VL-3B-Instruct)
- Image captioning [BLIP-2](https://huggingface.co/Salesforce/blip2-opt-2.7b)
- OCR-based document understanding with [GOT-OCR2](https://huggingface.co/stepfun-ai/GOT-OCR-2.0-hf)
- Table question answering with [TAPAS](https://huggingface.co/google/tapas-base)
- Unified multimodal understanding and generation with [Emu3](https://huggingface.co/BAAI/Emu3-Gen)
- Vision to text with [Llava-OneVision](https://huggingface.co/llava-hf/llava-onevision-qwen2-0.5b-ov-hf)
- Visual question answering with [Llava](https://huggingface.co/llava-hf/llava-1.5-7b-hf)
- Visual referring expression segmentation with [Kosmos-2](https://huggingface.co/microsoft/kosmos-2-patch14-224)

</details>

<details>
<summary>NLP</summary>

- Masked word completion with [ModernBERT](https://huggingface.co/answerdotai/ModernBERT-base)
- Named entity recognition with [Gemma](https://huggingface.co/google/gemma-2-2b)
- Question answering with [Mixtral](https://huggingface.co/mistralai/Mixtral-8x7B-v0.1)
- Summarization with [BART](https://huggingface.co/facebook/bart-large-cnn)
- Translation with [T5](https://huggingface.co/google-t5/t5-base)
- Text generation with [Llama](https://huggingface.co/meta-llama/Llama-3.2-1B)
- Text classification with [Qwen](https://huggingface.co/Qwen/Qwen2.5-0.5B)

</details>

## Citation

We now have a [paper](https://www.aclweb.org/anthology/2020.emnlp-demos.6/) you can cite for the 🤗 Transformers library:
```bibtex
@inproceedings{wolf-etal-2020-transformers,
    title = "Transformers: State-of-the-Art Natural Language Processing",
    author = "Thomas Wolf and Lysandre Debut and Victor Sanh and Julien Chaumond and Clement Delangue and Anthony Moi and Pierric Cistac and Tim Rault and Rémi Louf and Morgan Funtowicz and Joe Davison and Sam Shleifer and Patrick von Platen and Clara Ma and Yacine Jernite and Julien Plu and Canwen Xu and Teven Le Scao and Sylvain Gugger and Mariama Drame and Quentin Lhoest and Alexander M. Rush",
    booktitle = "Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing: System Demonstrations",
    month = oct,
    year = "2020",
    address = "Online",
    publisher = "Association for Computational Linguistics",
    url = "https://www.aclweb.org/anthology/2020.emnlp-demos.6",
    pages = "38--45"
}
```
