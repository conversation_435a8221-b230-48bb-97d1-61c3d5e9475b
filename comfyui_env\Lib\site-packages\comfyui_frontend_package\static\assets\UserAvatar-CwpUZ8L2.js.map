{"version": 3, "file": "UserAvatar-CwpUZ8L2.js", "sources": ["../../src/components/common/UserAvatar.vue"], "sourcesContent": ["<template>\n  <Avatar\n    :image=\"photoUrl ?? undefined\"\n    :icon=\"hasAvatar ? undefined : 'pi pi-user'\"\n    shape=\"circle\"\n    :aria-label=\"ariaLabel ?? $t('auth.login.userAvatar')\"\n    @error=\"handleImageError\"\n  />\n</template>\n\n<script setup lang=\"ts\">\nimport Avatar from 'primevue/avatar'\nimport { computed, ref } from 'vue'\n\nconst { photoUrl, ariaLabel } = defineProps<{\n  photoUrl?: string | null\n  ariaLabel?: string\n}>()\n\nconst imageError = ref(false)\nconst handleImageError = () => {\n  imageError.value = true\n}\nconst hasAvatar = computed(() => photoUrl && !imageError.value)\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;AAmBM,UAAA,aAAa,IAAI,KAAK;AAC5B,UAAM,mBAAmB,6BAAM;AAC7B,iBAAW,QAAQ;AAAA,IAAA,GADI;AAGzB,UAAM,YAAY,SAAS,MAAM,QAAA,YAAY,CAAC,WAAW,KAAK;;;;;;;;;;;;"}