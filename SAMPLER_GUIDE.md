# ComfyUI 采样器使用指南

## 🚨 问题解决

您遇到的错误是因为采样器名称不正确。ComfyUI 中的采样器名称与其他 UI 略有不同。

### ❌ 错误的采样器名称
- `euler_a` → 应该是 `euler_ancestral`
- `dpm++_2m` → 应该是 `dpmpp_2m`

## ✅ 可用的采样器列表

### 🏆 推荐采样器（按质量和速度排序）

#### 1. **dpmpp_2m** ⭐⭐⭐⭐⭐
- **用途**: 最佳平衡，高质量
- **速度**: 快
- **步数**: 15-25 步
- **特点**: 最受欢迎，质量稳定

#### 2. **euler_ancestral** ⭐⭐⭐⭐
- **用途**: 创意性强，变化丰富
- **速度**: 快
- **步数**: 20-30 步
- **特点**: 每次生成结果差异较大

#### 3. **euler** ⭐⭐⭐⭐
- **用途**: 稳定，可重现
- **速度**: 快
- **步数**: 20-30 步
- **特点**: 相同种子产生相同结果

#### 4. **dpmpp_2m_karras** ⭐⭐⭐⭐⭐
- **用途**: 高质量，细节丰富
- **速度**: 中等
- **步数**: 15-20 步
- **特点**: 使用 Karras 噪声调度

#### 5. **ddim** ⭐⭐⭐
- **用途**: 经典采样器
- **速度**: 中等
- **步数**: 25-50 步
- **特点**: 稳定但需要更多步数

### 🔧 完整采样器列表

```
基础采样器:
- euler
- euler_ancestral
- heun
- dpm_2
- dpm_2_ancestral
- lms

DPM++ 系列 (推荐):
- dpmpp_2m
- dpmpp_2m_sde
- dpmpp_2s_ancestral
- dpmpp_3m_sde

高级采样器:
- ddim
- ddpm
- uni_pc
- lcm (快速采样)

GPU 优化版本:
- dpmpp_sde_gpu
- dpmpp_2m_sde_gpu
- dpmpp_3m_sde_gpu
```

## 📋 调度器 (Scheduler) 选项

### 常用调度器:
- **normal**: 标准线性调度
- **karras**: Karras 噪声调度（推荐）
- **exponential**: 指数调度
- **sgm_uniform**: SGM 均匀调度
- **simple**: 简单调度
- **ddim_uniform**: DDIM 均匀调度

## 🎯 推荐组合

### 1. 快速高质量 (推荐)
```json
"sampler_name": "dpmpp_2m",
"scheduler": "karras",
"steps": 20,
"cfg": 7.0
```

### 2. 创意探索
```json
"sampler_name": "euler_ancestral",
"scheduler": "normal",
"steps": 25,
"cfg": 7.5
```

### 3. 稳定重现
```json
"sampler_name": "euler",
"scheduler": "normal",
"steps": 25,
"cfg": 7.0
```

### 4. 最高质量
```json
"sampler_name": "dpmpp_2m_sde",
"scheduler": "karras",
"steps": 30,
"cfg": 8.0
```

## 🚀 修复后的工作流

我已经创建了修复版本的工作流：

### 文件: `workflows/sd15_fixed.json`

**改进**:
- ✅ 使用正确的采样器名称: `dpmpp_2m`
- ✅ 使用 Karras 调度器提升质量
- ✅ 优化的步数和 CFG 设置
- ✅ 更好的负面提示词

## 🎨 使用建议

### 根据需求选择采样器:

1. **日常使用**: `dpmpp_2m` + `karras`
2. **快速预览**: `euler` + `normal`
3. **创意探索**: `euler_ancestral` + `normal`
4. **最高质量**: `dpmpp_2m_sde` + `karras`

### 参数调整建议:

- **步数**: 
  - 快速: 15-20 步
  - 平衡: 20-25 步
  - 高质量: 25-35 步

- **CFG**:
  - 低引导: 5-7 (更自由)
  - 标准: 7-9 (平衡)
  - 高引导: 9-12 (严格遵循提示词)

## 🔄 立即使用

1. **导入修复的工作流**:
   ```
   拖拽 workflows/sd15_fixed.json 到 ComfyUI 界面
   ```

2. **验证设置**:
   - 采样器: `dpmpp_2m`
   - 调度器: `karras`
   - 步数: 20

3. **开始生成**:
   点击 "Queue Prompt"

现在工作流应该可以正常运行了！🎉
