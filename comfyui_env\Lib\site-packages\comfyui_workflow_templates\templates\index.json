[{"moduleName": "default", "title": "Basics", "type": "image", "templates": [{"name": "default", "title": "Image Generation", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images from text descriptions."}, {"name": "image2image", "title": "Image to Image", "mediaType": "image", "mediaSubtype": "webp", "description": "Transform existing images using text prompts.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/img2img/"}, {"name": "lora", "title": "<PERSON><PERSON>", "mediaType": "image", "mediaSubtype": "webp", "description": "Apply LoRA models for specialized styles or subjects.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/lora/"}, {"name": "lora_multiple", "title": "<PERSON><PERSON>", "mediaType": "image", "mediaSubtype": "webp", "description": "Combine multiple LoRA models for unique results.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/lora/"}, {"name": "inpaint_example", "title": "Inpaint", "mediaType": "image", "mediaSubtype": "webp", "description": "Edit specific parts of images seamlessly.", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/inpaint/"}, {"name": "inpain_model_outpainting", "title": "Outpaint", "mediaType": "image", "mediaSubtype": "webp", "description": "Extend images beyond their original boundaries.", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/inpaint/#outpainting"}, {"name": "embedding_example", "title": "Embedding", "mediaType": "image", "mediaSubtype": "webp", "description": "Use textual inversion for consistent styles.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/textual_inversion_embeddings/"}, {"name": "gligen_textbox_example", "title": "Gligen Textbox", "mediaType": "image", "mediaSubtype": "webp", "description": "Specify the location and size of objects.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/gligen/"}]}, {"moduleName": "default", "title": "Flux", "type": "image", "templates": [{"name": "flux_dev_checkpoint_example", "title": "Flux Dev", "mediaType": "image", "mediaSubtype": "webp", "description": "Create images using Flux development models.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#flux-dev-1"}, {"name": "flux_schnell", "title": "Flux Schnell", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images quickly with Flux Schnell.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#flux-schnell-1"}, {"name": "flux_fill_inpaint_example", "title": "Flux Inpaint", "mediaType": "image", "mediaSubtype": "webp", "description": "Fill in missing parts of images.", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#fill-inpainting-model"}, {"name": "flux_fill_outpaint_example", "title": "Flux Outpaint", "mediaType": "image", "mediaSubtype": "webp", "description": "Extend images using Flux outpainting.", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#fill-inpainting-model"}, {"name": "flux_canny_model_example", "title": "Flux Canny Model", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images from edge detection.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#canny-and-depth"}, {"name": "flux_depth_lora_example", "title": "Flux Depth Lora", "mediaType": "image", "mediaSubtype": "webp", "description": "Create images with depth-aware LoRA.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#canny-and-depth"}, {"name": "flux_redux_model_example", "title": "Flux Redux Model", "mediaType": "image", "mediaSubtype": "webp", "description": "Transfer style from a reference image to guide image generation with Flux.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/flux/#redux"}]}, {"moduleName": "default", "title": "Image", "type": "image", "templates": [{"name": "hidream_i1_dev", "title": "HiDream I1 Dev", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images with HiDream I1 Dev."}, {"name": "hidream_i1_fast", "title": "HiDream I1 Fast", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images quickly with HiDream I1."}, {"name": "hidream_i1_full", "title": "HiDream I1 Full", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images with HiDream I1."}, {"name": "hidream_e1_full", "title": "HiDream E1 Full", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "description": "Edit images with HiDream E1."}, {"name": "sd3.5_simple_example", "title": "SD3.5 Simple", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images with SD 3.5.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sd3/#sd35"}, {"name": "sd3.5_large_canny_controlnet_example", "title": "SD3.5 Large Canny ControlNet", "mediaType": "image", "mediaSubtype": "webp", "description": "Use edge detection to guide image generation with SD 3.5.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sd3/#sd35-controlnets"}, {"name": "sd3.5_large_depth", "title": "SD3.5 Large Depth", "mediaType": "image", "mediaSubtype": "webp", "description": "Create depth-aware images with SD 3.5.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sd3/#sd35-controlnets"}, {"name": "sd3.5_large_blur", "title": "SD3.5 Large Blur", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images from blurred reference images with SD 3.5.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sd3/#sd35-controlnets"}, {"name": "sdxl_simple_example", "title": "SDXL Simple", "mediaType": "image", "mediaSubtype": "webp", "description": "Create high-quality images with SDXL.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sdxl/"}, {"name": "sdxl_refiner_prompt_example", "title": "SDXL Refiner Prompt", "mediaType": "image", "mediaSubtype": "webp", "description": "Enhance SDXL outputs with refiners.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sdxl/"}, {"name": "sdxl_revision_text_prompts", "title": "SDXL Revision Text Prompts", "mediaType": "image", "mediaSubtype": "webp", "description": "Transfer concepts from reference images to guide image generation with SDXL.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sdxl/#revision"}, {"name": "sdxl_revision_zero_positive", "title": "SDXL Revision Zero Positive", "mediaType": "image", "mediaSubtype": "webp", "description": "Add text prompts alongside reference images to guide image generation with SDXL.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sdxl/#revision"}, {"name": "sdxlturbo_example", "title": "SDXL Turbo", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images in a single step with SDXL Turbo.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/sdturbo/"}, {"name": "image_lotus_depth_v1_1", "title": "Lotus Depth", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "description": "Run Lotus Depth in ComfyUI for zero-shot, efficient monocular depth estimation with high detail retention."}]}, {"moduleName": "default", "title": "Video", "type": "video", "templates": [{"name": "video_wan_vace_14B_t2v", "title": "Wan  VACE Text to Video", "description": "Generate high-quality and coherent videos directly from natural language prompts.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/vace"}, {"name": "video_wan_vace_14B_ref2v", "title": "Wan  VACE Reference to Video", "description": "Extend a reference image into a stylistically consistent and content-coherent video sequence.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/vace"}, {"name": "video_wan_vace_14B_v2v", "title": "Wan  VACE Control  Video", "description": "Create new videos by controlling input videos and reference images", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/vace"}, {"name": "video_wan_vace_outpainting", "title": "Wan VACE Outpainting", "description": "Use the video extension capabilities of Wan VACE to expand the video size.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/vace"}, {"name": "video_wan_vace_flf2v", "title": "Wan VACE FLF2V", "description": "Generate videos through controlling the first and last frames.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/vace"}, {"name": "text_to_video_wan", "title": "Wan 2.1 Text to Video", "description": "Quickly Generate videos from text descriptions.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/wan/#text-to-video"}, {"name": "image_to_video_wan", "title": "Wan 2.1 Image to Video", "description": "Quickly Generate videos from images.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/wan/#image-to-video"}, {"name": "wan2.1_fun_inp", "title": "Wan 2.1 Inpainting", "description": "Create videos from start and end frames.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/fun-inp"}, {"name": "wan2.1_fun_control", "title": "Wan 2.1 ControlNet", "description": "Guide video generation with pose, depth, edge controls and more.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/fun-control"}, {"name": "wan2.1_flf2v_720_f16", "title": "Wan 2.1 FLF2V 720p F16", "description": "Generate video through controlling the first and last frames.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/video/wan/wan-flf"}, {"name": "ltxv_text_to_video", "title": "LTXV Text to Video", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate videos from text descriptions.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/ltxv/#text-to-video"}, {"name": "ltxv_image_to_video", "title": "LTXV Image to Video", "mediaType": "image", "mediaSubtype": "webp", "description": "Convert still images into videos.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/ltxv/#image-to-video"}, {"name": "mochi_text_to_video_example", "title": "Mochi Text to Video", "mediaType": "image", "mediaSubtype": "webp", "description": "Create videos with <PERSON><PERSON> model.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/mochi/"}, {"name": "hunyuan_video_text_to_video", "title": "Hunyuan Video Text to Video", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate videos using Hunyuan model.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/hunyuan_video/"}, {"name": "image_to_video", "title": "SVD Image to Video", "mediaType": "image", "mediaSubtype": "webp", "description": "Transform images into animated videos.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/video/#image-to-video"}, {"name": "txt_to_image_to_video", "title": "SVD Text to Image to Video", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images from text and then convert them into videos.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/video/#image-to-video"}]}, {"moduleName": "default", "title": "Image API", "type": "image", "templates": [{"name": "api_bfl_flux_1_kontext_pro_image", "title": "BFL Flux.1 Kontext Pro", "description": "Edit images with Flux.1 Kontext pro image.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider"}, {"name": "api_bfl_flux_1_kontext_max_image", "title": "BFL Flux.1 Kontext Max", "description": "Edit images with Flux.1 Kontext max image.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider"}, {"name": "api_bfl_flux_pro_t2i", "title": "BFL Flux[Pro]: Text to Image", "description": "Create images with FLUX.1 [pro]'s excellent prompt following, visual quality, image detail and output diversity.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_luma_photon_i2i", "title": "Luma Photon: Image to Image", "description": "Guide image generation using a combination of images and prompt.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider"}, {"name": "api_luma_photon_style_ref", "title": "Luma Photon: Style Reference", "description": "Apply and blend style references with exact control. Luma Photon captures the essence of each reference image, letting you combine distinct visual elements while maintaining professional quality.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider"}, {"name": "api_recraft_image_gen_with_color_control", "title": "Recraft: Color Control Image Generation", "description": "Create a custom palette to reuse for multiple images or hand-pick colors for each photo. Match your brand's color palette and craft visuals that are distinctly yours.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_recraft_image_gen_with_style_control", "title": "Recraft: Style Control Image Generation", "description": "Control style with visual examples, align positioning, and fine-tune objects. Store and share styles for perfect brand consistency.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_recraft_vector_gen", "title": "Recraft: Vector Generation", "description": "Go from a text prompt to vector image with Recraft's AI vector generator. Produce the best-quality vector art for logos, posters, icon sets, ads, banners and mockups. Perfect your designs with sharp, high-quality SVG files. Create branded vector illustrations for your app or website in seconds.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_runway_text_to_image", "title": "Runway: Text to Image", "description": "Transform text prompts into high-quality images using Runway's cutting-edge AI model.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_runway_reference_to_image", "title": "Runway: Reference to Image", "description": "Generate new images based on reference styles and compositions with Runway's AI.", "mediaType": "image", "thumbnailVariant": "compareSlider", "mediaSubtype": "webp"}, {"name": "api_stability_ai_stable_image_ultra_t2i", "title": "Stability AI: Stable Image Ultra Text to Image", "description": "Generate high quality images with excellent prompt adherence. Perfect for professional use cases at 1 megapixel resolution.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_stability_ai_i2i", "title": "Stability AI: Image to Image", "description": "Transform your images with high-quality image-to-image generation. Perfect for professional image editing and style transfer.", "mediaType": "image", "thumbnailVariant": "compareSlider", "mediaSubtype": "webp"}, {"name": "api_stability_ai_sd3.5_t2i", "title": "Stability AI: SD3.5 Text to Image", "description": "Generate high quality images with excellent prompt adherence. Perfect for professional use cases at 1 megapixel resolution.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_stability_ai_sd3.5_i2i", "title": "Stability AI: SD3.5 Image to Image", "description": "Generate high quality images with excellent prompt adherence. Perfect for professional use cases at 1 megapixel resolution.", "mediaType": "image", "thumbnailVariant": "compareSlider", "mediaSubtype": "webp"}, {"name": "api_ideogram_v3_t2i", "title": "Ideogram V3: Text to Image", "description": "Generate images with high-quality image-prompt alignment, photorealism, and text rendering. Create professional-quality logos, promotional posters, landing page concepts, product photography, and more. Effortlessly craft sophisticated spatial compositions with intricate backgrounds, precise and nuanced lighting and colors, and lifelike environmental detail.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_openai_image_1_t2i", "title": "OpenAI: GPT-Image-1 Text to Image", "description": "Use GPT Image 1 API to generate images from text descriptions.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/api-nodes/openai/gpt-image-1"}, {"name": "api_openai_image_1_i2i", "title": "OpenAI: GPT-Image-1 Image to Image", "description": "Use GPT Image 1 API to generate images from images.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://docs.comfy.org/tutorials/api-nodes/openai/gpt-image-1"}, {"name": "api_openai_image_1_inpaint", "title": "OpenAI: GPT-Image-1 Inpaint", "description": "Use GPT Image 1 API to inpaint images.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://docs.comfy.org/tutorials/api-nodes/openai/gpt-image-1"}, {"name": "api_openai_image_1_multi_inputs", "title": "OpenAI: GPT-Image-1 Multi Inputs", "description": "Use GPT Image 1 API with multiple inputs to generate images.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://docs.comfy.org/tutorials/api-nodes/openai/gpt-image-1"}, {"name": "api_openai_dall_e_2_t2i", "title": "OpenAI: Dall-E 2 Text to Image", "description": "Use Dall-E 2 API to generate images from text descriptions.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/api-nodes/openai/dall-e-2"}, {"name": "api_openai_dall_e_2_inpaint", "title": "OpenAI: Dall-E 2 Inpaint", "description": "Use Dall-E 2 API to inpaint images.", "mediaType": "image", "mediaSubtype": "webp", "thumbnailVariant": "compareSlider", "tutorialUrl": "https://docs.comfy.org/tutorials/api-nodes/openai/dall-e-2"}, {"name": "api_openai_dall_e_3_t2i", "title": "OpenAI: Dall-E 3 Text to Image", "description": "Use Dall-E 3 API to generate images from text descriptions.", "mediaType": "image", "mediaSubtype": "webp", "tutorialUrl": "https://docs.comfy.org/tutorials/api-nodes/openai/dall-e-3"}]}, {"moduleName": "default", "title": "Video API", "type": "video", "templates": [{"name": "api_kling_i2v", "title": "Kling: Image to Video", "description": "Create videos with great prompt adherence for actions, expressions, and camera movements. Now supporting complex prompts with sequential actions for you to be the director of your scene.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_kling_effects", "title": "Kling: Video Effects", "description": "Apply stunning visual effects to your images and transform them into dynamic videos. Choose from a variety of effects.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_kling_flf", "title": "Kling: FLF2V", "description": "Generate videos through controlling the first and last frames.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_luma_i2v", "title": "Luma: Image to Video", "description": "Take static images and instantly create magical high quality animations.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_luma_t2v", "title": "Luma: Text to Video", "description": "High-quality videos can be generated using simple prompts.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_hailuo_minimax_t2v", "title": "MiniMax: Text to Video", "description": "Generate high-quality videos directly from text prompts. Explore MiniMax's advanced AI capabilities to create diverse visual narratives with professional CGI effects and stylistic elements to bring your descriptions to life.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_hailuo_minimax_i2v", "title": "MiniMax: Image to Video", "description": "Create refined videos from images and text, including CGI integration and trendy photo effects like viral AI hugging. Choose from a variety of video styles and themes to match your creative vision.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_pixverse_i2v", "title": "PixVerse: Image to Video", "description": "Transforms static images into dynamic videos with motion and effects.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_pixverse_template_i2v", "title": "PixVerse Templates: Image to Video", "description": "Transforms static images into dynamic videos with motion and effects.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_pixverse_t2v", "title": "PixVerse: Text to Video", "description": "Generate videos with accurate prompt interpretation and stunning video dynamics.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_runway_gen3a_turbo_image_to_video", "title": "Runway: Gen3a Turbo Image to Video", "description": "Create cinematic videos from static images with Runway's Gen3a Turbo speed.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_runway_gen4_turo_image_to_video", "title": "Runway: Gen4 Turbo Image to Video", "description": "Transform images into dynamic videos using Runway's latest Gen4 technology.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_runway_first_last_frame", "title": "Runway: First Last Frame to Video", "description": "Generate smooth video transitions between two keyframes with Runway's precision.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_pika_i2v", "title": "Pika: Image to Video", "description": "Transform a single static image into a smooth, animated video. Leverage <PERSON>'s AI technology to bring natural motion and life to your images.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_pika_scene", "title": "Pika Scenes: Images to Video", "description": "Use multiple images as ingredients and generate videos that incorporate all of them.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_veo2_i2v", "title": "Veo2: Image to Video", "description": "Use Google Veo2 API to generate videos from images.", "mediaType": "image", "mediaSubtype": "webp"}]}, {"moduleName": "default", "title": "3D API", "type": "image", "templates": [{"name": "api_rodin_image_to_model", "title": "Rodin: Image to Model", "description": "Transform single photos into detailed 3D sculptures with <PERSON><PERSON>'s artistic AI.", "mediaType": "image", "thumbnailVariant": "compareSlider", "mediaSubtype": "webp"}, {"name": "api_rodin_multiview_to_model", "title": "Rodin: Multiview to Model", "description": "<PERSON>ulpt comprehensive 3D models using <PERSON><PERSON>'s multi-angle reconstruction.", "mediaType": "image", "thumbnailVariant": "compareSlider", "mediaSubtype": "webp"}, {"name": "api_tripo_text_to_model", "title": "Tripo: Text to Model", "description": "Craft 3D objects from descriptions with <PERSON><PERSON>'s text-driven modeling.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_tripo_image_to_model", "title": "Tripo: Image to Model", "description": "Convert 2D images into professional 3D assets using Tripo's engine.", "mediaType": "image", "thumbnailVariant": "compareSlider", "mediaSubtype": "webp"}, {"name": "api_tripo_multiview_to_model", "title": "Tripo: Multiview to Model", "description": "Build 3D models from multiple angles with Tripo's advanced scanner.", "mediaType": "image", "thumbnailVariant": "compareSlider", "mediaSubtype": "webp"}]}, {"moduleName": "default", "title": "LLM API", "type": "image", "templates": [{"name": "api_openai_chat", "title": "OpenAI: <PERSON><PERSON>", "description": "Engage with OpenAI's advanced language models for intelligent conversations.", "mediaType": "image", "mediaSubtype": "webp"}, {"name": "api_google_gemini", "title": "Google Gemini: <PERSON><PERSON>", "description": "Experience Google's multimodal AI with Gemini's reasoning capabilities.", "mediaType": "image", "mediaSubtype": "webp"}]}, {"moduleName": "default", "title": "Upscaling", "type": "image", "templates": [{"name": "hiresfix_latent_workflow", "title": "Upscale", "mediaType": "image", "mediaSubtype": "webp", "description": "Enhance image quality in latent space.", "thumbnailVariant": "zoomHover", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/2_pass_txt2img/"}, {"name": "esrgan_example", "title": "ESRGAN", "mediaType": "image", "mediaSubtype": "webp", "description": "Use upscale models to enhance image quality.", "thumbnailVariant": "zoomHover", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/upscale_models/"}, {"name": "hiresfix_esrgan_workflow", "title": "HiresFix ESRGAN Workflow", "mediaType": "image", "mediaSubtype": "webp", "description": "Use upscale models during intermediate steps.", "thumbnailVariant": "zoomHover", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/2_pass_txt2img/#non-latent-upscaling"}, {"name": "latent_upscale_different_prompt_model", "title": "Latent Upscale Different Prompt Model", "mediaType": "image", "mediaSubtype": "webp", "description": "Upscale and change prompt across passes.", "thumbnailVariant": "zoomHover", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/2_pass_txt2img/#more-examples"}]}, {"moduleName": "default", "title": "ControlNet", "type": "image", "templates": [{"name": "controlnet_example", "title": "Scribble ControlNet", "mediaType": "image", "mediaSubtype": "webp", "description": "Control image generation with reference images.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/controlnet/"}, {"name": "2_pass_pose_worship", "title": "Pose ControlNet 2 Pass", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate images from pose references.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/controlnet/#pose-controlnet"}, {"name": "depth_controlnet", "title": "Depth ControlNet", "mediaType": "image", "mediaSubtype": "webp", "description": "Create images with depth-aware generation.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/controlnet/#t2i-adapter-vs-controlnets"}, {"name": "depth_t2i_adapter", "title": "Depth T2I Adapter", "mediaType": "image", "mediaSubtype": "webp", "description": "Quickly generate depth-aware images with a T2I adapter.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/controlnet/#t2i-adapter-vs-controlnets"}, {"name": "mixing_controlnets", "title": "Mixing ControlNets", "mediaType": "image", "mediaSubtype": "webp", "description": "Combine multiple ControlNet models together.", "thumbnailVariant": "hoverDissolve", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/controlnet/#mixing-controlnets"}]}, {"moduleName": "default", "title": "Area Composition", "type": "image", "templates": [{"name": "area_composition", "title": "Area Composition", "mediaType": "image", "mediaSubtype": "webp", "description": "Control image composition with areas.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/area_composition/"}, {"name": "area_composition_reversed", "title": "Area Composition Reversed", "mediaType": "image", "mediaSubtype": "webp", "description": "Reverse area composition workflow.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/area_composition/"}, {"name": "area_composition_square_area_for_subject", "title": "Area Composition Square Area for Subject", "mediaType": "image", "mediaSubtype": "webp", "description": "Create consistent subject placement.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/area_composition/#increasing-consistency-of-images-with-area-composition"}]}, {"moduleName": "default", "title": "3D", "type": "3d", "templates": [{"name": "3d_hunyuan3d_image_to_model", "title": "Hunyuan3D 2.0", "mediaType": "image", "mediaSubtype": "webp", "description": "Use Hunyuan3D 2.0 to generate models from a single view.", "tutorialUrl": ""}, {"name": "3d_hunyuan3d_multiview_to_model", "title": "Hunyuan3D 2.0 MV", "mediaType": "image", "mediaSubtype": "webp", "description": " Use Hunyuan3D 2mv to generate models from multiple views.", "tutorialUrl": "", "thumbnailVariant": "compareSlider"}, {"name": "3d_hunyuan3d_multiview_to_model_turbo", "title": "Hunyuan3D 2.0 MV Turbo", "mediaType": "image", "mediaSubtype": "webp", "description": "Use Hunyuan3D 2mv turbo to generate models from multiple views.", "tutorialUrl": "", "thumbnailVariant": "compareSlider"}, {"name": "stable_zero123_example", "title": "Stable Zero123", "mediaType": "image", "mediaSubtype": "webp", "description": "Generate 3D views from single images.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/3d/"}]}, {"moduleName": "default", "title": "Audio", "type": "audio", "templates": [{"name": "audio_stable_audio_example", "title": "Stable Audio", "mediaType": "audio", "mediaSubtype": "mp3", "description": "Generate audio from text descriptions.", "tutorialUrl": "https://comfyanonymous.github.io/ComfyUI_examples/audio/"}, {"name": "audio_ace_step_1_t2a_instrumentals", "title": "ACE-Step v1 Text to Instrumentals Music", "mediaType": "audio", "mediaSubtype": "mp3", "description": "Input text/lyrics to generate Instrumentals Music.", "tutorialUrl": "https://docs.comfy.org/tutorials/audio/ace-step/ace-step-v1"}, {"name": "audio_ace_step_1_t2a_song", "title": "ACE Step v1 Text to Song", "mediaType": "audio", "mediaSubtype": "mp3", "description": "Input text/lyrics to generate song with human vocals, supporting multilingual & style customization.", "tutorialUrl": "https://docs.comfy.org/tutorials/audio/ace-step/ace-step-v1"}, {"name": "audio_ace_step_1_m2m_editing", "title": "ACE Step v1 M2M Editing", "mediaType": "audio", "mediaSubtype": "mp3", "description": "Use M2M to edit existing song, change the style, lyrics, etc.", "tutorialUrl": "https://docs.comfy.org/tutorials/audio/ace-step/ace-step-v1"}]}]