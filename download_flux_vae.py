#!/usr/bin/env python3
"""
下载 Flux VAE 模型
"""

import os
import requests
from pathlib import Path
import sys

def download_file_with_progress(url, filename):
    """下载文件并显示进度"""
    print(f"正在下载: {filename}")
    print(f"下载地址: {url}")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        mb_downloaded = downloaded / (1024 * 1024)
                        mb_total = total_size / (1024 * 1024)
                        print(f"\r进度: {percent:.1f}% ({mb_downloaded:.1f}MB/{mb_total:.1f}MB)", end='')
        
        print(f"\n✅ 下载完成: {filename}")
        return True
        
    except Exception as e:
        print(f"\n❌ 下载失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Flux VAE 下载器")
    print("=" * 50)
    
    # 确保目录存在
    vae_dir = Path("models/vae")
    vae_dir.mkdir(parents=True, exist_ok=True)
    
    # Flux VAE 信息
    vae_info = {
        "name": "Flux VAE (ae.safetensors)",
        "filename": "ae.safetensors",
        "url": "https://huggingface.co/black-forest-labs/FLUX.1-dev/resolve/main/ae.safetensors",
        "size": "约 335MB"
    }
    
    filepath = vae_dir / vae_info["filename"]
    
    print(f"模型: {vae_info['name']}")
    print(f"大小: {vae_info['size']}")
    print(f"保存位置: {filepath}")
    print()
    
    # 检查文件是否已存在
    if filepath.exists():
        print("⚠️  文件已存在!")
        file_size = filepath.stat().st_size / (1024 * 1024)
        print(f"当前文件大小: {file_size:.1f}MB")
        
        overwrite = input("是否重新下载? (y/N): ").strip().lower()
        if overwrite != 'y':
            print("取消下载")
            return
    
    # 开始下载
    print("开始下载 Flux VAE...")
    success = download_file_with_progress(vae_info["url"], filepath)
    
    if success:
        print("\n🎉 Flux VAE 下载完成!")
        print("现在您可以使用 Flux 工作流了。")
        print("\n使用方法:")
        print("1. 拖拽 workflows/flux_basic.json 到 ComfyUI 界面")
        print("2. 修改提示词")
        print("3. 点击 'Queue Prompt' 生成图像")
    else:
        print("\n❌ 下载失败，请检查网络连接或手动下载")
        print(f"手动下载地址: {vae_info['url']}")
        print(f"保存位置: {filepath}")

if __name__ == "__main__":
    main()
