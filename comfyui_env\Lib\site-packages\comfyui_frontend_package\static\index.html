<!doctype html><html lang="en"><head><meta charset="UTF-8"><title>ComfyUI</title><meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"><link rel="stylesheet" href="user.css"/><link rel="stylesheet" href="materialdesignicons.min.css"/><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="black"><link rel="manifest" href="./assets/manifest-CebUEmtR.json"><script type="importmap">{
  "imports": {
    "primevue": "./assets/vendor-primevue-CBB09Bln.js",
    "vue": "./assets/vendor-vue-B7YUw5vA.js",
    "vue-i18n": "./assets/vendor-vue-i18n-CdFxvEOa.js"
  }
}</script><script type="module" crossorigin src="./assets/index-DhLPvT6M.js"></script><link rel="modulepreload" crossorigin href="./assets/vendor-vue-B7YUw5vA.js"><link rel="modulepreload" crossorigin href="./assets/vendor-primevue-CBB09Bln.js"><link rel="modulepreload" crossorigin href="./assets/vendor-vue-i18n-CdFxvEOa.js"><link rel="stylesheet" crossorigin href="./assets/index-FCuhzJpH.css"></head><body class="litegraph grid"><div id="vue-app"></div></body></html>